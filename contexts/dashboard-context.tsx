"use client";

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import Cookies from 'js-cookie';
import { useTranslation } from 'react-i18next';
import {
  School,
  Users,
  BarChart3,
  User,
  CreditCard,
  MapPin,
  MessageSquare
} from 'lucide-react';
import { TurkishLiraIcon } from '@/components/ui/turkish-lira-icon';

export interface DashboardCardConfig {
  id: string;
  title: string;
  description: string;
  icon: any; // LucideIcon type
  href: string;
  bgColor: string;
  textColor: string;
}

interface DashboardState {
  selectedCards: string[];
  isLoading: boolean;
  version: number; // Force re-render version
}

type DashboardAction = 
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_SELECTED_CARDS'; payload: string[] }
  | { type: 'RESET_TO_DEFAULT' }
  | { type: 'FORCE_UPDATE' };

const dashboardReducer = (state: DashboardState, action: DashboardAction): DashboardState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_SELECTED_CARDS':
      console.log('🔄 DashboardContext: Setting selected cards:', action.payload);
      return { 
        ...state, 
        selectedCards: action.payload,
        version: state.version + 1 // Increment version to force re-render
      };
    case 'RESET_TO_DEFAULT':
      console.log('🔄 DashboardContext: Resetting to default');
      return { 
        ...state, 
        selectedCards: ['athletes', 'payments', 'expenses', 'sms'],
        version: state.version + 1
      };
    case 'FORCE_UPDATE':
      console.log('🔄 DashboardContext: Force update');
      return { ...state, version: state.version + 1 };
    default:
      return state;
  }
};

interface DashboardContextType {
  state: DashboardState;
  setSelectedCards: (cards: string[]) => void;
  resetToDefault: () => void;
  forceUpdate: () => void;
  getSelectedCards: () => DashboardCardConfig[];
  availableCards: DashboardCardConfig[];
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

const COOKIE_NAME = 'dashboard-preferences';
const DEFAULT_CARDS = ['athletes', 'payments', 'expenses', 'sms'];

export function DashboardProvider({ children }: { children: ReactNode }) {
  const { t } = useTranslation(['shared', 'dashboard']);
  
  const [state, dispatch] = useReducer(dashboardReducer, {
    selectedCards: DEFAULT_CARDS,
    isLoading: true,
    version: 0
  });

  // All available cards configuration with translations
  const availableCards: DashboardCardConfig[] = [
    {
      id: 'schools',
      title: t("nav.schools", { ns: 'shared' }),
      description: t("dashboard.cards.schools", { ns: 'dashboard' }),
      icon: School,
      href: '/schools',
      bgColor: 'bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/50 dark:to-blue-950/50 border border-blue-200 dark:border-blue-800',
      textColor: 'text-blue-900 dark:text-blue-100'
    },
    {
      id: 'instructors',
      title: t("nav.instructors", { ns: 'shared' }),
      description: t("dashboard.cards.instructors", { ns: 'dashboard' }),
      icon: Users,
      href: '/instructors',
      bgColor: 'bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/50 dark:to-green-950/50 border border-green-200 dark:border-green-800',
      textColor: 'text-green-900 dark:text-green-100'
    },
    {
      id: 'teams',
      title: t("nav.teams", { ns: 'shared' }),
      description: t("dashboard.cards.teams", { ns: 'dashboard' }),
      icon: BarChart3,
      href: '/teams',
      bgColor: 'bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/50 dark:to-purple-950/50 border border-purple-200 dark:border-purple-800',
      textColor: 'text-purple-900 dark:text-purple-100'
    },
    {
      id: 'athletes',
      title: t("nav.athletes", { ns: 'shared' }),
      description: t("dashboard.cards.athletes", { ns: 'dashboard' }),
      icon: User,
      href: '/athletes',
      bgColor: 'bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/50 dark:to-orange-950/50 border border-orange-200 dark:border-orange-800',
      textColor: 'text-orange-900 dark:text-orange-100'
    },
    {
      id: 'payments',
      title: t("nav.payments", { ns: 'shared' }),
      description: t("dashboard.cards.payments", { ns: 'dashboard' }),
      icon: CreditCard,
      href: '/payments',
      bgColor: 'bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/50 dark:to-emerald-950/50 border border-emerald-200 dark:border-emerald-800',
      textColor: 'text-emerald-900 dark:text-emerald-100'
    },
    {
      id: 'expenses',
      title: t("nav.expenses", { ns: 'shared' }),
      description: t("dashboard.cards.expenses", { ns: 'dashboard' }),
      icon: TurkishLiraIcon,
      href: '/expenses',
      bgColor: 'bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/50 dark:to-red-950/50 border border-red-200 dark:border-red-800',
      textColor: 'text-red-900 dark:text-red-100'
    },
    {
      id: 'facilities',
      title: t("nav.facilities", { ns: 'shared' }),
      description: t("dashboard.cards.facilities", { ns: 'dashboard' }),
      icon: MapPin,
      href: '/facilities',
      bgColor: 'bg-gradient-to-br from-teal-50 to-teal-100 dark:from-teal-900/50 dark:to-teal-950/50 border border-teal-200 dark:border-teal-800',
      textColor: 'text-teal-900 dark:text-teal-100'
    },
    {
      id: 'sms',
      title: t("nav.sms", { ns: 'shared' }),
      description: t("dashboard.cards.sms", { ns: 'dashboard' }),
      icon: MessageSquare,
      href: '/sms',
      bgColor: 'bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/50 dark:to-indigo-950/50 border border-indigo-200 dark:border-indigo-800',
      textColor: 'text-indigo-900 dark:text-indigo-100'
    }
  ];

  // Load saved preferences on mount
  useEffect(() => {
    console.log('🚀 DashboardContext: Loading preferences from cookies');
    try {
      const saved = Cookies.get(COOKIE_NAME);
      if (saved) {
        const parsed = JSON.parse(saved);
        console.log('📂 DashboardContext: Found saved preferences:', parsed);
        dispatch({ type: 'SET_SELECTED_CARDS', payload: parsed });
      }
    } catch (error) {
      console.error('❌ DashboardContext: Error loading preferences:', error);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  const setSelectedCards = (cards: string[]) => {
    console.log('💾 DashboardContext: Saving cards to cookie:', cards);
    try {
      Cookies.set(COOKIE_NAME, JSON.stringify(cards), { expires: 365 });
      dispatch({ type: 'SET_SELECTED_CARDS', payload: cards });
      console.log('✅ DashboardContext: Cards saved and state updated');
    } catch (error) {
      console.error('❌ DashboardContext: Error saving preferences:', error);
    }
  };

  const resetToDefault = () => {
    console.log('🔄 DashboardContext: Resetting to default and deleting cookie');
    try {
      Cookies.remove(COOKIE_NAME);
      dispatch({ type: 'RESET_TO_DEFAULT' });
      console.log('✅ DashboardContext: Reset completed');
    } catch (error) {
      console.error('❌ DashboardContext: Error resetting:', error);
    }
  };

  const forceUpdate = () => {
    dispatch({ type: 'FORCE_UPDATE' });
  };

  const getSelectedCards = (): DashboardCardConfig[] => {
    const selected = availableCards.filter(card => state.selectedCards.includes(card.id));
    console.log('📋 DashboardContext: Getting selected cards:', selected.map(c => c.id));
    return selected;
  };

  const contextValue: DashboardContextType = {
    state,
    setSelectedCards,
    resetToDefault,
    forceUpdate,
    getSelectedCards,
    availableCards
  };

  return (
    <DashboardContext.Provider value={contextValue}>
      {children}
    </DashboardContext.Provider>
  );
}

export function useDashboardContext() {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboardContext must be used within a DashboardProvider');
  }
  return context;
}