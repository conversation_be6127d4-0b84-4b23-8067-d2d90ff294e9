'use client';

import { getSmsBalance } from '@/lib/actions/sms';
import React, { createContext, useContext, useState, useCallback, useRef, ReactNode } from 'react';

// Global singleton to prevent multiple balance fetches across all SMS context instances
let globalBalancePromise: Promise<number> | null = null;
let globalBalance: number | null = null;

interface SmsContextType {
  // Balance management
  balance: number | null;
  setBalance: (balance: number) => void;
  refreshBalance: () => Promise<void>;
  ensureBalanceInitialized: () => Promise<void>;

  // SMS logs refresh
  refreshLogs: () => void;
  logsRefreshTrigger: number;

  // General refresh for components
  refreshData: () => void;
  dataRefreshTrigger: number;
}

const SmsContext = createContext<SmsContextType | undefined>(undefined);

interface SmsProviderProps {
  children: ReactNode;
  initialBalance?: number | null;
}

export function SmsProvider({ children, initialBalance = null }: SmsProviderProps) {
  // Initialize with global balance if available, otherwise use initialBalance
  const [balance, setBalance] = useState<number | null>(globalBalance ?? initialBalance);
  const [logsRefreshTrigger, setLogsRefreshTrigger] = useState(0);
  const [dataRefreshTrigger, setDataRefreshTrigger] = useState(0);
  const hasInitializedBalance = useRef(globalBalance !== null || initialBalance !== null);

  // Debug logging
  const instanceId = useRef(Math.random().toString(36).substr(2, 9));
  console.log(`🏗️ SmsProvider instance ${instanceId.current} created at ${new Date().toISOString()}`);
  console.log(`🏗️ SmsProvider: globalBalance=${globalBalance}, initialBalance=${initialBalance}`);

  const fetchBalanceOnce = useCallback(async (): Promise<number> => {
    // If we already have a global balance, return it
    if (globalBalance !== null) {
      return globalBalance;
    }

    // If there's already a fetch in progress, wait for it
    if (globalBalancePromise) {
      return await globalBalancePromise;
    }

    // Start a new fetch
    globalBalancePromise = (async () => {
      try {
        const result = await getSmsBalance();
        let newBalance: number;
        if (result && typeof result.balance === 'number') {
          newBalance = result.balance;
        } else {
          newBalance = 0;
        }
        globalBalance = newBalance;
        return newBalance;
      } catch (error) {
        console.error('Failed to fetch SMS balance:', error);
        globalBalance = 0;
        return 0;
      } finally {
        globalBalancePromise = null;
      }
    })();

    return await globalBalancePromise;
  }, []);

  const refreshBalance = useCallback(async () => {
    // Force refresh by clearing global state
    globalBalance = null;
    globalBalancePromise = null;

    const newBalance = await fetchBalanceOnce();
    setBalance(newBalance);
    hasInitializedBalance.current = true;
  }, [fetchBalanceOnce]);

  // Lazy initialization - only fetch balance when it's actually needed
  const ensureBalanceInitialized = useCallback(async () => {
    if (!hasInitializedBalance.current) {
      const newBalance = await fetchBalanceOnce();
      setBalance(newBalance);
      hasInitializedBalance.current = true;
    }
  }, [fetchBalanceOnce]);

  const refreshLogs = useCallback(() => {
    setLogsRefreshTrigger(prev => prev + 1);
  }, []);

  const refreshData = useCallback(() => {
    setDataRefreshTrigger(prev => prev + 1);
    refreshLogs();
  }, [refreshLogs]);

  // Enhanced setBalance that marks balance as initialized
  const enhancedSetBalance = useCallback((newBalance: number) => {
    setBalance(newBalance);
    hasInitializedBalance.current = true;
  }, []);

  const value: SmsContextType = {
    balance,
    setBalance: enhancedSetBalance,
    refreshBalance,
    ensureBalanceInitialized,
    refreshLogs,
    logsRefreshTrigger,
    refreshData,
    dataRefreshTrigger,
  };

  return (
    <SmsContext.Provider value={value}>
      {children}
    </SmsContext.Provider>
  );
}

export function useSms() {
  const context = useContext(SmsContext);
  if (context === undefined) {
    throw new Error('useSms must be used within a SmsProvider');
  }
  return context;
}

// Hook for components that need to trigger refresh after SMS operations
export function useSmsRefresh() {
  const { refreshData, refreshBalance, refreshLogs } = useSms();

  return {
    // Call this after successful SMS sending
    onSmsSuccess: useCallback(() => {
      refreshData();
    }, [refreshData]),

    // Call this to refresh only balance
    refreshBalance,

    // Call this to refresh only logs
    refreshLogs,
  };
}

// Safe version of SMS refresh hook that doesn't throw error when used outside SMS context
export function useSmsRefreshSafe() {
  const context = useContext(SmsContext);

  if (context === undefined) {
    // Return a no-op function when SMS context is not available
    return {
      onSmsSuccess: () => {}
    };
  }

  const { refreshData, refreshBalance, refreshLogs } = context;

  return {
    // Call this after successful SMS sending
    onSmsSuccess: useCallback(() => {
      refreshData();
    }, [refreshData]),

    // Call this to refresh only balance
    refreshBalance,

    // Call this to refresh only logs
    refreshLogs,
  };
}
