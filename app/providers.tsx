'use client';

import { ThemeProvider } from '@/components/layout/theme-provider';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/lib/i18n';
import { useEffect, useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { SessionProvider } from 'next-auth/react';
import { TokenManager } from '@/components/layout/token-manager';
import { Toaster } from '@/components/ui/toaster';

export function Providers({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        refetchOnWindowFocus: false,
        staleTime: 5 * 60 * 1000, // 5 minutes
      },
    },
  }));
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    
    // Ensure i18n is initialized before using
    if (i18n.isInitialized) {
      // Initialize language from cookie on mount
      const cookie = document.cookie.split(';').find(c => c.trim().startsWith('NEXT_LOCALE='));
      if (cookie) {
        const lang = cookie.split('=')[1];
        i18n.changeLanguage(lang);
      }
    } else {
      // Wait for i18n to be ready
      i18n.on('initialized', () => {
        const cookie = document.cookie.split(';').find(c => c.trim().startsWith('NEXT_LOCALE='));
        if (cookie) {
          const lang = cookie.split('=')[1];
          i18n.changeLanguage(lang);
        }
      });
    }
  }, []);

  // Prevent hydration mismatch by only rendering i18n provider on client
  if (!isClient) {
    return (
      <SessionProvider>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          {children}
          <Toaster />
        </ThemeProvider>
      </SessionProvider>
    );
  }

  return (
    <SessionProvider>
      <TokenManager />
      <I18nextProvider i18n={i18n}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <QueryClientProvider client={queryClient}>
            {children}
            <Toaster />
          </QueryClientProvider>
        </ThemeProvider>
      </I18nextProvider>
    </SessionProvider>
  );
}