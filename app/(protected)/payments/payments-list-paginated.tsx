"use client";

import { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { PlusCircle, FileText, CreditCard, Filter } from "lucide-react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { createPaymentColumns } from "@/components/payments-table-columns";
import { PaymentTransactionsListPaginated } from "@/components/payment-transactions-list-paginated";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { Label } from "@/components/ui/label";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Payment, PaymentPlan } from "@/lib/types";
import { GenericListPage } from "@/components/ui/generic-list-page";
import { PaginationData, SearchParams } from "@/hooks/use-paginated-list";

interface PaymentsListPaginatedProps {
  initialData: Payment[];
  initialPagination: PaginationData;
  plans: PaymentPlan[];
  transactionData?: any[];
  transactionPagination?: PaginationData;
  athletes?: any[];
  initialSearchParams: SearchParams & {
    tab?: string;
    status?: string;
    type?: string;
    fromDate?: string;
    toDate?: string;
    transactionMethod?: string;
    athleteId?: string;
    paymentId?: string;
  };
}

export function PaymentsListPaginated({
  initialData,
  initialPagination,
  plans,
  transactionData,
  transactionPagination,
  athletes,
  initialSearchParams
}: PaymentsListPaginatedProps) {
  const { t } = useSafeTranslation();
  const router = useRouter();

  const paymentColumns = useMemo(() => createPaymentColumns(t), [t]);

  // Tab handling
  const activeTab = initialSearchParams.tab || 'payments';

  const handleTabChange = (value: string) => {
    const params = new URLSearchParams();
    params.set('tab', value);
    router.push(`/payments?${params.toString()}`);
  };

  // Local state for filters
  const [filters, setFilters] = useState({
    status: initialSearchParams.status || "",
    type: initialSearchParams.type || "",
    fromDate: initialSearchParams.fromDate || "",
    toDate: initialSearchParams.toDate || "",
  });
  const [showFilters, setShowFilters] = useState(
    !!(initialSearchParams.status || initialSearchParams.type ||
       initialSearchParams.fromDate || initialSearchParams.toDate)
  );

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    const searchParams = new URLSearchParams(window.location.search);

    // Update search params with filter values
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== 'all' && value.trim() !== '') {
        searchParams.set(key, value);
      } else {
        searchParams.delete(key);
      }
    });

    // Reset to page 1 when applying filters
    searchParams.set('page', '1');

    // Use router.push for smooth navigation without page refresh
    router.push(`/payments?${searchParams.toString()}`);
  };

  const handleClearFilters = () => {
    // Reset all filter states
    setFilters({
      status: "",
      type: "",
      fromDate: "",
      toDate: "",
    });

    // Clear URL parameters
    const searchParams = new URLSearchParams(window.location.search);

    // Remove filter parameters but keep search and pagination
    ['status', 'type', 'fromDate', 'toDate'].forEach(key => {
      searchParams.delete(key);
    });

    // Reset to page 1
    searchParams.set('page', '1');

    // Use router.push for smooth navigation without page refresh
    router.push(`/payments?${searchParams.toString()}`);
  };

  // Create filters component with the same pattern as other pages
  const filtersComponent = showFilters ? (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">{t('common.actions.filter')}</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(false)}
            >
              {t('common.actions.cancel')}
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="statusFilter">{t('payments.statusLabel')}</Label>
              <Select
                value={filters.status || "all"}
                onValueChange={(value) => handleFilterChange('status', value === 'all' ? '' : value)}
              >
                <SelectTrigger id="statusFilter">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('common.all')}</SelectItem>
                  <SelectItem value="pending">{t('payments.status.pending')}</SelectItem>
                  <SelectItem value="partially_paid">{t('payments.status.partially_paid')}</SelectItem>
                  <SelectItem value="completed">{t('payments.status.completed')}</SelectItem>
                  <SelectItem value="overdue">{t('payments.status.overdue')}</SelectItem>
                  <SelectItem value="cancelled">{t('payments.status.cancelled')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="typeFilter">{t('payments.typeLabel')}</Label>
              <Select
                value={filters.type || "all"}
                onValueChange={(value) => handleFilterChange('type', value === 'all' ? '' : value)}
              >
                <SelectTrigger id="typeFilter">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('common.all')}</SelectItem>
                  <SelectItem value="fee">{t('payments.types.fee')}</SelectItem>
                  <SelectItem value="equipment">{t('payments.types.equipment')}</SelectItem>
                  <SelectItem value="other">{t('payments.types.other')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fromDateFilter">{t('payments.fromDate')}</Label>
              <DatePicker
                date={filters.fromDate ? new Date(filters.fromDate) : undefined}
                onSelect={(date) => {
                  if (date) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    handleFilterChange('fromDate', `${year}-${month}-${day}`);
                  } else {
                    handleFilterChange('fromDate', '');
                  }
                }}
                placeholder={t('payments.filters.selectFromDate')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="toDateFilter">{t('payments.toDate')}</Label>
              <DatePicker
                date={filters.toDate ? new Date(filters.toDate) : undefined}
                onSelect={(date) => {
                  if (date) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    handleFilterChange('toDate', `${year}-${month}-${day}`);
                  } else {
                    handleFilterChange('toDate', '');
                  }
                }}
                placeholder={t('payments.filters.selectToDate')}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button onClick={handleApplyFilters}>
              {t('common.actions.apply')}
            </Button>
            <Button variant="outline" onClick={handleClearFilters}>
              {t('common.actions.clear')}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  ) : (
    <Button
      variant="outline"
      onClick={() => setShowFilters(true)}
    >
      <Filter className="mr-2 h-4 w-4" />
      {t('common.actions.filter')}
    </Button>
  );

  // Create actions for the header
  const actions = (
    <div className="flex gap-2">
      <Button asChild>
        <Link href="/payments/new">
          <CreditCard className="mr-2 h-4 w-4" />
          {t('payments.recordPayment')}
        </Link>
      </Button>
      <Button variant="outline" asChild>
        <Link href="/payments/plans/new">
          <FileText className="mr-2 h-4 w-4" />
          {t('payments.newPaymentPlan')}
        </Link>
      </Button>
    </div>
  );

  return (
    <div className="space-y-6">
      <Tabs defaultValue="payments" onValueChange={handleTabChange} value={activeTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="payments">{t('payments.tabPayments')}</TabsTrigger>
          <TabsTrigger value="transactions">{t('payments.tabTransactions')}</TabsTrigger>
          <TabsTrigger value="plans">{t('payments.tabPlans')}</TabsTrigger>
        </TabsList>

        <TabsContent value="payments">
          <GenericListPage
            data={initialData}
            pagination={initialPagination}
            columns={paymentColumns}
            title={t('payments.title')}
            description={t('payments.messages.managePayments')}
            basePath="/payments"
            initialSearchParams={initialSearchParams}
            actions={actions}
            filters={filtersComponent}
            searchPlaceholder={t('payments.placeholders.searchPayments')}
            paginationOptions={{
              defaultSortBy: 'date',
              defaultSortOrder: 'desc',
              searchMinLength: 3,
              searchDebounceMs: 500,
            }}
          />
        </TabsContent>

        <TabsContent value="transactions">
          {transactionData && transactionPagination ? (
            <PaymentTransactionsListPaginated
              initialData={transactionData}
              initialPagination={transactionPagination}
              athletes={athletes || []}
              initialSearchParams={initialSearchParams}
            />
          ) : (
            <Card>
              <CardContent className="py-8">
                <div className="text-center text-muted-foreground">
                  {t('payments.transactions.noTransactions')}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="plans">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {plans.map((plan) => {
              return (
                <Card key={plan.id}>
                  <CardHeader>
                    <CardTitle>{plan.name}</CardTitle>
                    <CardDescription>
                      {t('payments.plans.monthlyValue')}: {plan.monthlyValue} {t('common.currency')}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {/* Monthly Value */}
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('payments.plans.monthlyValue')}:</span>
                        <span className="font-medium">{plan.monthlyValue} {t('common.currency')}</span>
                      </div>
                      
                      {/* Payment Schedule */}
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('payments.plans.assignDay')}:</span>
                        <span className="font-medium">{plan.assignDay}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('payments.plans.dueDay')}:</span>
                        <span className="font-medium">{plan.dueDay}</span>
                      </div>
                      
                      {/* Status */}
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">{t('payments.statusLabel')}:</span>
                        <span className="capitalize">{t(`payments.planEdit.status.${plan.status}`, plan.status)}</span>
                      </div>
                      
                      {/* Description */}
                      {plan.description && (
                        <div className="mt-2">
                          <span className="text-sm text-muted-foreground">{plan.description}</span>
                        </div>
                      )}
                      
                      {/* Branches */}
                      {plan.branches && plan.branches.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-4">
                          {plan.branches.map((branch) => (
                            <span
                              key={branch.id}
                              className="bg-secondary text-secondary-foreground text-xs rounded-full px-2 py-1"
                            >
                              {t(`common.branches.${branch.name}`, { ns: 'shared' })}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/payments/plans/${plan.id}`}>{t('payments.actions.view')}</Link>
                    </Button>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/payments/plans/${plan.id}/edit`}>{t('payments.actions.edit')}</Link>
                    </Button>
                  </CardFooter>
                </Card>
              );
            })}
            
            <Link href="/payments/plans/new" className="block">
              <Card className="flex flex-col items-center justify-center bg-muted/40 border-dashed h-full hover:bg-muted/60 transition-colors group">
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <div className="mb-4 rounded-full bg-background p-6 group-hover:scale-110 transition-transform">
                    <PlusCircle className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <p className="text-sm text-muted-foreground text-center">
                    {t('payments.createPlan')}
                  </p>
                </CardContent>
              </Card>
            </Link>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
