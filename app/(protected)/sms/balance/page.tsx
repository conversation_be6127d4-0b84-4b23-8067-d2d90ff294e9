import { getSmsBalance, getBalanceStatus } from '@/lib/actions/sms';
import SmsBalancePageClient from './sms-balance-page-client';

export default async function SmsBalancePage() {
  // Fetch balance data server-side
  let initialData: { balance: any; balanceStatus: any } | null = null;

  try {
    const [balance, balanceStatus] = await Promise.all([
      getSmsBalance(),
      getBalanceStatus()
    ]);

    initialData = {
      balance: balance || null,
      balanceStatus: balanceStatus || null
    };
  } catch (error) {
    console.error('Error loading SMS balance data:', error);
    initialData = {
      balance: null,
      balanceStatus: null
    };
  }

  return <SmsBalancePageClient initialData={initialData} />;
}
