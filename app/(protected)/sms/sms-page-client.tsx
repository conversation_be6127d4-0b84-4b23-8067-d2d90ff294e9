'use client';

import { Suspense, useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageSquare, BarChart3} from 'lucide-react';
import { getSmsStats } from '@/lib/actions/sms';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import QuickSmsActions from '@/components/sms/quick-sms-actions';
import SmsBalanceDisplay from '@/components/sms/sms-balance-display';
import { useSms } from '@/contexts/sms-context';

interface SmsOverviewProps {
  initialStats: any;
}

function SmsOverview({ initialStats }: SmsOverviewProps) {
  const { t } = useSafeTranslation();
  const { dataRefreshTrigger } = useSms();
  const [data, setData] = useState<{
    stats: any;
  }>({ stats: initialStats });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Only refetch when dataRefreshTrigger changes (after SMS operations)
    if (dataRefreshTrigger > 0) {
      const loadData = async () => {
        try {
          setLoading(true);
          const stats = await getSmsStats();
          setData({ stats });
          setError(null);
        } catch (err) {
          setError('Error loading SMS data');
        } finally {
          setLoading(false);
        }
      };

      void loadData();
    }
  }, [dataRefreshTrigger]);

  if (loading) {
    return <SmsOverviewSkeleton />;
  }

  if (error || !data) {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <p className="text-sm text-muted-foreground">{error || 'Error loading SMS data'}</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const { stats } = data;

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {/* Balance Card - Reactive */}
      <SmsBalanceDisplay />

      {/* Total SMS Sent */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('sms:stats.totalSent')}</CardTitle>
          <MessageSquare className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {stats?.reduce((total: number, stat: any) => total + (stat.total || 0), 0) || 0}
          </div>
          <p className="text-xs text-muted-foreground">
            {t('sms:stats.allTime')}
          </p>
        </CardContent>
      </Card>

      {/* Payment Reminders */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('sms:stats.paymentReminders')}</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {stats?.filter((stat: any) => stat.type === 'payment_reminder')
              .reduce((total: number, stat: any) => total + (stat.total || 0), 0) || 0}
          </div>
          <p className="text-xs text-muted-foreground">
            {t('sms:stats.paymentReminderSms')}
          </p>
        </CardContent>
      </Card>

      {/* Team Messages */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{t('sms:stats.teamMessages')}</CardTitle>
          <MessageSquare className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {stats?.filter((stat: any) => stat.type === 'team_message')
              .reduce((total: number, stat: any) => total + (stat.total || 0), 0) || 0}
          </div>
          <p className="text-xs text-muted-foreground">
            {t('sms:stats.teamMessagesSent')}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

function SmsOverviewSkeleton() {
  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
      {[...Array(4)].map((_, i) => (
        <Card key={i}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="h-4 w-24 bg-muted animate-pulse rounded" />
            <div className="h-4 w-4 bg-muted animate-pulse rounded" />
          </CardHeader>
          <CardContent>
            <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
            <div className="h-4 w-20 bg-muted animate-pulse rounded" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

interface SmsPageClientProps {
  initialData: {
    stats: any;
    payments: any[];
    athletes: any[];
  };
}

export default function SmsPageClient({ initialData }: SmsPageClientProps) {
  const { t } = useSafeTranslation();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('sms:title')}</h1>
          <p className="text-muted-foreground">
            {t('sms:description')}
          </p>
        </div>
      </div>

      <Suspense fallback={<SmsOverviewSkeleton />}>
        <SmsOverview initialStats={initialData.stats} />
      </Suspense>

      {/* Quick SMS Actions */}
      <QuickSmsActions initialPayments={initialData.payments} initialAthletes={initialData.athletes} />
    </div>
  );
}
