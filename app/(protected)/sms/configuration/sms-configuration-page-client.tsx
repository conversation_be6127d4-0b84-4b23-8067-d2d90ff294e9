'use client';

import { Suspense, useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { getActiveSmsConfiguration, activateSmsConfiguration } from '@/lib/actions/sms';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import { Power, PowerOff } from 'lucide-react';
import SmsConfigurationForm from './sms-configuration-form';
import {useToast} from "@/hooks/use-toast";

interface SmsConfigurationContentProps {
  initialConfiguration: any;
}

function SmsConfigurationContent({ initialConfiguration }: SmsConfigurationContentProps) {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [activeConfig, setActiveConfig] = useState<any>(initialConfiguration);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadConfig = async () => {
    try {
      setLoading(true);
      const config = await getActiveSmsConfiguration();
      setActiveConfig(config);
      setError(null);
    } catch (err) {
      console.error('Error loading SMS configuration:', err);
      setError('Error loading SMS configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleActivateDeactivate = async () => {
    if (!activeConfig) return;

    try {
      if (activeConfig.isActive) {
        // Deactivate by creating a new inactive configuration
        await activateSmsConfiguration(''); // Empty ID will deactivate all
        toast({
          title: t('common.success'),
          description: t('sms:configuration.messages.deactivated'),
        });
      } else {
        await activateSmsConfiguration(activeConfig.id);
        toast({
          title: t('common.success'),
          description: t('sms:configuration.messages.activated'),
        });
      }
      await loadConfig(); // Refresh the configuration
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('sms:configuration.messages.error'),
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return <SmsConfigurationSkeleton />;
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-sm text-muted-foreground">{error}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current Configuration Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {t('sms:configuration.current.title')}
              <Badge variant={activeConfig?.isActive ? 'default' : 'secondary'}>
                {activeConfig?.isActive ? t('sms:configuration.current.active') : t('sms:configuration.current.inactive')}
              </Badge>
            </div>
            {activeConfig && (
              <Button
                variant={activeConfig.isActive ? 'destructive' : 'default'}
                size="sm"
                onClick={handleActivateDeactivate}
                className="flex items-center space-x-2"
              >
                {activeConfig.isActive ? (
                  <>
                    <PowerOff className="h-4 w-4" />
                    <span>{t('sms:configuration.actions.deactivate')}</span>
                  </>
                ) : (
                  <>
                    <Power className="h-4 w-4" />
                    <span>{t('sms:configuration.actions.activate')}</span>
                  </>
                )}
              </Button>
            )}
          </CardTitle>
          <CardDescription>
            {activeConfig?.isActive
              ? `${t('sms:configuration.current.version')} ${activeConfig.version} - ${t('sms:configuration.current.lastUpdated')} ${new Date(activeConfig.updatedAt).toLocaleDateString()}`
              : t('sms:configuration.current.noConfig')
            }
          </CardDescription>
        </CardHeader>
        {activeConfig && (
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-medium mb-2">{t('sms:configuration.templates.pending.title')}</h4>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    {t('sms:configuration.templates.pending.daysDescription')}: {activeConfig.pendingReminderDays?.join(', ') || 'Not configured'}
                  </p>
                  <div className="p-3 bg-muted rounded-md">
                    <p className="text-sm">{activeConfig.pendingPaymentTemplate}</p>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-2">{t('sms:configuration.templates.overdue.title')}</h4>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    {t('sms:configuration.templates.overdue.daysDescription')}: {activeConfig.overdueReminderDays?.join(', ') || 'Not configured'}
                  </p>
                  <div className="p-3 bg-muted rounded-md">
                    <p className="text-sm">{activeConfig.overduePaymentTemplate}</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Configuration Form */}
      <Card>
        <CardHeader>
          <CardTitle>{t('sms:configuration.update.title')}</CardTitle>
          <CardDescription>
            {t('sms:configuration.update.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SmsConfigurationForm initialData={activeConfig} onSuccess={loadConfig} />
        </CardContent>
      </Card>

      {/* Template Variables Help */}
      <Card>
        <CardHeader>
          <CardTitle>{t('sms:configuration.variables.title')}</CardTitle>
          <CardDescription>
            {t('sms:configuration.variables.description')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <h4 className="font-medium mb-2">{t('sms:configuration.variables.payment')}</h4>
              <ul className="space-y-1 text-sm">
                <li><code className="bg-muted px-1 rounded">{`{{athleteName}}`}</code> - {t('sms:configuration.variables.athleteName')}</li>
                <li><code className="bg-muted px-1 rounded">{`{{amount}}`}</code> - {t('sms:configuration.variables.amount')}</li>
                <li><code className="bg-muted px-1 rounded">{`{{paymentDueDate}}`}</code> - {t('sms:configuration.variables.paymentDueDate')}</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">{t('sms:configuration.variables.organization')}</h4>
              <ul className="space-y-1 text-sm">
                <li><code className="bg-muted px-1 rounded">{`{{clubName}}`}</code> - {t('sms:configuration.variables.clubName')}</li>
              </ul>
            </div>
          </div>
          <div className="mt-4 p-4 bg-blue-50 rounded-md">
            <h4 className="font-medium text-blue-900 mb-2">{t('sms:configuration.variables.example')}</h4>
            <p className="text-sm text-blue-800">
              Dear {`{{athleteName}}`}, your payment of {`{{amount}}`} for {`{{clubName}}`} is due on {`{{paymentDueDate}}`}. Please make your payment on time.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function SmsConfigurationSkeleton() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="h-6 w-48 bg-muted animate-pulse rounded" />
          <div className="h-4 w-64 bg-muted animate-pulse rounded" />
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <div className="h-4 w-32 bg-muted animate-pulse rounded" />
              <div className="h-20 bg-muted animate-pulse rounded" />
            </div>
            <div className="space-y-2">
              <div className="h-4 w-32 bg-muted animate-pulse rounded" />
              <div className="h-20 bg-muted animate-pulse rounded" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface SmsConfigurationPageClientProps {
  initialConfiguration: any;
}

export default function SmsConfigurationPageClient({ initialConfiguration }: SmsConfigurationPageClientProps) {
  const { t } = useSafeTranslation();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('sms:configuration.title')}</h1>
          <p className="text-muted-foreground">
            {t('sms:configuration.description')}
          </p>
        </div>
      </div>

      <Suspense fallback={<SmsConfigurationSkeleton />}>
        <SmsConfigurationContent initialConfiguration={initialConfiguration} />
      </Suspense>
    </div>
  );
}
