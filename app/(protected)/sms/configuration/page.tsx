import { getActiveSmsConfiguration } from '@/lib/actions/sms';
import SmsConfigurationPageClient from './sms-configuration-page-client';

export default async function SmsConfigurationPage() {
  // Fetch configuration data server-side
  let initialConfiguration = null;
  try {
    initialConfiguration = await getActiveSmsConfiguration();
  } catch (error) {
    console.error('Error loading SMS configuration:', error);
  }

  return <SmsConfigurationPageClient initialConfiguration={initialConfiguration} />;
}
