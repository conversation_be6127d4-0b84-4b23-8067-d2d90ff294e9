import { getSmsStats, getSmsBalance, getPaymentsForSms, getAllActiveAthletesForSms } from '@/lib/actions/sms';
import SmsPageClient from './sms-page-client';

export default async function SmsPage() {
  // Fetch all data server-side EXCEPT balance (let context handle it)
  try {
    const [stats, paymentsResult, athletesResult] = await Promise.all([
      getSmsStats(),
      getPaymentsForSms(),
      getAllActiveAthletesForSms()
    ]);

    const initialData = {
      stats,
      payments: paymentsResult.data || [],
      athletes: athletesResult.data || []
    };

    return <SmsPageClient initialData={initialData} />;
  } catch (error) {
    console.error('Error loading SMS data:', error);

    // Return with empty data on error
    const initialData = {
      stats: null,
      payments: [],
      athletes: []
    };

    return <SmsPageClient initialData={initialData} />;
  }
}
