import { getSmsLogsPaginated } from '@/lib/actions/sms';
import { SmsLogsListPaginated } from '@/components/sms/sms-logs-list-paginated';

interface SmsHistoryPageProps {
  searchParams: Promise<{
    page?: string;
    limit?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    type?: string;
    status?: string;
    senderType?: string;
    dateFrom?: string;
    dateTo?: string;
  }>;
}

export default async function SmsHistoryPage({ searchParams }: SmsHistoryPageProps) {
  // Resolve the searchParams promise
  const resolvedSearchParams = await searchParams;
  
  const page = parseInt(resolvedSearchParams.page || '1');
  const limit = parseInt(resolvedSearchParams.limit || '10');
  const search = resolvedSearchParams.search || '';
  const sortBy = resolvedSearchParams.sortBy || 'sentAt';
  const sortOrder = (resolvedSearchParams.sortOrder as 'asc' | 'desc') || 'desc';

  // Build filters object
  const filters: any = {};
  if (resolvedSearchParams.type && resolvedSearchParams.type !== 'all') {
    filters.type = resolvedSearchParams.type;
  }
  if (resolvedSearchParams.status && resolvedSearchParams.status !== 'all') {
    filters.status = resolvedSearchParams.status;
  }
  if (resolvedSearchParams.senderType && resolvedSearchParams.senderType !== 'all') {
    filters.senderType = resolvedSearchParams.senderType;
  }
  if (resolvedSearchParams.dateFrom) {
    filters.dateFrom = resolvedSearchParams.dateFrom;
  }
  if (resolvedSearchParams.dateTo) {
    filters.dateTo = resolvedSearchParams.dateTo;
  }

  try {
    const result = await getSmsLogsPaginated({
      page,
      limit,
      search: search || undefined,
      sortBy,
      sortOrder,
      filters: Object.keys(filters).length > 0 ? filters : undefined,
    });

    return (
      <SmsLogsListPaginated
        initialData={result.data || []}
        initialPagination={result.pagination}
        initialSearchParams={resolvedSearchParams}
      />
    );
  } catch (error) {
    console.error('Error loading SMS history:', error);
    
    // Return empty state on error
    return (
      <SmsLogsListPaginated
        initialData={[]}
        initialPagination={{
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPreviousPage: false,
        }}
        initialSearchParams={resolvedSearchParams}
      />
    );
  }
}