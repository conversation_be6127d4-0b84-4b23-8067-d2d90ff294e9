"use client";

import { useState, useEffect, useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Plus, X } from "lucide-react";
import Link from "next/link";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { DatePicker } from "@/components/ui/date-picker";
import { calculateProratedAmountForBillingCycle } from "@/lib/proration-utils";
import { createAthleteWithTeamAssignmentsAndBalance } from "@/lib/actions/athletes";
import {useToast} from "@/hooks/use-toast";

interface Team {
  id: string;
  name: string;
  description?: string;
  school?: {
    name: string;
  };
  branch?: {
    name: string;
  };
}

interface PaymentPlan {
  id: string;
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  description?: string;
  status?: string;
}

interface TeamPaymentAssignment {
  teamId: string;
  paymentPlanId?: string;
}

interface NewAthleteClientProps {
  teams: Team[];
  paymentPlans: PaymentPlan[];
}

export default function NewAthleteClient({ teams, paymentPlans }: NewAthleteClientProps) {
  const router = useRouter();
  const { t, i18n } = useSafeTranslation();
  const [isPending, startTransition] = useTransition();
  const { toast } = useToast();
  const [athlete, setAthlete] = useState({
    name: "",
    surname: "",
    nationalId: "",
    birthDate: "",
    registrationDate: new Date().toISOString().split('T')[0], // Default to today
    parent: {
      name: "",
      surname: "",
      phone: "",
      email: "",
      address: "",
    },
  });

  // State for team and payment plan assignments
  const [teamAssignments, setTeamAssignments] = useState<TeamPaymentAssignment[]>([]);

  // State for balance management
  const [useProrated, setUseProrated] = useState(false);
  const [initialBalance, setInitialBalance] = useState("");
  const [calculatedBalance, setCalculatedBalance] = useState<number | null>(null);

  // Get all payment plans that are selected from team assignments
  const getSelectedPaymentPlans = useCallback(() => {
    const assignments = teamAssignments.filter(a => a.paymentPlanId);
    return assignments.map(assignment => {
      const plan = paymentPlans.find(plan => plan.id === assignment.paymentPlanId);
      return { assignment, plan };
    }).filter(item => item.plan); // Only include items where plan was found
  }, [teamAssignments, paymentPlans]);

  // Calculate total prorated balance when checkbox is checked and payment plans are selected
  useEffect(() => {
    if (useProrated) {
      const selectedPlans = getSelectedPaymentPlans();
      if (selectedPlans.length > 0) {
        let totalProratedAmount = 0;
        
        selectedPlans.forEach(({ plan }) => {
          if (plan) {
            const monthlyAmount = parseFloat(plan.monthlyValue);
            // Use the new billing cycle-based proration calculation
            const proratedAmount = calculateProratedAmountForBillingCycle(
              monthlyAmount,
              plan.assignDay
            );
            totalProratedAmount += proratedAmount;
          }
        });
        
        setCalculatedBalance(totalProratedAmount);
        setInitialBalance(totalProratedAmount.toFixed(2));
      } else {
        setCalculatedBalance(null);
        setInitialBalance("");
      }
    } else {
      setCalculatedBalance(null);
    }
  }, [useProrated, getSelectedPaymentPlans]);

  const addTeamAssignment = () => {
    if (teams.length === 0) {
      toast({
        title: t('common.error'),
        description: t('athletes.messages.noTeamsAvailable', 'No teams available'),
        variant: "destructive",
      });
      return;
    }
    
    const availableTeams = teams.filter(
      team => !teamAssignments.some(assignment => assignment.teamId === team.id)
    );
    
    if (availableTeams.length === 0) {
      toast({
        title: t('common.error'),
        description: t('athletes.messages.allTeamsSelected', 'All available teams have been selected'),
        variant: "destructive",
      });
      return;
    }
    
    setTeamAssignments(prev => [...prev, { teamId: availableTeams[0].id }]);
  };

  const removeTeamAssignment = (index: number) => {
    setTeamAssignments(prev => prev.filter((_, i) => i !== index));
  };

  const updateTeamAssignment = (index: number, field: keyof TeamPaymentAssignment, value: string | undefined) => {
    setTeamAssignments(prev => 
      prev.map((assignment, i) => 
        i === index ? { ...assignment, [field]: value } : assignment
      )
    );
  };

  const getAvailableTeamsForAssignment = (currentIndex: number) => {
    return teams.filter(team => 
      !teamAssignments.some((assignment, index) => 
        index !== currentIndex && assignment.teamId === team.id
      )
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation
    if (!athlete.name || !athlete.surname || !athlete.nationalId || !athlete.birthDate) {
      toast({
        title: t('common.error'),
        description: t('athletes.messages.requiredFields', 'Please fill in all required fields'),
        variant: "destructive",
      });
      return;
    }
    
    if (!athlete.parent.name || !athlete.parent.surname || !athlete.parent.phone) {
      toast({
        title: t('common.error'),
        description: t('athletes.messages.requiredParentFields', 'Please fill in all required parent fields'),
        variant: "destructive",
      });
      return;
    }

    // Validate parent phone format
    if (athlete.parent.phone) {
      const cleanedPhone = athlete.parent.phone.replace(/[\s\-]/g, '').replace(/\+90/g, '').replace(/^0/, '');
      const parentPhoneRegex = /^[1-9]\d{9}$/;
      if (!parentPhoneRegex.test(cleanedPhone)) {
        toast({
          title: t('common.error'),
          description: t('common.validation.parentPhone'),
          variant: "destructive",
        });
        return;
      }
    }

    // Validate team assignments (ensure no duplicate teams)
    const teamIds = teamAssignments.map(assignment => assignment.teamId);
    const uniqueTeamIds = new Set(teamIds);
    if (teamIds.length !== uniqueTeamIds.size) {
      toast({
        title: t('common.error'),
        description: t('athletes.messages.duplicateTeams', 'Each team can only be selected once'),
        variant: "destructive",
      });
      return;
    }

    const athleteData = {
      name: athlete.name,
      surname: athlete.surname,
      nationalId: athlete.nationalId,
      birthDate: athlete.birthDate,
      parentName: athlete.parent.name,
      parentSurname: athlete.parent.surname,
      parentPhone: athlete.parent.phone,
      parentEmail: athlete.parent.email || undefined,
      parentAddress: athlete.parent.address || undefined,
      teamAssignments: teamAssignments.length > 0 ? teamAssignments : undefined,
      initialBalance: initialBalance || undefined,
      useProrated: useProrated,
    };

    startTransition(async () => {
      try {
        const result = await createAthleteWithTeamAssignmentsAndBalance(athleteData, i18n.language || 'en');
        
        if (result.success) {
          toast({
            title: t('common.success'),
            description: t('athletes.messages.createSuccess', 'Athlete created successfully'),
          });
          router.push('/athletes');
        } else {
          let errorDescriptionKey = '';
          if(result.errorType == 'BusinessRuleError'){
            errorDescriptionKey = `errors.${result.error}`;
          }else{
            errorDescriptionKey = 'athletes.messages.createError';
          }
          toast({
            title: t('common.error'),
            description: t(errorDescriptionKey),
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Error creating athlete:', error);
        toast({
          title: t('common.error'),
          description: t('athletes.messages.createError', 'Failed to create athlete'),
          variant: "destructive",
        });
      }
    });
  };

  return (
    <div className="container mx-auto py-6">
      <Button
        variant="ghost"
        className="mb-6"
        asChild
      >
        <Link href="/athletes">
          <ArrowLeft className="mr-2 h-4 w-4" />
          {t('common.actions.back')}
        </Link>
      </Button>

      <Card>
        <CardHeader>
          <CardTitle>{t('athletes.new')}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Athlete Basic Information */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">{t('athletes.details.firstName')} *</Label>
                  <Input
                    id="name"
                    required
                    value={athlete.name}
                    onChange={(e) =>
                      setAthlete((prev) => ({ ...prev, name: e.target.value }))
                    }
                    placeholder={t('athletes.placeholders.firstName')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="surname">{t('athletes.details.lastName')} *</Label>
                  <Input
                    id="surname"
                    required
                    value={athlete.surname}
                    onChange={(e) =>
                      setAthlete((prev) => ({ ...prev, surname: e.target.value }))
                    }
                    placeholder={t('athletes.placeholders.lastName')}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="nationalId">{t('athletes.details.nationalId')} *</Label>
                  <Input
                    id="nationalId"
                    required
                    value={athlete.nationalId}
                    onChange={(e) =>
                      setAthlete((prev) => ({ ...prev, nationalId: e.target.value }))
                    }
                    placeholder={t('athletes.placeholders.nationalId')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="birthDate">{t('athletes.details.birthDate')} *</Label>
                  <DatePicker
                    date={athlete.birthDate ? new Date(athlete.birthDate) : undefined}
                    onSelect={(date) => {
                      if (date) {
                        // Format date as YYYY-MM-DD in local timezone to avoid timezone issues
                        const year = date.getFullYear();
                        const month = String(date.getMonth() + 1).padStart(2, '0');
                        const day = String(date.getDate()).padStart(2, '0');
                        setAthlete((prev) => ({ ...prev, birthDate: `${year}-${month}-${day}` }));
                      } else {
                        setAthlete((prev) => ({ ...prev, birthDate: '' }));
                      }
                    }}
                    placeholder={t('athletes.form.selectBirthDate')}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="registrationDate">{t('athletes.form.registrationDate')} *</Label>
                  <DatePicker
                    date={athlete.registrationDate ? new Date(athlete.registrationDate) : undefined}
                    onSelect={(date) => {
                      if (date) {
                        // Format date as YYYY-MM-DD in local timezone to avoid timezone issues
                        const year = date.getFullYear();
                        const month = String(date.getMonth() + 1).padStart(2, '0');
                        const day = String(date.getDate()).padStart(2, '0');
                        setAthlete((prev) => ({ ...prev, registrationDate: `${year}-${month}-${day}` }));
                      } else {
                        setAthlete((prev) => ({ ...prev, registrationDate: '' }));
                      }
                    }}
                    placeholder={t('athletes.form.selectRegistrationDate')}
                  />
                </div>
              </div>

              {/* Parent Information */}
              <div className="border-t pt-4 mt-4">
                <h3 className="text-lg font-medium mb-4">{t('athletes.details.parent')}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="parentName">{t('athletes.details.parentName')} *</Label>
                    <Input
                      id="parentName"
                      required
                      value={athlete.parent.name}
                      onChange={(e) =>
                        setAthlete((prev) => ({
                          ...prev,
                          parent: { ...prev.parent, name: e.target.value },
                        }))
                      }
                      placeholder={t('athletes.placeholders.parentName')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="parentSurname">{t('athletes.details.lastName')} *</Label>
                    <Input
                      id="parentSurname"
                      required
                      value={athlete.parent.surname}
                      onChange={(e) =>
                        setAthlete((prev) => ({
                          ...prev,
                          parent: { ...prev.parent, surname: e.target.value },
                        }))
                      }
                      placeholder={t('athletes.placeholders.lastName')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="parentPhone">{t('athletes.details.parentPhone')} *</Label>
                    <Input
                      id="parentPhone"
                      required
                      value={athlete.parent.phone}
                      onChange={(e) =>
                        setAthlete((prev) => ({
                          ...prev,
                          parent: { ...prev.parent, phone: e.target.value },
                        }))
                      }
                      placeholder={t('athletes.placeholders.parentPhone')}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="parentEmail">{t('athletes.details.parentEmail')}</Label>
                    <Input
                      id="parentEmail"
                      type="email"
                      value={athlete.parent.email}
                      onChange={(e) =>
                        setAthlete((prev) => ({
                          ...prev,
                          parent: { ...prev.parent, email: e.target.value },
                        }))
                      }
                      placeholder={t('athletes.placeholders.parentEmail')}
                    />
                  </div>
                </div>

                <div className="space-y-2 mt-4">
                  <Label htmlFor="parentAddress">{t('athletes.details.parentAddress')}</Label>
                  <Input
                    id="parentAddress"
                    value={athlete.parent.address}
                    onChange={(e) =>
                      setAthlete((prev) => ({
                        ...prev,
                        parent: { ...prev.parent, address: e.target.value },
                      }))
                    }
                    placeholder={t('athletes.placeholders.parentAddress')}
                  />
                </div>
              </div>

              {/* Team and Payment Plan Assignments */}
              <div className="border-t pt-4 mt-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium">{t('athletes.teamAssignments', 'Team Assignments')} ({t('common.optional', 'Optional')})</h3>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addTeamAssignment}
                    disabled={teams.length === 0 || teamAssignments.length >= teams.length}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {t('athletes.addTeamAssignment', 'Add Team')}
                  </Button>
                </div>
                
                {teamAssignments.length === 0 ? (
                  <p className="text-sm text-muted-foreground">
                    {t('athletes.noTeamAssignments', 'No team assignments. You can add the athlete to teams later.')}
                  </p>
                ) : (
                  <div className="space-y-4">
                    {teamAssignments.map((assignment, index) => {
                      const availableTeams = getAvailableTeamsForAssignment(index);
                      const selectedTeam = teams.find(team => team.id === assignment.teamId);
                      const availablePaymentPlans = paymentPlans.filter(plan => plan.status !== 'inactive');
                      
                      return (
                        <div key={index} className="p-4 border rounded-lg bg-muted/50">
                          <div className="flex items-start justify-between mb-4">
                            <h4 className="font-medium">{t('athletes.teamAssignment', 'Team Assignment')} {index + 1}</h4>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeTeamAssignment(index)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label>{t('teams.details.team', 'Team')} *</Label>
                              <Select
                                value={assignment.teamId}
                                onValueChange={(value) => updateTeamAssignment(index, 'teamId', value)}
                              >
                                <SelectTrigger className="h-auto min-h-[40px]">
                                  <SelectValue placeholder={t('athletes.form.selectTeam', 'Select a team')} />
                                </SelectTrigger>
                                <SelectContent>
                                  {availableTeams.map((team) => (
                                    <SelectItem key={team.id} value={team.id} className="h-auto py-2">
                                      <div className="flex flex-col items-start w-full">
                                        <span className="font-medium">{team.name}</span>
                                        {(team.school?.name || team.branch?.name) && (
                                          <span className="text-xs text-muted-foreground mt-1">
                                            {[team.school?.name, team.branch?.name].filter(Boolean).join(' - ')}
                                          </span>
                                        )}
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            
                            <div className="space-y-2">
                              <Label>{t('payments.paymentPlan', 'Payment Plan')} ({t('common.optional', 'Optional')})</Label>
                              <Select
                                value={assignment.paymentPlanId || "none"}
                                onValueChange={(value) => updateTeamAssignment(index, 'paymentPlanId', value === "none" ? undefined : value)}
                              >
                                <SelectTrigger className="h-auto min-h-[40px]">
                                  <SelectValue placeholder={t('athletes.form.selectPaymentPlan', 'Select a payment plan')} />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="none" className="h-auto py-2">
                                    <span className="font-medium">{t('common.actions.none', 'None')}</span>
                                  </SelectItem>
                                  {availablePaymentPlans.map((plan) => (
                                    <SelectItem key={plan.id} value={plan.id} className="h-auto py-2">
                                      <div className="flex flex-col items-start w-full">
                                        <span className="font-medium">{plan.name}</span>
                                        <span className="text-xs text-muted-foreground mt-1">
                                          {parseFloat(plan.monthlyValue).toFixed(2)} {t('common.currency', 'TL')}
                                        </span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          
                          {selectedTeam && (
                            <div className="mt-2 p-2 bg-background rounded text-sm">
                              <strong>{t('teams.details.team', 'Team')}:</strong> {selectedTeam.name}
                              {selectedTeam.description && (
                                <div className="text-muted-foreground">{selectedTeam.description}</div>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>

              {/* Initial Balance Section */}
              <div className="border-t pt-4 mt-4">
                <h3 className="text-lg font-medium mb-4">{t('athletes.initialBalance', 'Initial Balance')} ({t('common.optional', 'Optional')})</h3>
                
                {/* Show prorated option only if payment plans are selected */}
                {getSelectedPaymentPlans().length > 0 && (
                  <div className="space-y-4 mb-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="useProrated"
                        checked={useProrated}
                        onCheckedChange={(checked) => setUseProrated(checked as boolean)}
                      />
                      <Label htmlFor="useProrated" className="text-sm">
                        {t('athletes.proratedBalance', 'Automatically calculate and apply prorated balance for the remaining days of the month')}
                      </Label>
                    </div>
                    
                    {useProrated && calculatedBalance !== null && (
                      <div className="p-3 bg-muted rounded-md">
                        <div className="text-sm space-y-2">
                          <div>
                            <strong>{t('athletes.selectedPlans', 'Selected Payment Plans')}:</strong>
                          </div>
                          {getSelectedPaymentPlans().map(({ assignment, plan }, index) => {
                            const monthlyAmount = parseFloat(plan?.monthlyValue || '0');
                            const proratedAmount = calculateProratedAmountForBillingCycle(
                              monthlyAmount,
                              plan?.assignDay || 1
                            );
                            
                            // Calculate billing cycle days for this plan
                            const today = new Date();
                            const year = today.getFullYear();
                            const month = today.getMonth();
                            const assignDay = plan?.assignDay || 1;
                            
                            // Calculate last billing date
                            let lastBillingDate = new Date(year, month, assignDay);
                            if (lastBillingDate > today) {
                              lastBillingDate = new Date(year, month - 1, assignDay);
                            }
                            
                            // Calculate next billing date
                            let nextBillingDate = new Date(year, month, assignDay);
                            if (nextBillingDate <= today) {
                              nextBillingDate = new Date(year, month + 1, assignDay);
                            }
                            
                            const totalBillingDays = Math.ceil((nextBillingDate.getTime() - lastBillingDate.getTime()) / (1000 * 60 * 60 * 24));
                            const remainingDays = Math.ceil((nextBillingDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                            
                            return (
                              <div key={assignment.teamId} className="ml-4 p-2 bg-background rounded text-xs">
                                <div>
                                  <strong>{plan?.name}</strong> - {monthlyAmount.toFixed(2)} {t('common.currency', 'TL')}/{t('common.month', 'month')}
                                </div>
                                <div className="text-muted-foreground">
                                  {t('athletes.proratedLabel', 'Prorated')}: {proratedAmount.toFixed(2)} {t('common.currency', 'TL')}
                                </div>
                                <div className="text-muted-foreground text-xs">
                                  {t('athletes.billingCycle', 'Billing cycle')} ({t('athletes.day', 'Day')} {assignDay}): {remainingDays}/{totalBillingDays} {t('athletes.daysRemaining', 'days remaining')}
                                </div>
                              </div>
                            );
                          })}
                          <div className="font-medium text-primary">
                            <strong>{t('athletes.totalCalculatedAmount', 'Total Calculated Prorated Amount')}:</strong> {calculatedBalance.toFixed(2)} {t('common.currency', 'TL')}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Manual balance input */}
                {!useProrated && (
                  <div className="space-y-2">
                    <Label htmlFor="initialBalance">{t('athletes.manualBalance', 'Manual Initial Balance')}</Label>
                    <Input
                      id="initialBalance"
                      type="number"
                      step="0.01"
                      min="0"
                      value={initialBalance}
                      onChange={(e) => setInitialBalance(e.target.value)}
                      placeholder={t('athletes.enterBalance', 'Enter initial balance amount')}
                    />
                    <p className="text-xs text-muted-foreground">
                      {t('athletes.balanceHint', 'Enter 0 or leave empty for no initial balance. This amount will be added to the athlete\'s account as a pending payment.')}
                    </p>
                  </div>
                )}

                {useProrated && (
                  <div className="space-y-2">
                    <Label htmlFor="proratedBalance">{t('athletes.proratedBalance', 'Prorated Balance')}</Label>
                    <Input
                      id="proratedBalance"
                      type="number"
                      step="0.01"
                      value={initialBalance}
                      readOnly
                      className="bg-muted"
                    />
                    <p className="text-xs text-muted-foreground">
                      {t('athletes.proratedHint', 'This amount is automatically calculated based on the remaining days in the current month.')}
                    </p>
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/athletes")}
              >
                {t('common.actions.cancel')}
              </Button>
              <Button type="submit" disabled={isPending}>
                {isPending ? t('athletes.actions.creating') : t('athletes.actions.createAthlete')}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}