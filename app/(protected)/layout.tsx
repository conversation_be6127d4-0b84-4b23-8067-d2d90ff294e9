import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { MainNav } from "@/components/layout/main-nav";
import { BreadcrumbNav } from "@/components/layout/breadcrumb-nav";
import { ThemeProvider } from "@/components/layout/theme-provider";
import { Toaster } from "@/components/ui/toaster";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Sporlex - Kulup Yönetmenin Kolay Yolu",
  description: "Kulüp yönetimini kolaylaştıran Sporlex ile spor kulübünüzü daha verimli yönetin.",
};

export default function ProtectedLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className={inter.className}>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <div className="min-h-screen flex flex-col">
          <MainNav />
          <main className="flex-1 container mx-auto py-4 px-4 md:px-6">
            <BreadcrumbNav />
            {children}
          </main>
        </div>
        <Toaster />
      </ThemeProvider>
    </div>
  );
}