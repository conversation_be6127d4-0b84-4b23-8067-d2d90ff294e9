"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { DatePicker } from "@/components/ui/date-picker";
import { ArrowLeft, Package2, Check, ChevronsUpDown } from "lucide-react";
import Link from "next/link";
import { SecureImage } from "@/components/ui/secure-image";
import { Athlete, Item } from "@/lib/types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { createItemSale } from "@/lib/api";
import {useToast} from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface SellItemClientProps {
  item: Item;
  athletes: Athlete[];
}

export default function SellItemClient({ item, athletes }: SellItemClientProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { t } = useSafeTranslation();
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [sale, setSale] = useState({
    athleteId: "",
    quantity: 1,
    paymentStatus: "completed" as "pending" | "completed" | "overdue",
    billingDate: new Date() as Date | undefined,
    dueDate: (() => {
      const defaultDueDate = new Date();
      defaultDueDate.setDate(defaultDueDate.getDate() + 10);
      return defaultDueDate;
    })() as Date | undefined,
  });

  // Filter athletes based on search term
  const filteredAthletes = searchTerm.length < 3 
    ? [] 
    : athletes.filter(athlete => {
        const searchLower = searchTerm.toLowerCase();
        return (
          athlete.name?.toLowerCase().includes(searchLower) ||
          athlete.surname?.toLowerCase().includes(searchLower) ||
          (athlete.parentPhone && athlete.parentPhone.includes(searchTerm))
        );
      });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!item || !sale.athleteId || sale.quantity < 1) {
      toast({
        title: t('common.error'),
        description: t('items.sell.errors.invalidData'),
        variant: "destructive",
      });
      return;
    }

    try {
      setLoading(true);
      
      const result = await createItemSale({
        itemId: item.id,
        athleteId: sale.athleteId,
        quantity: sale.quantity,
        paymentStatus: sale.paymentStatus,
        billingDate: sale.billingDate ? sale.billingDate.toISOString().split('T')[0] : undefined,
        dueDate: sale.dueDate ? sale.dueDate.toISOString().split('T')[0] : undefined,
      });
      if(result.success){
        toast({
          title: t('common.success'),
          description: t('items.sell.messages.saleCompleted'),
        });
        router.push("/items");
      }else{
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'items.sell.errors.saleError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }

    } catch (error) {
      console.error("Error processing sale:", error);
      toast({
        title: t('common.error'),
        description: t('items.sell.errors.saleError'),
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const totalPrice = parseFloat(item.price) * sale.quantity;

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Link href="/items">
          <Button variant="ghost" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold tracking-tight">{t('items.sell.title')}</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t('items.sell.itemDetails')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="relative aspect-square">
                <SecureImage
                  src={item.image || ""}
                  alt={item.name}
                  fill
                  className="object-cover rounded-md"
                  placeholderIcon={Package2}
                />
              </div>

              <div className="space-y-4">
                <h3 className="font-medium">{item.name}</h3>
                {item.description && (
                  <p className="text-sm text-muted-foreground mt-1">
                    {item.description}
                  </p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('items.details.price')}:</span>
                <span className="font-medium">{item.price} {t('common.currency')}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t('items.sell.availableStock')}:</span>
                <span className="font-medium">{item.stock}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('items.sell.saleInformation')}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>{t('items.sell.selectAthlete')} *</Label>
                  <Popover open={open} onOpenChange={setOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={open}
                        className="w-full justify-between h-auto min-h-[40px] p-3"
                      >
                        {sale.athleteId ? (
                          <div className="flex items-center gap-2 text-left">
                            <div className="flex flex-col">
                              <span className="font-medium">
                                {athletes.find((athlete) => athlete.id === sale.athleteId)?.name} {athletes.find((athlete) => athlete.id === sale.athleteId)?.surname}
                              </span>
                              {athletes.find((athlete) => athlete.id === sale.athleteId)?.parentPhone && (
                                <span className="text-sm text-muted-foreground">
                                  {athletes.find((athlete) => athlete.id === sale.athleteId)?.parentPhone}
                                </span>
                              )}
                            </div>
                          </div>
                        ) : (
                          t('items.placeholders.chooseAthlete')
                        )}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent 
                      className="p-0" 
                      align="start"
                      style={{ width: 'var(--radix-popover-trigger-width)' }}
                    >
                      <Command>
                        <CommandInput 
                          placeholder={t('athletes.placeholders.searchAthletes')}
                          value={searchTerm}
                          onValueChange={setSearchTerm}
                        />
                        <CommandList className="max-h-[300px]">
                          {searchTerm.length < 3 ? (
                            <div className="p-4 text-center text-sm text-muted-foreground">
                              {t('common.search.enterMinChars', { count: 3 })}
                            </div>
                          ) : (
                            <>
                              <CommandEmpty>{t('common.search.noResults')}</CommandEmpty>
                              <CommandGroup>
                                {filteredAthletes.map((athlete) => (
                                  <CommandItem
                                    key={athlete.id}
                                    value={`${athlete.name} ${athlete.surname} ${athlete.parentPhone || ''}`}
                                    onSelect={() => {
                                      setSale((prev) => ({ ...prev, athleteId: athlete.id }));
                                      setOpen(false);
                                      setSearchTerm(""); // Reset search query
                                    }}
                                  >
                                    <Check
                                      className={cn(
                                        "mr-2 h-4 w-4",
                                        sale.athleteId === athlete.id ? "opacity-100" : "opacity-0"
                                      )}
                                    />
                                    <div className="flex-1">
                                      <div className="font-medium">{athlete.name} {athlete.surname}</div>
                                      <div className="text-sm text-muted-foreground">
                                        {athlete.parentPhone && `${t('athletes.table.parentPhone')}: ${athlete.parentPhone}`}
                                        {athlete.parentPhone && athlete.parentEmail && ' | '}
                                        {athlete.parentEmail && `${t('athletes.table.parentEmail')}: ${athlete.parentEmail}`}
                                      </div>
                                    </div>
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            </>
                          )}
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label>{t('items.sell.quantity')} *</Label>
                  <Input
                    type="number"
                    min="1"
                    max={item.stock}
                    value={sale.quantity}
                    onChange={(e) =>
                      setSale((prev) => ({ ...prev, quantity: parseInt(e.target.value) || NaN}))
                    }
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label>{t('items.sell.paymentStatus')} *</Label>
                  <Select
                    value={sale.paymentStatus}
                    onValueChange={(value) =>
                      setSale((prev) => ({
                        ...prev,
                        paymentStatus: value as "pending" | "completed" | "overdue"
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={t('items.sell.selectPaymentStatus')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="completed">{t('items.paymentStatus.completed')}</SelectItem>
                      <SelectItem value="pending">{t('items.paymentStatus.pending')}</SelectItem>
                      <SelectItem value="overdue">{t('items.paymentStatus.overdue')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>



                <div className="space-y-2">
                  <Label>{t('items.sell.billingDate')} *</Label>
                  <DatePicker
                    date={sale.billingDate}
                    onSelect={(date) =>
                      setSale((prev) => ({ ...prev, billingDate: date }))
                    }
                    placeholder={t('items.sell.selectBillingDate')}
                  />
                </div>

                <div className="space-y-2">
                  <Label>{t('items.sell.dueDate')} *</Label>
                  <DatePicker
                    date={sale.dueDate}
                    onSelect={(date) =>
                      setSale((prev) => ({ ...prev, dueDate: date }))
                    }
                    placeholder={t('items.sell.selectDueDate')}
                  />
                </div>

                <div className="pt-4 border-t">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-muted-foreground">{t('items.sell.unitPrice')}:</span>
                    <span>{item.price} {t('common.currency')}</span>
                  </div>
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-sm text-muted-foreground">{t('items.sell.quantity')}:</span>
                    <span>× {sale.quantity}</span>
                  </div>
                  <div className="flex items-center justify-between text-lg font-medium">
                    <span>{t('items.sell.total')}:</span>
                    <span>{totalPrice.toFixed(2)} {t('common.currency')}</span>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push("/items")}
                  disabled={loading}
                >
                  {t('common.actions.cancel')}
                </Button>
                <Button 
                  type="submit" 
                  disabled={loading || !sale.athleteId || sale.quantity < 1 || sale.quantity > item.stock}
                >
                  {loading ? t('items.sell.processing') : t('items.sell.completeSale')}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
