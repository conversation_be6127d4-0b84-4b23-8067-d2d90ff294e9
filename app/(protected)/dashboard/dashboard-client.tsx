"use client";

import { Suspense } from "react";
import { SkeletonDashboardCard } from "@/components/ui/skeleton-dashboard-card";
import { DashboardWidgets } from "@/components/dashboard/dashboard-widgets";
import { DashboardProvider } from "@/contexts/dashboard-context";
import {
  FinancialOverviewChart,
  ExpenseBreakdownChart,
  IncomeSourcesChart,
  OverduePaymentsList
} from "@/components/dashboard/charts";

interface DashboardClientProps {
  financialData: any;
  overdueAthletes: any[];
}

export default function DashboardClient({ financialData, overdueAthletes }: DashboardClientProps) {
  return (
    <div className="space-y-8">
      <DashboardProvider>
        <Suspense fallback={
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <SkeletonDashboardCard />
              <SkeletonDashboardCard />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <SkeletonDashboardCard />
              <SkeletonDashboardCard />
              <SkeletonDashboardCard />
              <SkeletonDashboardCard />
            </div>
          </div>
        }>
          <DashboardWidgets />
        </Suspense>
      </DashboardProvider>
      
      {/* Main Charts Section */}
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        {/* Financial Overview - Larger on desktop */}
        <div className="xl:col-span-3">
          <FinancialOverviewChart data={financialData} />
        </div>
        
        {/* Overdue Payments - Sidebar on desktop */}
        <div className="xl:col-span-1">
          <OverduePaymentsList athletes={overdueAthletes} />
        </div>
      </div>

      {/* Secondary Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ExpenseBreakdownChart data={financialData} />
        <IncomeSourcesChart data={financialData} />
      </div>
    </div>
  );
}
