import { Providers } from './providers'
import './globals.css'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Standard favicon */}
        <link rel="icon" href="/favicon.ico" sizes="16x16 32x32 48x48" />
        <link rel="icon" href="/icons/favicon-32x32.png" sizes="32x32" type="image/png" />
        <link rel="icon" href="/icons/favicon-16x16.png" sizes="16x16" type="image/png" />
        <link rel="icon" href="/icons/favicon.svg" type="image/svg+xml" />
        
        {/* Apple Touch Icons */}
        <link rel="apple-touch-icon" href="/icons/favicon-192x192.png" />
        
        {/* Android/Chrome */}
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#3e464c" />
        
        {/* Microsoft tiles */}
        <meta name="msapplication-TileColor" content="#3e464c" />
        <meta name="msapplication-TileImage" content="/icons/favicon-192x192.png" />
        
        {/* Safari pinned tab */}
        <link rel="mask-icon" href="/icons/favicon.svg" color="#3e464c" />
      </head>
      <body>
        <Providers>{children}</Providers>
      </body>
    </html>
  )
}