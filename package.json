{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:seed": "tsx src/db/seed.ts", "process-payments": "tsx scripts/process-payments.ts", "process-payments:help": "tsx scripts/process-payments.ts --help", "test-overdue-payments": "tsx scripts/test-overdue-payments.ts", "check-payment-status": "tsx scripts/check-payment-status.ts", "test-sms-translations": "tsx scripts/test-sms-translations.ts"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@netgsm/sms": "^1.1.10", "@next/swc-wasm-nodejs": "15.2.4", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@tanstack/react-query": "^5.77.1", "@tanstack/react-table": "^8.13.2", "@types/js-cookie": "^3.0.6", "@types/node": "20.6.2", "@types/node-cron": "^3.0.11", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "accept-language": "^3.0.20", "autoprefixer": "10.4.15", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "embla-carousel-react": "^8.3.0", "encoding": "^0.1.13", "eslint": "8.49.0", "eslint-config-next": "15.2.4", "exceljs": "^4.4.0", "framer-motion": "^11.1.18", "i18next": "^23.16.8", "i18next-browser-languagedetector": "^7.2.2", "i18next-http-backend": "^2.7.3", "input-otp": "^1.2.4", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "lucide-react": "^0.446.0", "next": "15.5.2", "next-auth": "4.24.10", "next-i18next": "^15.4.2", "next-themes": "^0.3.0", "node-cron": "^4.1.0", "pg": "^8.16.0", "postcss": "8.4.31", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-dom": "18.2.0", "react-hook-form": "^7.53.0", "react-i18next": "^14.1.3", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "vaul": "^0.9.9", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.23.8"}, "devDependencies": {"@types/pg": "^8.15.2", "drizzle-kit": "^0.31.1", "tsx": "^4.19.4"}}