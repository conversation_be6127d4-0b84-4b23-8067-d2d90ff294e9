'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { extractTenantIdFromSession } from '@/lib/session-utils';

interface TenantInfo {
  id: string;
  name: string;
}

export function useTenantInfo() {
  const { data: session, status } = useSession();
  const [tenantInfo, setTenantInfo] = useState<TenantInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchTenantInfo() {
      try {
        setLoading(true);
        setError(null);

        if (!session) {
          setTenantInfo(null);
          return;
        }

        const tenantId = extractTenantIdFromSession(session);
        
        if (!tenantId) {
          setTenantInfo(null);
          return;
        }

        // Fetch tenant information using actions
        const { getTenantById } = await import('@/lib/actions/tenants');
        const tenant = await getTenantById(tenantId);
        
        setTenantInfo({
          id: tenant.id,
          name: tenant.name
        });
      } catch (err) {
        console.error('Error fetching tenant info:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setTenantInfo(null);
      } finally {
        setLoading(false);
      }
    }

    fetchTenantInfo();
  }, [session, status]);

  return {
    tenantInfo,
    loading,
    error,
    tenantId: tenantInfo?.id || null,
    tenantName: tenantInfo?.name || null
  };
}