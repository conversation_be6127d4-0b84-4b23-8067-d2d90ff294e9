"use client";

import { useState, useEffect, useReducer } from 'react';
import { useTranslation } from 'react-i18next';
import {
  School,
  Users,
  BarChart3,
  User,
  CreditCard,
  MapPin,
  MessageSquare
} from "lucide-react";
import { TurkishLiraIcon } from "@/components/ui/turkish-lira-icon";

export interface DashboardCardConfig {
  id: string;
  title: string;
  description: string;
  icon: any;
  href: string;
  bgColor: string;
  textColor: string;
}

const COOKIE_NAME = 'dashboard-preferences';
const DEFAULT_CARDS = ['athletes', 'payments', 'expenses', 'sms'];

// Reducer for managing dashboard state
interface DashboardState {
  selectedCards: string[];
  isLoading: boolean;
  version: number; // Force re-renders
}

type DashboardAction = 
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_SELECTED_CARDS'; payload: string[] }
  | { type: 'RESET_TO_DEFAULT' }
  | { type: 'FORCE_UPDATE' };

function dashboardReducer(state: DashboardState, action: DashboardAction): DashboardState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_SELECTED_CARDS':
      console.log('🔄 Reducer: Setting cards to', action.payload);
      return { 
        ...state, 
        selectedCards: action.payload,
        version: state.version + 1 
      };
    case 'RESET_TO_DEFAULT':
      console.log('🔄 Reducer: Resetting to defaults');
      return { 
        ...state, 
        selectedCards: [...DEFAULT_CARDS],
        version: state.version + 1 
      };
    case 'FORCE_UPDATE':
      return { ...state, version: state.version + 1 };
    default:
      return state;
  }
}

export function useDashboardPreferences() {
  const { t } = useTranslation(['shared', 'dashboard']);
  
  const [state, dispatch] = useReducer(dashboardReducer, {
    selectedCards: DEFAULT_CARDS,
    isLoading: true,
    version: 0
  });

  // All available cards configuration
  const allCards: DashboardCardConfig[] = [
    {
      id: 'schools',
      title: t("nav.schools", { ns: 'shared' }),
      description: t("dashboard.cards.schools", { ns: 'dashboard' }),
      icon: School,
      href: '/schools',
      bgColor: 'bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200',
      textColor: 'text-blue-900'
    },
    {
      id: 'instructors',
      title: t("nav.instructors", { ns: 'shared' }),
      description: t("dashboard.cards.instructors", { ns: 'dashboard' }),
      icon: Users,
      href: '/instructors',
      bgColor: 'bg-gradient-to-br from-green-50 to-green-100 border border-green-200',
      textColor: 'text-green-900'
    },
    {
      id: 'teams',
      title: t("nav.teams", { ns: 'shared' }),
      description: t("dashboard.cards.teams", { ns: 'dashboard' }),
      icon: BarChart3,
      href: '/teams',
      bgColor: 'bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200',
      textColor: 'text-purple-900'
    },
    {
      id: 'athletes',
      title: t("nav.athletes", { ns: 'shared' }),
      description: t("dashboard.cards.athletes", { ns: 'dashboard' }),
      icon: User,
      href: '/athletes',
      bgColor: 'bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200',
      textColor: 'text-orange-900'
    },
    {
      id: 'payments',
      title: t("nav.payments", { ns: 'shared' }),
      description: t("dashboard.cards.payments", { ns: 'dashboard' }),
      icon: CreditCard,
      href: '/payments',
      bgColor: 'bg-gradient-to-br from-emerald-50 to-emerald-100 border border-emerald-200',
      textColor: 'text-emerald-900'
    },
    {
      id: 'expenses',
      title: t("nav.expenses", { ns: 'shared' }),
      description: t("dashboard.cards.expenses", { ns: 'dashboard' }),
      icon: TurkishLiraIcon,
      href: '/expenses',
      bgColor: 'bg-gradient-to-br from-red-50 to-red-100 border border-red-200',
      textColor: 'text-red-900'
    },
    {
      id: 'facilities',
      title: t("nav.facilities", { ns: 'shared' }),
      description: t("dashboard.cards.facilities", { ns: 'dashboard' }),
      icon: MapPin,
      href: '/facilities',
      bgColor: 'bg-gradient-to-br from-teal-50 to-teal-100 border border-teal-200',
      textColor: 'text-teal-900'
    },
    {
      id: 'sms',
      title: t("nav.sms", { ns: 'shared' }),
      description: t("dashboard.cards.sms", { ns: 'dashboard' }),
      icon: MessageSquare,
      href: '/sms',
      bgColor: 'bg-gradient-to-br from-indigo-50 to-indigo-100 border border-indigo-200',
      textColor: 'text-indigo-900'
    }
  ];

  // Load preferences from cookies on mount
  useEffect(() => {
    const savedPreferences = getCookie(COOKIE_NAME);
    if (savedPreferences) {
      try {
        const parsed = JSON.parse(savedPreferences);
        if (Array.isArray(parsed) && parsed.length === 4) {
          dispatch({ type: 'SET_SELECTED_CARDS', payload: parsed });
        }
      } catch (error) {
        console.warn('Failed to parse dashboard preferences from cookie:', error);
      }
    }
    dispatch({ type: 'SET_LOADING', payload: false });
  }, []);

  // Get selected cards configuration
  const getSelectedCards = (): DashboardCardConfig[] => {
    return state.selectedCards
      .map((cardId: string) => allCards.find(card => card.id === cardId))
      .filter((card: DashboardCardConfig | undefined): card is DashboardCardConfig => card !== undefined);
  };

  // Update selected cards and save to cookies
  const updateSelectedCards = (newSelectedCards: string[]) => {
    if (newSelectedCards.length !== 4) {
      throw new Error('Exactly 4 cards must be selected');
    }
    
    console.log('🔄 Updating selected cards:', newSelectedCards);
    
    dispatch({ type: 'SET_SELECTED_CARDS', payload: newSelectedCards });
    setCookie(COOKIE_NAME, JSON.stringify(newSelectedCards), 365); // Expire in 1 year
  };

  // Reset to default cards
  const resetToDefault = () => {
    console.log('🔄 Resetting to defaults:', DEFAULT_CARDS);
    
    dispatch({ type: 'RESET_TO_DEFAULT' });
    // Delete the cookie to truly reset
    deleteCookie(COOKIE_NAME);
  };

  return {
    allCards,
    selectedCards: state.selectedCards,
    getSelectedCards,
    updateSelectedCards,
    resetToDefault,
    isLoading: state.isLoading,
    defaultCards: DEFAULT_CARDS,
    updateKey: state.version // Add this to trigger re-renders
  };
}

// Cookie utility functions
function setCookie(name: string, value: string, days: number) {
  const expires = new Date();
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
  document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
}

function getCookie(name: string): string | null {
  if (typeof document === 'undefined') return null;
  
  const nameEQ = name + '=';
  const ca = document.cookie.split(';');
  
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
  }
  
  return null;
}

function deleteCookie(name: string) {
  if (typeof document === 'undefined') return;
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax`;
}