'use client';

import { ColumnDef } from "@tanstack/react-table";
import { Athlete } from "@/lib/types";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { BadgeStatus } from "@/components/ui/badge-status";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Eye, MoreHorizontal, Pencil, Trash, UserCheck, UserX } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { deleteAthlete } from "@/lib/api";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useState, useEffect, useMemo, useCallback, memo } from "react";
import { AthleteActivationDialog } from "@/components/athletes/athlete-activation-dialog";
import { AthleteDeactivationDialog } from "@/components/athletes/athlete-deactivation-dialog";
import {useToast} from "@/hooks/use-toast";

const AthleteActivationMenuItem = memo(({ athlete }: { athlete: Athlete }) => {
  const { t } = useSafeTranslation();
  const [isActivationDialogOpen, setIsActivationDialogOpen] = useState(false);
  const [isDeactivationDialogOpen, setIsDeactivationDialogOpen] = useState(false);

  const handleClick = useCallback(() => {
    if (athlete.status === 'active') {
      setIsDeactivationDialogOpen(true);
    } else {
      setIsActivationDialogOpen(true);
    }
  }, [athlete.status]);

  const handleActivationOpenChange = useCallback((open: boolean) => {
    setIsActivationDialogOpen(open);
  }, []);

  const handleDeactivationComplete = useCallback(() => {
    setIsDeactivationDialogOpen(false);
    // Refresh the page to show updated data
    window.location.reload();
  }, []);

  const handleActivationSuccess = useCallback(() => {
    setIsActivationDialogOpen(false);
    // Refresh the page to show updated data
    window.location.reload();
  }, []);

  return (
    <>
      <DropdownMenuItem
        className={athlete.status === 'active' ? "text-yellow-600 focus:text-yellow-600" : "text-green-600 focus:text-green-600"}
        onSelect={(e) => {
          e.preventDefault();
          handleClick();
        }}
      >
        {athlete.status === 'active' ? (
          <>
            <UserX className="mr-2 h-4 w-4" />
            {t('athletes.actions.deactivateAthlete', 'Deactivate Athlete')}
          </>
        ) : (
          <>
            <UserCheck className="mr-2 h-4 w-4" />
            {t('athletes.actions.activateAthlete', 'Activate Athlete')}
          </>
        )}
      </DropdownMenuItem>
      
      {/* Activation Dialog */}
      {isActivationDialogOpen && (
        <AthleteActivationDialog 
          athlete={athlete} 
          isOpen={isActivationDialogOpen}
          onOpenChange={handleActivationOpenChange}
          onSuccess={handleActivationSuccess}
        />
      )}
      
      {/* Deactivation Dialog */}
      <AthleteDeactivationDialog
        athleteId={athlete.id}
        athleteName={`${athlete.name} ${athlete.surname}`}
        open={isDeactivationDialogOpen}
        onOpenChange={setIsDeactivationDialogOpen}
        onDeactivationComplete={handleDeactivationComplete}
      />
    </>
  );
});

AthleteActivationMenuItem.displayName = 'AthleteActivationMenuItem';

const DeleteAthleteDialog = ({ athlete }: { athlete: Athlete }) => {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      const result = await deleteAthlete(athlete.id);
      if(!result.success){
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'athletes.messages.deleteError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }else{
        toast({
          title: t('common.success'),
          description: t('athletes.messages.deleteSuccess', 'Athlete deleted successfully'),
        });
      }

      router.refresh();
    } catch (error) {
      console.error("Error deleting athlete:", error);
      toast({
        title: t('common.error'),
        description: t('athletes.messages.deleteError', 'Failed to delete athlete'),
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <DropdownMenuItem
          className="text-destructive focus:text-destructive"
          onSelect={(e) => e.preventDefault()}
        >
          <Trash className="mr-2 h-4 w-4" />
          {t('athletes.actions.deleteAthlete', 'Delete Athlete')}
        </DropdownMenuItem>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {t('athletes.deleteDialog.title', 'Delete Athlete')}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {t('athletes.deleteDialog.description', 
              `Are you sure you want to delete ${athlete.name} ${athlete.surname}? This action cannot be undone.`
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>
            {t('common.actions.cancel', 'Cancel')}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isDeleting 
              ? t('common.actions.deleting', 'Deleting...') 
              : t('common.actions.delete', 'Delete')
            }
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export const useColumns = (): ColumnDef<Athlete>[] => {
  const { t } = useSafeTranslation();

  return useMemo(() => [
    {
      accessorKey: "name",
      header: t('athletes.table.name'),
      cell: ({ row }) => (
        <div>
          <div className="font-medium">
            {row.original.name} {row.original.surname}
          </div>
          <div className="text-xs text-muted-foreground">
            {t('athletes.details.nationalId')}: {row.original.nationalId}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "parentName",
      header: t('athletes.table.parent'),
      cell: ({ row }) => (
        <div>
          <div className="font-medium">
            {row.original.parentName} {row.original.parentSurname}
          </div>
          <div className="text-xs text-muted-foreground">
            {row.original.parentPhone}
          </div>
        </div>
      ),
    },
    {
      accessorKey: "birthDate",
      header: t('athletes.table.birthDate'),
      cell: ({ row }) => {
        try {
          return format(new Date(row.original.birthDate), "dd/MM/yyyy");
        } catch {
          return row.original.birthDate;
        }
      },
    },
    {
      accessorKey: "teams",
      header: t('athletes.table.teams'),
      cell: ({ row }) => (
        <div>
          {row.original.teams && row.original.teams.length > 0 ? (
            <div className="text-xs">
              {t('athletes.messages.teamCount', { count: row.original.teams.length })}
            </div>
          ) : (
            <div className="text-xs text-muted-foreground">{t('athletes.messages.noTeams')}</div>
          )}
        </div>
      ),
    },
    {
      accessorKey: "balance",
      header: t('athletes.table.balance'),
      cell: ({ row }) => {
        const balance = parseFloat(row.original.balance);
        return (
          <div className={balance < 0 ? "text-red-500 font-medium" : "text-green-500 font-medium"}>
            {balance.toFixed(2)} {t('common.currency')}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: t('athletes.table.status'),
      cell: ({ row }) => <BadgeStatus status={row.original.status} />,
    },
    {
      id: "actions",
      header: t('athletes.table.actions'),
      cell: ({ row }) => {
        const athlete = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">{t('athletes.actions.openMenu')}</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>{t('athletes.table.actions')}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href={`/athletes/${athlete.id}`} className="flex items-center">
                  <Eye className="mr-2 h-4 w-4" />
                  {t('athletes.actions.viewDetails')}
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href={`/athletes/${athlete.id}/edit`} className="flex items-center">
                  <Pencil className="mr-2 h-4 w-4" />
                  {t('athletes.actions.editAthlete')}
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <AthleteActivationMenuItem athlete={athlete} />
              <DropdownMenuSeparator />
              <DeleteAthleteDialog athlete={athlete} />
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ], [t]);
}