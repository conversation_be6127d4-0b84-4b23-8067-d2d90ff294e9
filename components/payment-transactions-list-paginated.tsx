"use client";

import { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { PlusCircle, Filter, Download } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { createPaymentTransactionColumns } from "@/components/payment-transactions-table-columns";
import { AddTransactionDialog } from "@/components/add-transaction-dialog";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { Label } from "@/components/ui/label";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { GenericListPage } from "@/components/ui/generic-list-page";
import { PaymentTransaction } from "@/lib/types";

interface PaymentTransactionsListPaginatedProps {
  initialData: PaymentTransaction[];
  initialPagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  athletes: any[];
  initialSearchParams: {
    page?: string;
    limit?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    transactionMethod?: string;
    athleteId?: string;
    paymentId?: string;
    fromDate?: string;
    toDate?: string;
  };
}

export function PaymentTransactionsListPaginated({
  initialData,
  initialPagination,
  athletes,
  initialSearchParams
}: PaymentTransactionsListPaginatedProps) {
  const { t } = useSafeTranslation();
  const router = useRouter();

  const transactionColumns = useMemo(() => createPaymentTransactionColumns(t), [t]);

  // Local state for filters
  const [filters, setFilters] = useState({
    transactionMethod: initialSearchParams.transactionMethod || "",
    fromDate: initialSearchParams.fromDate || "",
    toDate: initialSearchParams.toDate || "",
  });
  const [showFilters, setShowFilters] = useState(
    !!(initialSearchParams.transactionMethod ||
       initialSearchParams.fromDate || initialSearchParams.toDate)
  );

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    const searchParams = new URLSearchParams(window.location.search);

    // Always preserve the tab parameter
    searchParams.set('tab', 'transactions');

    // Update search params with filter values
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== 'all' && value.trim() !== '') {
        searchParams.set(key, value);
      } else {
        searchParams.delete(key);
      }
    });

    // Reset to page 1 when applying filters
    searchParams.set('page', '1');

    // Use router.replace for smooth navigation without page refresh
    router.replace(`/payments?${searchParams.toString()}`);
  };

  const handleClearFilters = () => {
    setFilters({
      transactionMethod: "",
      fromDate: "",
      toDate: "",
    });

    const searchParams = new URLSearchParams(window.location.search);

    // Remove filter params
    ['transactionMethod', 'fromDate', 'toDate'].forEach(key => {
      searchParams.delete(key);
    });

    // Reset to page 1
    searchParams.set('page', '1');

    router.replace(`/payments?${searchParams.toString()}`);
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== "");

  // Create filters component (matching payment listing structure exactly)
  const filtersComponent = showFilters ? (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">{t('common.actions.filter')}</h3>
              <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFilters(false)}
              >
                  {t('common.actions.cancel')}
              </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="transactionMethodFilter">{t('payments.transactions.method')}</Label>
              <Select
                value={filters.transactionMethod}
                onValueChange={(value) => handleFilterChange('transactionMethod', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('payments.selectMethod')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('common.all')}</SelectItem>
                  <SelectItem value="cash">{t('payments.transactionMethods.cash')}</SelectItem>
                  <SelectItem value="bank_transfer">{t('payments.transactionMethods.bank_transfer')}</SelectItem>
                  <SelectItem value="credit_card">{t('payments.transactionMethods.credit_card')}</SelectItem>
                  <SelectItem value="existing_balance">{t('payments.transactionMethods.existing_balance')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fromDateFilter">{t('payments.fromDate')}</Label>
              <DatePicker
                date={filters.fromDate ? new Date(filters.fromDate) : undefined}
                onSelect={(date) => {
                  if (date) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    handleFilterChange('fromDate', `${year}-${month}-${day}`);
                  } else {
                    handleFilterChange('fromDate', '');
                  }
                }}
                placeholder={t('payments.filters.selectFromDate')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="toDateFilter">{t('payments.toDate')}</Label>
              <DatePicker
                date={filters.toDate ? new Date(filters.toDate) : undefined}
                onSelect={(date) => {
                  if (date) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    handleFilterChange('toDate', `${year}-${month}-${day}`);
                  } else {
                    handleFilterChange('toDate', '');
                  }
                }}
                placeholder={t('payments.filters.selectToDate')}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button onClick={handleApplyFilters}>
              {t('common.actions.apply')}
            </Button>
            <Button variant="outline" onClick={handleClearFilters}>
              {t('common.actions.clear')}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  ) : (
    <Button
      variant="outline"
      onClick={() => setShowFilters(true)}
    >
      <Filter className="mr-2 h-4 w-4" />
      {t('common.actions.filter')}
    </Button>
  );

  // Create actions for the header (matching payment listing structure)
  const actions = (
    <div className="flex gap-2">
      <AddTransactionDialog
        athletes={athletes}
        onSuccess={() => {
          // Refresh the page to show new transaction
          router.refresh();
        }}
      />
    </div>
  );

  return (
    <GenericListPage
      data={initialData}
      pagination={initialPagination}
      columns={transactionColumns}
      title={t('payments.transactions.title')}
      description={t('payments.transactions.description')}
      basePath="/payments"
      initialSearchParams={{...initialSearchParams, tab: 'transactions'}}
      actions={actions}
      filters={filtersComponent}
      searchPlaceholder={t('payments.placeholders.searchTransactions')}
      paginationOptions={{
        defaultSortBy: 'transactionDate',
        defaultSortOrder: 'desc',
        searchMinLength: 3,
        searchDebounceMs: 500,
      }}
    />
  );
}
