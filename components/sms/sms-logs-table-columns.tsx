"use client";

import { ColumnDef } from "@tanstack/react-table";
import { format, isValid } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { MessageSquare, User, Users, Phone } from "lucide-react";

export interface SmsLog {
  id: string;
  type: 'payment_reminder' | 'team_message' | 'custom';
  status: 'pending' | 'sent' | 'failed' | 'cancelled';
  receiver: string;
  message: string;
  senderIdentifier: string;
  sentAt: Date | string;
  creditsUsed: number;
  senderType: 'user' | 'system';
  senderId?: string;
  athlete?: {
    id: string;
    name: string;
    surname: string;
  };
  team?: {
    id: string;
    name: string;
  };

}

const TypeCell = ({ type }: { type: string }) => {
  const { t } = useSafeTranslation();
  
  const typeConfig = {
    payment_reminder: {
      icon: MessageSquare,
      color: "bg-blue-100 text-blue-800 hover:bg-blue-200",
      label: t('sms:common.types.paymentReminder')
    },
    team_message: {
      icon: Users,
      color: "bg-green-100 text-green-800 hover:bg-green-200", 
      label: t('sms:common.types.teamMessage')
    },
    custom: {
      icon: User,
      color: "bg-purple-100 text-purple-800 hover:bg-purple-200",
      label: t('sms:common.types.custom')
    }
  };

  const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.custom;
  const Icon = config.icon;

  return (
    <Badge className={config.color}>
      <Icon className="mr-1 h-3 w-3" />
      {config.label}
    </Badge>
  );
};

const StatusCell = ({ status }: { status: string }) => {
  const { t } = useSafeTranslation();
  
  const statusColors = {
    pending: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
    sent: "bg-green-100 text-green-800 hover:bg-green-200",
    failed: "bg-red-100 text-red-800 hover:bg-red-200",
    cancelled: "bg-gray-100 text-gray-800 hover:bg-gray-200",
  };

  return (
    <Badge className={statusColors[status as keyof typeof statusColors] || "bg-gray-100 text-gray-800 hover:bg-gray-200"}>
      {t(`sms:status.${status}`, status)}
    </Badge>
  );
};

const ReceiverCell = ({ receiver, athlete }: { receiver: string; athlete?: SmsLog['athlete'] }) => {
  return (
    <div className="space-y-1">
      <div className="flex items-center space-x-2">
        <Phone className="h-3 w-3 text-muted-foreground" />
        <span className="font-mono text-sm">{receiver}</span>
      </div>
      {athlete && (
        <div className="text-xs text-muted-foreground">
          {athlete.name} {athlete.surname}
        </div>
      )}
    </div>
  );
};

const MessageCell = ({ message }: { message: string }) => {
  const truncatedMessage = message.length > 100 ? `${message.substring(0, 100)}...` : message;
  
  return (
    <div className="max-w-xs">
      <div className="text-sm" title={message}>
        {truncatedMessage}
      </div>
    </div>
  );
};

const DateCell = ({ date }: { date: Date | string }) => {
  const { t } = useSafeTranslation();
  
  if (!date) {
    return <span className="text-muted-foreground text-sm">-</span>;
  }
  
  // Handle both string and Date objects
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (!isValid(dateObj)) {
    return <span className="text-muted-foreground text-sm">-</span>;
  }
  
  return (
    <div className="text-sm">
      {format(dateObj, 'dd/MM/yyyy HH:mm')}
    </div>
  );
};

const CreditsCell = ({ credits }: { credits: number }) => {
  const { t } = useSafeTranslation();
  
  return (
    <div className="text-sm font-medium">
      {credits} {t('sms:common.credits')}
    </div>
  );
};

const SenderCell = ({ senderType, senderId }: { senderType: string; senderId?: string }) => {
  const { t } = useSafeTranslation();
  
  const senderConfig = {
    user: {
      color: "bg-blue-100 text-blue-800 hover:bg-blue-200",
      label: t('common.user', 'User')
    },
    system: {
      color: "bg-gray-100 text-gray-800 hover:bg-gray-200", 
      label: t('common.system', 'System')
    }
  };

  const config = senderConfig[senderType as keyof typeof senderConfig] || senderConfig.user;

  return (
    <Badge className={config.color}>
      {config.label}
    </Badge>
  );
};

export const createSmsLogsColumns = (t: any): ColumnDef<SmsLog>[] => [
  {
    accessorKey: "type",
    header: () => t('sms:common.type', 'Type'),
    cell: ({ row }) => <TypeCell type={row.original.type} />,
  },
  {
    accessorKey: "status", 
    header: () => t('sms:common.status', 'Status'),
    cell: ({ row }) => <StatusCell status={row.original.status} />,
  },
  {
    accessorKey: "receiver",
    header: () => t('sms:common.receiver', 'Receiver'),
    cell: ({ row }) => <ReceiverCell receiver={row.original.receiver} athlete={row.original.athlete} />,
  },
  {
    accessorKey: "message",
    header: () => t('sms:common.message', 'Message'),
    cell: ({ row }) => <MessageCell message={row.original.message} />,
  },
  {
    accessorKey: "sentAt",
    header: () => t('sms:common.sentAt', 'Sent At'),
    cell: ({ row }) => <DateCell date={row.original.sentAt} />,
  },
  {
    accessorKey: "creditsUsed",
    header: () => t('sms:common.credits', 'Credits'),
    cell: ({ row }) => <CreditsCell credits={row.original.creditsUsed} />,
  },
  {
    accessorKey: "senderType",
    header: () => t('sms:common.sender', 'Sender'),
    cell: ({ row }) => <SenderCell senderType={row.original.senderType} senderId={row.original.senderId} />,
  },
];