'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, CheckCircle, Zap, Wallet } from 'lucide-react';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import { useSms } from '@/contexts/sms-context';

interface SmsBalanceDisplayProps {
  className?: string;
  showCard?: boolean;
  variant?: 'default' | 'compact';
}

export default function SmsBalanceDisplay({
  className = '',
  showCard = true,
  variant = 'default'
}: SmsBalanceDisplayProps) {
  const { t } = useSafeTranslation();
  const { balance, refreshBalance, ensureBalanceInitialized, dataRefreshTrigger } = useSms();
  const [loading, setLoading] = useState(false);

  // Initialize balance when component mounts (lazy loading)
  useEffect(() => {
    const initializeBalance = async () => {
      setLoading(true);
      await ensureBalanceInitialized();
      setLoading(false);
    };

    void initializeBalance();
  }, [ensureBalanceInitialized]); // Include dependency

  // Refresh balance when dataRefreshTrigger changes (when SMS operations complete)
  useEffect(() => {
    if (dataRefreshTrigger > 0) {
      setLoading(true);
      refreshBalance().finally(() => setLoading(false));
    }
  }, [refreshBalance, dataRefreshTrigger]);

  const getBalanceStatus = () => {
    // Show loading if we're actively refreshing or if balance is null and context hasn't loaded yet
    if (loading || balance === null) return 'loading';
    if (balance === 0) return 'empty';
    if (balance < 50) return 'low';
    return 'sufficient';
  };



  const getBalanceIcon = () => {
    const status = getBalanceStatus();
    switch (status) {
      case 'empty': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'low': return <Zap className="h-4 w-4 text-amber-500" />;
      case 'sufficient': return <CheckCircle className="h-4 w-4 text-green-500" />;
      default: return <Wallet className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusMessage = () => {
    const status = getBalanceStatus();
    switch (status) {
      case 'empty': return t('sms:balance.warnings.empty');
      case 'low': return t('sms:balance.warnings.low');
      case 'sufficient': return t('sms:balance.status.sufficient');
      default: return t('sms:common.loading');
    }
  };

  const getBalanceBadgeVariant = (): "default" | "secondary" | "destructive" | "outline" => {
    const status = getBalanceStatus();
    switch (status) {
      case 'empty': return 'destructive';
      case 'low': return 'secondary';
      case 'sufficient': return 'default';
      default: return 'outline';
    }
  };

  // Compact variant for smaller spaces
  const compactContent = (
    <div className="flex items-center justify-between">
      <div className="flex items-center space-x-3">
        {getBalanceIcon()}
        <div>
          <div className="flex items-baseline space-x-2">
            <span className="text-xl font-bold">
              {loading || balance === null ? '...' : balance.toLocaleString('tr-TR')}
            </span>
            <span className="text-sm text-muted-foreground">
              {t('sms:balance.credits')}
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            {t('sms:balance.current.credits')}
          </p>
        </div>
      </div>

      <Badge variant={getBalanceBadgeVariant()} className="text-xs">
        {loading || balance === null ? t('sms:common.loading') : t(`sms:balance.status.${getBalanceStatus()}`)}
      </Badge>
    </div>
  );

  // Simple, consistent variant that matches other cards
  const fullContent = (
    <div>
      {/* Balance Display */}
      <div className="flex items-center justify-between">
        <div>
          <div className="text-2xl font-bold">
            {loading || balance === null ? '...' : balance.toLocaleString('tr-TR')}
          </div>
          <p className="text-xs text-muted-foreground">
            {t('sms:balance.credits')}
          </p>
        </div>

        <Badge variant={getBalanceBadgeVariant()} className="text-xs">
          {loading || balance === null ? t('sms:common.loading') : t(`sms:balance.status.${getBalanceStatus()}`)}
        </Badge>
      </div>

      {/* Status Message */}
      {balance !== null && balance < 50 && (
        <p className="text-xs text-muted-foreground mt-2">
          {getStatusMessage()}
        </p>
      )}
    </div>
  );

  const content = variant === 'compact' ? compactContent : fullContent;

  if (!showCard) {
    return <div className={className}>{content}</div>;
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">
          {t('sms:balance.current.title')}
        </CardTitle>
        {getBalanceIcon()}
      </CardHeader>
      <CardContent>
        {content}
      </CardContent>
    </Card>
  );
}
