"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { MessageSquare, Settings, CreditCard, BarChart3 } from "lucide-react";
import { memo } from "react";

const navigationItems = [
  {
    href: "/sms",
    label: "sms:navigation.overview",
    icon: MessageSquare,
    exact: true,
  },
  {
    href: "/sms/configuration",
    label: "sms:navigation.configuration",
    icon: Settings,
  },
  {
    href: "/sms/balance",
    label: "sms:navigation.balance",
    icon: CreditCard,
  },
  {
    href: "/sms/history",
    label: "sms:navigation.history",
    icon: BarChart3,
  },
];

export const SmsNavigation = memo(function SmsNavigation() {
  const { t } = useSafeTranslation();
  const pathname = usePathname();

  return (
    <nav className="flex space-x-1 border-b">
      {navigationItems.map((item) => {
        const Icon = item.icon;
        const isActive = item.exact 
          ? pathname === item.href
          : pathname.startsWith(item.href);

        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-t-lg border-b-2 transition-colors",
              isActive
                ? "border-primary text-primary bg-primary/5"
                : "border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground/50"
            )}
          >
            <Icon className="h-4 w-4" />
            <span>{t(item.label)}</span>
          </Link>
        );
      })}
    </nav>
  );
});