'use client';

import { useState, useTransition, useEffect, useMemo } from 'react';
import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { Loader2, MessageSquare, AlertTriangle, CheckCircle, Eye, EyeOff, Users, FileText, Send, Info, Search } from 'lucide-react';
import { sendPaymentReminderSms } from '@/lib/actions/sms';
import { Payment } from '@/lib/types';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import { useToast } from '@/hooks/use-toast';
import { useSmsRefresh } from '@/contexts/sms-context';
import { useTenantInfo } from '@/hooks/use-tenant-info';

const paymentReminderSchema = z.object({
  templateType: z.enum(['pending', 'overdue']),
  useCustomTemplate: z.boolean(),
  customTemplate: z.string().max(1000, 'Template must be 1000 characters or less').optional(),
});

type PaymentReminderFormData = z.infer<typeof paymentReminderSchema>;

interface ColorfulPaymentReminderDialogProps {
  payments: Payment[];
  trigger?: React.ReactNode;
  onSuccess?: () => void;
  defaultTemplateType?: 'pending' | 'overdue';
}

export default function ColorfulPaymentReminderDialog({
  payments,
  trigger,
  onSuccess,
  defaultTemplateType = 'pending'
}: ColorfulPaymentReminderDialogProps) {
  const { t, i18n } = useSafeTranslation();
  const { toast } = useToast();
  const { onSmsSuccess } = useSmsRefresh();
  const { tenantName } = useTenantInfo();
  const [open, setOpen] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedPayments, setSelectedPayments] = useState<string[]>([]);
  const [combinePayments, setCombinePayments] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset
  } = useForm<PaymentReminderFormData>({
    resolver: zodResolver(paymentReminderSchema),
    defaultValues: {
      templateType: defaultTemplateType,
      useCustomTemplate: false,
      customTemplate: '',
    }
  });

  const templateType = watch('templateType');
  const useCustomTemplate = watch('useCustomTemplate');
  const customTemplate = watch('customTemplate');

  // Filter payments based on search term and sort selected ones to top
  const filteredPayments = useMemo(() => {
    let filtered = payments;
    
    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      filtered = payments.filter(payment => {
        const athleteName = `${payment.athlete?.name || ''} ${payment.athlete?.surname || ''}`.toLowerCase();
        const parentPhone = (payment.athlete?.parentPhone || '').toLowerCase();
        const teamName = (payment.team?.name || '').toLowerCase();
        const amount = payment.amount?.toString().toLowerCase() || '';
        
        return athleteName.includes(lowerSearchTerm) ||
               parentPhone.includes(lowerSearchTerm) ||
               teamName.includes(lowerSearchTerm) ||
               amount.includes(lowerSearchTerm);
      });
    }
    
    // Sort selected payments to the top
    return filtered.sort((a, b) => {
      const aSelected = selectedPayments.includes(a.id);
      const bSelected = selectedPayments.includes(b.id);
      
      if (aSelected && !bSelected) return -1;
      if (!aSelected && bSelected) return 1;
      return 0; // Keep original order for same selection status
    });
  }, [payments, searchTerm, selectedPayments]);

  // Initialize selected payments when dialog opens
  useEffect(() => {
    if (open && selectedPayments.length === 0 && payments.length > 0) {
      const allPaymentIds = payments.map(p => p.id);
      setSelectedPayments(allPaymentIds);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, payments]); // Intentionally omit selectedPayments.length to prevent infinite loop



  const handlePaymentSelection = (paymentId: string, checked: boolean) => {
    setSelectedPayments(prev => 
      checked 
        ? [...prev, paymentId]
        : prev.filter(id => id !== paymentId)
    );
  };

  const handleSelectAll = () => {
    setSelectedPayments(filteredPayments.map(p => p.id));
  };

  const handleSelectNone = () => {
    const filteredPaymentIds = filteredPayments.map(p => p.id);
    setSelectedPayments(prev => prev.filter(id => !filteredPaymentIds.includes(id)));
  };

  const getSelectedPayments = () => {
    return payments.filter(p => selectedPayments.includes(p.id));
  };

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleClose = () => {
    setOpen(false);
    setCurrentStep(1);
    setShowConfirmation(false);
    setSelectedPayments([]);
    reset();
  };

  const getStepColor = (step: number) => {
    if (step < currentStep) return 'bg-green-600 dark:bg-green-500 text-white border-green-600 dark:border-green-500';
    if (step === currentStep) return 'bg-primary text-primary-foreground border-primary';
    return 'bg-muted text-muted-foreground border-muted';
  };

  const getStepIcon = (step: number) => {
    if (step < currentStep) return <CheckCircle className="w-5 h-5" />;
    if (step === 1) return <Users className="w-5 h-5" />;
    if (step === 2) return <FileText className="w-5 h-5" />;
    if (step === 3) return <Send className="w-5 h-5" />;
    return <div className="w-5 h-5 rounded-full bg-current" />;
  };

  // Template and SMS calculation logic
  const defaultTemplates = {
    pending: t('sms:configuration.templates.pending.default'),
    overdue: t('sms:configuration.templates.overdue.default')
  };

  const calculateSmsCount = (length: number) => {
    if (length === 0) return 0;
    if (length <= 160) return 1;
    if (length <= 306) return 2;
    if (length <= 459) return 3;
    if (length <= 612) return 4;
    return Math.ceil(length / 153);
  };

  const currentTemplate = useCustomTemplate ? customTemplate : defaultTemplates[templateType];
  const templateLength = currentTemplate?.length || 0;
  const smsCount = calculateSmsCount(templateLength);

  const processTemplatePreview = (template: string) => {
    const firstPayment = payments[0];
    const athlete = firstPayment?.athlete;

    // Calculate remaining amount for preview (total - paid)
    let remainingAmount = '0.00';
    if (firstPayment) {
      const paymentTotal = parseFloat(firstPayment.amount);
      const paymentPaid = parseFloat(firstPayment.amountPaid || '0');
      remainingAmount = (paymentTotal - paymentPaid).toFixed(2);
    }

    return template
      .replace(/\{\{parentName\}\}/g, athlete?.parentName || t('sms:configuration.preview.parentName'))
      .replace(/\{\{parentSurname\}\}/g, athlete?.parentSurname || t('sms:configuration.preview.parentName'))
      .replace(/\{\{athleteName\}\}/g, athlete?.name ? `${athlete.name} ${athlete.surname}` : t('sms:configuration.preview.athleteName'))
      .replace(/\{\{amount\}\}/g, `${remainingAmount} TL`)
      .replace(/\{\{paymentDueDate\}\}/g, firstPayment?.dueDate || t('sms:configuration.preview.paymentDueDate'))
      .replace(/\{\{clubName\}\}/g, (tenantName && tenantName.trim()) || t('sms:configuration.preview.clubName'));
  };

  const onSubmit = (data: PaymentReminderFormData) => {
    const selectedPaymentObjects = getSelectedPayments();

    if (selectedPaymentObjects.length === 0) {
      toast({
        title: t('sms:common.error'),
        description: t('sms:common.pleaseSelectAtLeastOne'),
        variant: 'destructive',
      });
      return;
    }

    // This should only be called for navigation between steps
    if (currentStep < 3) {
      nextStep();
    }
  };

  const handleSendSms = () => {
    const selectedPaymentObjects = getSelectedPayments();

    if (selectedPaymentObjects.length === 0) {
      toast({
        title: t('sms:common.error'),
        description: t('sms:common.pleaseSelectAtLeastOne'),
        variant: 'destructive',
      });
      return;
    }

    // Show confirmation dialog when Send SMS button is clicked
    setShowConfirmation(true);
  };

  const confirmSend = () => {
    const selectedPaymentObjects = getSelectedPayments();
    
    startTransition(async () => {
      try {
        const paymentIds = selectedPaymentObjects.map(p => p.id);
        
        const result = await sendPaymentReminderSms({
          paymentIds,
          templateType,
          customTemplate: useCustomTemplate ? customTemplate : undefined,
          combinePayments,
          language: i18n.language || 'en',
        });

        if (result.success) {
          const sentCount = result.data?.sentCount || 0;
          toast({
            title: t('common.success'),
            description: sentCount === 1
                ? t('sms:sending.paymentReminder.successSingle')
                : t('sms:sending.paymentReminder.successPlural').replace(/{count}/g, sentCount.toString()),
          });
          handleClose();
          onSuccess?.();
          onSmsSuccess(); // Refresh balance and logs
        } else {
          toast({
            title: t('common.error'),
            description: result.error || t('sms:sending.paymentReminder.error'),
            variant: 'destructive',
          });
        }
      } catch (error) {
        console.error('Error sending payment reminder SMS:', error);
        toast({
          title: t('common.error'),
          description: t('sms:sending.paymentReminder.error'),
          variant: 'destructive',
        });
      }
    });
  };

  if (payments.length === 0) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <MessageSquare className="h-4 w-4 mr-2" />
            Send SMS Reminder
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center space-x-2">
            <MessageSquare className="h-6 w-6 text-primary" />
            <span className="text-xl font-bold text-foreground">
              {t('sms:sending.paymentReminder.title')}
            </span>
          </DialogTitle>
          
          {/* Colorful Step Indicator */}
          <div className="flex items-center justify-center space-x-6 mt-6 mb-4 p-4 bg-muted/50 dark:bg-muted/30 rounded-lg border">
            <div className="flex items-center space-x-3">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${getStepColor(1)}`}>
                {getStepIcon(1)}
              </div>
              <div className="text-left">
                <div className={`text-sm font-bold ${currentStep === 1 ? 'text-primary' : currentStep > 1 ? 'text-green-600 dark:text-green-400' : 'text-muted-foreground'}`}>
                  {t('sms:steps.step')} 1
                </div>
                <div className={`text-xs ${currentStep === 1 ? 'text-primary' : currentStep > 1 ? 'text-green-600 dark:text-green-400' : 'text-muted-foreground'}`}>
                  {t('sms:steps.selectRecipients')}
                </div>
              </div>
            </div>
            
            <div className={`w-12 h-1 rounded-full transition-all duration-300 ${currentStep > 1 ? 'bg-green-600 dark:bg-green-500' : 'bg-border'}`}></div>
            
            <div className="flex items-center space-x-3">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${getStepColor(2)}`}>
                {getStepIcon(2)}
              </div>
              <div className="text-left">
                <div className={`text-sm font-bold ${currentStep === 2 ? 'text-primary' : currentStep > 2 ? 'text-green-600 dark:text-green-400' : 'text-muted-foreground'}`}>
                  {t('sms:steps.step')} 2
                </div>
                <div className={`text-xs ${currentStep === 2 ? 'text-primary' : currentStep > 2 ? 'text-green-600 dark:text-green-400' : 'text-muted-foreground'}`}>
                  {t('sms:steps.customizeMessage')}
                </div>
              </div>
            </div>
            
            <div className={`w-12 h-1 rounded-full transition-all duration-300 ${currentStep > 2 ? 'bg-green-600 dark:bg-green-500' : 'bg-border'}`}></div>
            
            <div className="flex items-center space-x-3">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${getStepColor(3)}`}>
                {getStepIcon(3)}
              </div>
              <div className="text-left">
                <div className={`text-sm font-bold ${currentStep === 3 ? 'text-primary' : 'text-muted-foreground'}`}>
                  {t('sms:steps.step')} 3
                </div>
                <div className={`text-xs ${currentStep === 3 ? 'text-primary' : 'text-muted-foreground'}`}>
                  {t('sms:steps.reviewAndSend')}
                </div>
              </div>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <form id="payment-reminder-form" onSubmit={handleSubmit(onSubmit)} className="space-y-6">

            {/* Step 1: Select Recipients */}
            {currentStep === 1 && (
              <div className="space-y-6 p-6 bg-card border border-border rounded-lg">
                <div className="space-y-4">
                  {/* Search Input */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      placeholder={t('common.search.placeholder')}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Badge variant="outline" className="bg-background">
                        {selectedPayments.length} / {filteredPayments.length} {t('sms:common.selected')}
                      </Badge>
                      {searchTerm && (
                        <p className="text-sm text-muted-foreground">
                          {t('common.search.showing')} {filteredPayments.length} {t('common.search.results')}
                        </p>
                      )}
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleSelectAll}
                      >
                        {t('sms:common.selectAll')}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleSelectNone}
                      >
                        {t('sms:common.selectNone')}
                      </Button>
                    </div>
                  </div>

                  <div className="max-h-64 overflow-y-auto bg-background rounded-lg border p-4 space-y-3">
                    {filteredPayments.length === 0 ? (
                      <div className="text-center text-muted-foreground py-4">
                        {searchTerm ? t('common.search.noResults') : t('sms:sending.paymentReminder.noPayments')}
                      </div>
                    ) : (
                      filteredPayments.map((payment) => {
                      const isOverdue = payment.status === 'overdue' || (payment.status === 'partially_paid' && new Date(payment.dueDate) < new Date());
                      const selectedBg = isOverdue ? 'bg-destructive/10 border-destructive/30' : 'bg-primary/10 border-primary/30';
                      const hoverBg = isOverdue ? 'hover:bg-destructive/5' : 'hover:bg-primary/5';

                      return (
                        <div
                          key={`${payment.id}-${selectedPayments.length}`}
                          className={`flex items-center space-x-3 p-3 rounded-lg transition-colors border ${
                            selectedPayments.includes(payment.id)
                              ? `${selectedBg} shadow-sm`
                              : `${hoverBg} border-border`
                          }`}
                        >
                        <Checkbox
                          id={`payment-${payment.id}`}
                          checked={selectedPayments.includes(payment.id)}
                          onCheckedChange={(checked) => handlePaymentSelection(payment.id, checked as boolean)}
                        />
                        <label
                          htmlFor={`payment-${payment.id}`}
                          className="flex-1 flex items-center justify-between text-sm cursor-pointer"
                        >
                          <div className="flex items-center space-x-3">
                            <div>
                              <div className="font-medium text-foreground">
                                {payment.athlete?.name} {payment.athlete?.surname}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {payment.athlete?.parentPhone || 'No phone number'}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-foreground">{payment.amount}</span>
                            <Badge
                              variant={payment.status === 'overdue' ? 'destructive' : 'secondary'}
                              className="text-xs"
                            >
                              {t(`sms:status.${payment.status}`)}
                            </Badge>
                          </div>
                        </label>
                        </div>
                      );
                    }))}
                  </div>

                  {/* Combine Payments Option */}
                  <div className="flex items-center space-x-3 p-4 bg-card rounded-lg border">
                    <Switch
                      id="combinePayments"
                      checked={combinePayments}
                      onCheckedChange={setCombinePayments}
                    />
                    <div className="flex-1">
                      <Label htmlFor="combinePayments" className="text-sm font-medium">
                        {t('sms:sending.paymentReminder.combinePayments')}
                      </Label>
                      <p className="text-xs text-muted-foreground mt-1">
                        {t('sms:sending.paymentReminder.combinePaymentsDescription')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Customize Message */}
            {currentStep === 2 && (
              <div className="space-y-6 p-6 bg-card border border-border rounded-lg">

                <div className="space-y-4">
                  {/* Template Type Display (Read-only) */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">{t('sms:colorful.paymentReminderType')}</Label>
                    <div className="p-3 bg-background rounded-lg border">
                      <div className="flex items-center space-x-2">
                        <Badge
                          variant={templateType === 'overdue' ? 'destructive' : 'secondary'}
                          className="text-sm"
                        >
                          {templateType === 'pending'
                            ? t('sms:colorful.pendingPaymentReminder')
                            : t('sms:colorful.overduePaymentReminder')
                          }
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          ({t('sms:colorful.basedOnSelectedPayments')})
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Custom Template Toggle */}
                  <div className="flex items-center space-x-3 p-4 bg-background rounded-lg border">
                    <Checkbox
                      id="useCustomTemplate"
                      checked={useCustomTemplate}
                      onCheckedChange={(checked) => setValue('useCustomTemplate', checked as boolean)}
                    />
                    <Label htmlFor="useCustomTemplate" className="text-sm font-medium">
                      {t('sms:sending.paymentReminder.customTemplate')}
                    </Label>
                  </div>

                  {/* Template Content */}
                  {useCustomTemplate ? (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">{t('sms:colorful.customMessage')}</Label>
                        <div className="flex items-center space-x-2 text-xs">
                          <span className="text-muted-foreground">
                            {templateLength} characters
                          </span>
                          <span className={`font-medium ${smsCount > 1 ? 'text-orange-600 dark:text-orange-400' : 'text-green-600 dark:text-green-400'}`}>
                            ~{smsCount} SMS
                          </span>
                        </div>
                      </div>
                      <Textarea
                        {...register('customTemplate')}
                        placeholder={t('sms:sending.paymentReminder.customPlaceholder')}
                        className="min-h-[120px]"
                      />
                      {errors.customTemplate && (
                        <p className="text-sm text-destructive">{errors.customTemplate.message}</p>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label className="text-sm font-medium">{t('sms:colorful.defaultTemplate')}</Label>
                        <div className="flex items-center space-x-2 text-xs">
                          <span className="text-muted-foreground">
                            {templateLength} characters
                          </span>
                          <span className={`font-medium ${smsCount > 1 ? 'text-orange-600 dark:text-orange-400' : 'text-green-600 dark:text-green-400'}`}>
                            ~{smsCount} SMS
                          </span>
                        </div>
                      </div>
                      <div className="p-4 bg-background rounded-lg border">
                        <p className="text-sm text-foreground">{defaultTemplates[templateType]}</p>
                      </div>
                    </div>
                  )}

                  {/* Preview */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label className="text-sm font-medium">{t('sms:common.preview')}</Label>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => setPreviewTemplate(!previewTemplate)}
                      >
                        {previewTemplate ? (
                          <>
                            <EyeOff className="h-4 w-4 mr-2" />
                            {t('sms:colorful.hidePreview')}
                          </>
                        ) : (
                          <>
                            <Eye className="h-4 w-4 mr-2" />
                            {t('sms:colorful.showPreview')}
                          </>
                        )}
                      </Button>
                    </div>

                    {previewTemplate && (
                      <div className="p-4 bg-background rounded-lg border">
                        <p className="text-sm text-foreground whitespace-pre-wrap">
                          {processTemplatePreview(currentTemplate || '')}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Review & Send */}
            {currentStep === 3 && (
              <div className="space-y-6 p-6 bg-card border border-border rounded-lg">

                <div className="space-y-4">
                  {/* Summary */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-background p-4 rounded-lg border text-center">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {(() => {
                          const selectedPaymentObjects = getSelectedPayments();
                          if (combinePayments) {
                            const uniqueAthletes = new Set(selectedPaymentObjects.map(p => p.athleteId));
                            return uniqueAthletes.size;
                          } else {
                            return selectedPaymentObjects.length;
                          }
                        })()}
                      </div>
                      <div className="text-sm text-muted-foreground">{t('sms:steps.recipients')}</div>
                    </div>
                    <div className="bg-background p-4 rounded-lg border text-center">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">{smsCount}</div>
                      <div className="text-sm text-muted-foreground">{t('sms:colorful.smsPerRecipient')}</div>
                    </div>
                    <div className="bg-background p-4 rounded-lg border text-center">
                      <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                        {(() => {
                          const selectedPaymentObjects = getSelectedPayments();
                          let recipientCount;
                          if (combinePayments) {
                            const uniqueAthletes = new Set(selectedPaymentObjects.map(p => p.athleteId));
                            recipientCount = uniqueAthletes.size;
                          } else {
                            recipientCount = selectedPaymentObjects.length;
                          }
                          return recipientCount * smsCount;
                        })()}
                      </div>
                      <div className="text-sm text-muted-foreground">{t('sms:colorful.totalSmsCredits')}</div>
                    </div>
                  </div>

                  {/* Credit Usage Info */}
                  <div className="p-3 bg-amber-50 dark:bg-amber-950/50 border border-amber-200 dark:border-amber-800 rounded-lg">
                    <div className="flex items-start space-x-2">
                      <Info className="h-4 w-4 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-sm font-medium text-amber-800 dark:text-amber-200 mb-1">
                          {t('sms:common.estimatedCreditUsage')}
                        </p>
                        <p className="text-xs text-amber-700 dark:text-amber-300">
                          {t('sms:common.creditUsageInfo')}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Message Preview */}
                  <div className="bg-background p-4 rounded-lg border">
                    <Label className="text-sm font-medium mb-2 block">{t('sms:colorful.messagePreview')}</Label>
                    <div className="p-3 bg-muted rounded border">
                      <p className="text-sm text-foreground whitespace-pre-wrap">
                        {processTemplatePreview(currentTemplate || '')}
                      </p>
                    </div>
                  </div>

                  {/* Warning */}
                  <Alert className="border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-950/50">
                    <AlertTriangle className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                    <AlertDescription className="text-orange-800 dark:text-orange-200">
                      <div className="space-y-1">
                        <p className="font-medium">
                          {t('sms:colorful.sendImmediately')}
                        </p>
                        <p className="text-sm">
                          {t('sms:colorful.reviewCarefully')}
                        </p>
                      </div>
                    </AlertDescription>
                  </Alert>
                </div>
              </div>
            )}

          </form>
        </div>

        {/* Navigation Footer */}
        <DialogFooter className="flex-shrink-0 mt-4 pt-4 border-t">
          <div className="flex justify-between w-full">
            <div className="flex space-x-2">
              {currentStep > 1 && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={prevStep}
                  disabled={isPending}
                >
                  {t('sms:steps.previous')}
                </Button>
              )}
            </div>

            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isPending}
              >
                {t('sms:steps.cancel')}
              </Button>

              {currentStep < 3 ? (
                <Button
                  type="button"
                  onClick={nextStep}
                  disabled={currentStep === 1 && selectedPayments.length === 0}
                  className="bg-primary hover:bg-primary/90"
                >
                  {t('sms:steps.nextStep')}
                </Button>
              ) : (
                <Button
                  type="button"
                  onClick={handleSendSms}
                  disabled={isPending || selectedPayments.length === 0}
                  className="bg-green-600 dark:bg-green-700 hover:bg-green-700 dark:hover:bg-green-800 text-white"
                >
                  {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  <Send className="mr-2 h-4 w-4" />
                  {t('sms:sending.paymentReminder.send')} ({selectedPayments.length})
                </Button>
              )}
            </div>
          </div>
        </DialogFooter>
      </DialogContent>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmation} onOpenChange={setShowConfirmation}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              <span>{t('sms:steps.confirmation')}</span>
            </DialogTitle>
            <DialogDescription>
              {t('sms:steps.confirmMessage')}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="p-4 bg-amber-50 dark:bg-amber-950/50 rounded-lg border border-amber-200 dark:border-amber-800">
              <div className="text-sm space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('sms:steps.recipients')}:</span>
                  <span className="font-medium text-foreground">
                    {(() => {
                      const selectedPaymentObjects = getSelectedPayments();
                      if (combinePayments) {
                        const uniqueAthletes = new Set(selectedPaymentObjects.map(p => p.athleteId));
                        return uniqueAthletes.size;
                      } else {
                        return selectedPaymentObjects.length;
                      }
                    })()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">{t('sms:steps.smsCredits')}:</span>
                  <span className="font-medium text-foreground">
                    {(() => {
                      const selectedPaymentObjects = getSelectedPayments();
                      let recipientCount;
                      if (combinePayments) {
                        const uniqueAthletes = new Set(selectedPaymentObjects.map(p => p.athleteId));
                        recipientCount = uniqueAthletes.size;
                      } else {
                        recipientCount = selectedPaymentObjects.length;
                      }
                      return recipientCount * smsCount;
                    })()}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowConfirmation(false)}
              disabled={isPending}
            >
              {t('sms:steps.cancel')}
            </Button>
            <Button
              type="button"
              onClick={confirmSend}
              disabled={isPending}
              className="bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white"
            >
              {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t('sms:steps.yesSend')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}
