"use client";

import { useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Filter } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { createSmsLogsColumns, SmsLog } from "./sms-logs-table-columns";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { Label } from "@/components/ui/label";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { GenericListPage } from "@/components/ui/generic-list-page";

interface SmsLogsListPaginatedProps {
  initialData: SmsLog[];
  initialPagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  initialSearchParams: {
    page?: string;
    limit?: string;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    type?: string;
    status?: string;
    senderType?: string;
    dateFrom?: string;
    dateTo?: string;
  };
}

export function SmsLogsListPaginated({
  initialData,
  initialPagination,
  initialSearchParams
}: SmsLogsListPaginatedProps) {
  const { t } = useSafeTranslation();
  const router = useRouter();

  const smsLogsColumns = useMemo(() => createSmsLogsColumns(t), [t]);

  // Local state for filters
  const [filters, setFilters] = useState({
    type: initialSearchParams.type || "",
    status: initialSearchParams.status || "",
    senderType: initialSearchParams.senderType || "",
    dateFrom: initialSearchParams.dateFrom || "",
    dateTo: initialSearchParams.dateTo || "",
  });
  
  const [showFilters, setShowFilters] = useState(
    !!(initialSearchParams.type ||
       initialSearchParams.status ||
       initialSearchParams.senderType ||
       initialSearchParams.dateFrom || 
       initialSearchParams.dateTo)
  );

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleApplyFilters = () => {
    const searchParams = new URLSearchParams(window.location.search);

    // Update search params with filter values
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== 'all' && value.trim() !== '') {
        searchParams.set(key, value);
      } else {
        searchParams.delete(key);
      }
    });

    // Reset to page 1 when applying filters
    searchParams.set('page', '1');

    // Use router.replace for smooth navigation without page refresh
    router.replace(`/sms/history?${searchParams.toString()}`);
  };

  const handleClearFilters = () => {
    setFilters({
      type: "",
      status: "",
      senderType: "",
      dateFrom: "",
      dateTo: "",
    });

    const searchParams = new URLSearchParams(window.location.search);

    // Remove filter params
    ['type', 'status', 'senderType', 'dateFrom', 'dateTo'].forEach(key => {
      searchParams.delete(key);
    });

    // Reset to page 1
    searchParams.set('page', '1');

    router.replace(`/sms/history?${searchParams.toString()}`);
  };

  const hasActiveFilters = Object.values(filters).some(value => value !== "");

  // Create filters component
  const filtersComponent = showFilters ? (
    <Card>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">{t('common.actions.filter')}</h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(false)}
            >
              {t('common.actions.cancel')}
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="typeFilter">{t('sms:common.type', )}</Label>
              <Select
                value={filters.type}
                onValueChange={(value) => handleFilterChange('type', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('sms:common.selectType')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('common.all')}</SelectItem>
                  <SelectItem value="payment_reminder">{t('sms:common.types.paymentReminder')}</SelectItem>
                  <SelectItem value="team_message">{t('sms:common.types.teamMessage')}</SelectItem>
                  <SelectItem value="custom">{t('sms:common.types.custom')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="statusFilter">{t('sms:common.status')}</Label>
              <Select
                value={filters.status}
                onValueChange={(value) => handleFilterChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('sms:common.selectStatus')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('common.all')}</SelectItem>
                  <SelectItem value="pending">{t('sms:status.pending')}</SelectItem>
                  <SelectItem value="sent">{t('sms:status.sent')}</SelectItem>
                  <SelectItem value="failed">{t('sms:status.failed')}</SelectItem>
                  <SelectItem value="cancelled">{t('sms:status.cancelled')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="senderTypeFilter">{t('sms:common.sender')}</Label>
              <Select
                value={filters.senderType}
                onValueChange={(value) => handleFilterChange('senderType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('sms:common.selectSender')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('common.all')}</SelectItem>
                  <SelectItem value="user">{t('common.user')}</SelectItem>
                  <SelectItem value="system">{t('common.system')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="fromDateFilter">{t('sms:common.fromDate')}</Label>
              <DatePicker
                date={filters.dateFrom ? new Date(filters.dateFrom) : undefined}
                onSelect={(date) => {
                  if (date) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    handleFilterChange('dateFrom', `${year}-${month}-${day}`);
                  } else {
                    handleFilterChange('dateFrom', '');
                  }
                }}
                placeholder={t('sms:common.selectFromDate')}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="toDateFilter">{t('sms:common.toDate')}</Label>
              <DatePicker
                date={filters.dateTo ? new Date(filters.dateTo) : undefined}
                onSelect={(date) => {
                  if (date) {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    handleFilterChange('dateTo', `${year}-${month}-${day}`);
                  } else {
                    handleFilterChange('dateTo', '');
                  }
                }}
                placeholder={t('sms:common.selectToDate', 'Select to date')}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button onClick={handleApplyFilters}>
              {t('common.actions.apply')}
            </Button>
            <Button variant="outline" onClick={handleClearFilters}>
              {t('common.actions.clear')}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  ) : (
    <Button
      variant="outline"
      onClick={() => setShowFilters(true)}
    >
      <Filter className="mr-2 h-4 w-4" />
      {t('common.actions.filter')}
      {hasActiveFilters && (
        <span className="ml-1 rounded-full bg-primary text-primary-foreground px-2 py-0.5 text-xs">
          {Object.values(filters).filter(v => v !== "").length}
        </span>
      )}
    </Button>
  );

  return (
    <GenericListPage
      data={initialData}
      pagination={initialPagination}
      columns={smsLogsColumns}
      title={t('sms:cards.history.title')}
      description={t('sms:cards.history.description')}
      basePath="/sms/history"
      initialSearchParams={initialSearchParams}
      filters={filtersComponent}
      searchPlaceholder={t('sms:cards.history.searchPlaceholder')}
      paginationOptions={{
        defaultSortBy: 'sentAt',
        defaultSortOrder: 'desc',
        searchMinLength: 3,
        searchDebounceMs: 500,
      }}
    />
  );
}