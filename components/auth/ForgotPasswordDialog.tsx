"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "react-i18next";
import { Loader2, KeyRound, User, Lock } from "lucide-react";
import {
  getUserByUsername,
  sendPasswordResetCode,
  resetUserPassword,
  checkRecentPasswordResetAttempt,
} from "@/lib/actions/auth";

interface ForgotPasswordDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function ForgotPasswordDialog({
  open,
  onOpenChange,
}: ForgotPasswordDialogProps) {
  const { t } = useTranslation(["auth", "shared"]);
  const { toast } = useToast();

  // Form states
  const [username, setUsername] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  // UI states
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<"username" | "reset">("username");
  const [userId, setUserId] = useState<string | null>(null);
  const [phoneNumber, setPhoneNumber] = useState<string | null>(null);
  const [preferredLanguage, setPreferredLanguage] = useState<string>("en");

  // Rate limiting
  const [lastCodeSentTime, setLastCodeSentTime] = useState<number | null>(null);
  const [canSendCode, setCanSendCode] = useState(true);
  const [remainingTime, setRemainingTime] = useState(0);

  // Reset form when dialog closes
  useEffect(() => {
    if (!open) {
      setUsername("");
      setVerificationCode("");
      setNewPassword("");
      setConfirmPassword("");
      setStep("username");
      setUserId(null);
      setPhoneNumber(null);
      setIsLoading(false);
      setLastCodeSentTime(null);
      setCanSendCode(true);
      setRemainingTime(0);
    }
  }, [open]);

  // Handle rate limiting countdown
  useEffect(() => {
    if (lastCodeSentTime) {
      const interval = setInterval(() => {
        const elapsed = Date.now() - lastCodeSentTime;
        const remaining = Math.max(0, 120000 - elapsed); // 2 minutes = 120000ms
        
        if (remaining === 0) {
          setCanSendCode(true);
          setRemainingTime(0);
          clearInterval(interval);
        } else {
          setCanSendCode(false);
          setRemainingTime(Math.ceil(remaining / 1000));
        }
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [lastCodeSentTime]);

  const handleSendCode = async () => {
    if (!username.trim()) {
      toast({
        title: t('common.error', { ns: "shared" }),
        description: t("auth.forgotPassword.usernameRequired", { ns: "auth" }),
        variant: "destructive",
      });
      return;
    }

    if (!canSendCode) {
      toast({
        title: t('common.error', { ns: "shared" }),
        description: t("auth.forgotPassword.waitBeforeResend", {
          ns: "auth",
          seconds: remainingTime,
        }),
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      // Step 1: Get user by username
      const userResult = await getUserByUsername(username);

      if (!userResult.success || !userResult.data) {
        toast({
          title: t('common.error', { ns: "shared" }),
          description: userResult.error || t("auth.forgotPassword.userNotFound", { ns: "auth" }),
          variant: "destructive",
        });
        return;
      }

      const { userId: fetchedUserId, phone, preferredLanguage: lang } = userResult.data;

      if (!phone) {
        toast({
          title: t('common.error', { ns: "shared" }),
          description: t("auth.forgotPassword.phoneNotFound", { ns: "auth" }),
          variant: "destructive",
        });
        return;
      }

      // Check for recent password reset attempts from backend
      const recentAttemptCheck = await checkRecentPasswordResetAttempt(fetchedUserId);
      if (recentAttemptCheck.success && !recentAttemptCheck.data?.canSendCode) {
        const remainingSeconds = recentAttemptCheck.data?.remainingSeconds || 120;
        
        // User has an active code - move to password reset step
        setUserId(fetchedUserId);
        setPhoneNumber(phone);
        setPreferredLanguage(lang || "en");
        setStep("reset");
        
        // Set the cooldown state based on backend response
        setCanSendCode(false);
        setRemainingTime(remainingSeconds);
        setLastCodeSentTime(Date.now() - (120 - remainingSeconds) * 1000); // Calculate when it was sent
        
        // Show masked phone number in info message
        const maskedPhone = phone.replace(/(\+\d{2})\d{3}(\d{3})\d{2}(\d{2})/, "$1***$2**$3");
        toast({
          title: t('common.success', { ns: "shared" }),
          description: t("auth.forgotPassword.codeAlreadySent", { 
            ns: "auth", 
            phone: maskedPhone,
            seconds: remainingSeconds 
          }),
        });
        return;
      }

      // Step 2: Send password reset code (now passing username as well)
      const codeResult = await sendPasswordResetCode(fetchedUserId, phone, lang || "en", username);

      if (!codeResult.success) {
        // Check if it's a rate limit error
        if ((codeResult as any).rateLimited) {
          const remainingSeconds = (codeResult as any).remainingSeconds || 120;
          setCanSendCode(false);
          setRemainingTime(remainingSeconds);
          setLastCodeSentTime(Date.now() - (120 - remainingSeconds) * 1000);
        }
        
        toast({
          title: t('common.error', { ns: "shared" }),
          description: codeResult.error || t("auth.forgotPassword.codeSendFailed", { ns: "auth" }),
          variant: "destructive",
        });
        return;
      }

      // Success - move to reset step
      setUserId(fetchedUserId);
      setPhoneNumber(phone);
      setPreferredLanguage(lang || "en");
      setStep("reset");
      setLastCodeSentTime(Date.now());
      setCanSendCode(false);

      // Show masked phone number in success message
      const maskedPhone = phone.replace(/(\+\d{2})\d{3}(\d{3})\d{2}(\d{2})/, "$1***$2**$3");
      toast({
        title: t('common.success', { ns: "shared" }),
        description: t("auth.forgotPassword.codeSent", { ns: "auth", phone: maskedPhone }),
      });
    } catch (error) {
      console.error("Error sending password reset code:", error);
      toast({
        title: t('common.error', { ns: "shared" }),
        description: t("auth.forgotPassword.unexpectedError", { ns: "auth" }),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async () => {
    // Validation
    if (!verificationCode.trim()) {
      toast({
        title: t('common.error', { ns: "shared" }),
        description: t("auth.forgotPassword.verificationCodeRequired", { ns: "auth" }),
        variant: "destructive",
      });
      return;
    }

    if (!newPassword.trim()) {
      toast({
        title: t('common.error', { ns: "shared" }),
        description: t("auth.forgotPassword.passwordRequired", { ns: "auth" }),
        variant: "destructive",
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      toast({
        title: t('common.error', { ns: "shared" }),
        description: t("auth.forgotPassword.passwordMismatch", { ns: "auth" }),
        variant: "destructive",
      });
      return;
    }

    if (newPassword.length < 8) {
      toast({
        title: t('common.error', { ns: "shared" }),
        description: t("auth.forgotPassword.passwordTooShort", { ns: "auth" }),
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      // If we don't have userId, get it again from username
      let effectiveUserId = userId;
      if (!effectiveUserId) {
        const userResult = await getUserByUsername(username);
        if (!userResult.success || !userResult.data) {
          toast({
            title: t('common.error', { ns: "shared" }),
            description: t("auth.forgotPassword.userNotFound", { ns: "auth" }),
            variant: "destructive",
          });
          return;
        }
        effectiveUserId = userResult.data.userId;
      }

      // Ensure we have a valid userId before proceeding
      if (!effectiveUserId) {
        toast({
          title: t('common.error', { ns: "shared" }),
          description: t("auth.forgotPassword.unexpectedError", { ns: "auth" }),
          variant: "destructive",
        });
        return;
      }

      // Step 3: Reset password
      const resetResult = await resetUserPassword(
        effectiveUserId,
        newPassword,
        verificationCode
      );

      if (!resetResult.success) {
        console.log('Reset failed:', resetResult); // Debug log
        
        // Map error codes to localized messages
        let errorMessage = t("auth.forgotPassword.resetFailed", { ns: "auth" });
        
        if ('errorCode' in resetResult) {
          console.log('Error code:', resetResult.errorCode); // Debug log
          
          switch (resetResult.errorCode) {
            case 'INVALID_VERIFICATION_CODE':
            case 'INVALID_REQUEST':
              errorMessage = t("auth.forgotPassword.invalidVerificationCode", { ns: "auth" });
              break;
            case 'INSECURE_PASSWORD':
              errorMessage = t("auth.forgotPassword.insecurePassword", { ns: "auth" });
              console.log('Insecure password message:', errorMessage); // Debug log
              break;
            case 'PASSWORD_TOO_SHORT':
              errorMessage = t("auth.forgotPassword.passwordTooShort", { ns: "auth" });
              break;
            case 'USER_NOT_FOUND':
              errorMessage = t("auth.forgotPassword.userNotFound", { ns: "auth" });
              break;
            case 'GENERAL_ERROR':
            case 'SERVER_ERROR':
            case 'UNKNOWN_ERROR':
              errorMessage = t("auth.forgotPassword.generalError", { ns: "auth" });
              break;
            default:
              errorMessage = resetResult.error || t("auth.forgotPassword.resetFailed", { ns: "auth" });
          }
        }
        
        console.log('Final error message:', errorMessage); // Debug log
        
        toast({
          title: t('common.error', { ns: "shared" }),
          description: errorMessage,
          variant: "destructive",
        });
        
        setIsLoading(false); // Make sure to set loading to false here
        return;
      }

      // Success
      toast({
        title: t('common.success', { ns: "shared" }),
        description: t("auth.forgotPassword.passwordResetSuccess", { ns: "auth" }),
      });

      // Close dialog
      onOpenChange(false);
    } catch (error) {
      console.error("Error resetting password:", error);
      toast({
        title: t('common.error', { ns: "shared" }),
        description: t("auth.forgotPassword.unexpectedError", { ns: "auth" }),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (!canSendCode) {
      toast({
        title: t('common.error', { ns: "shared" }),
        description: t("auth.forgotPassword.waitBeforeResend", {
          ns: "auth",
          seconds: remainingTime,
        }),
        variant: "destructive",
      });
      return;
    }

    // Reset to username step to resend code
    setStep("username");
    await handleSendCode();
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <KeyRound className="h-5 w-5" />
            {t("auth.forgotPassword.title", { ns: "auth" })}
          </DialogTitle>
          <DialogDescription>
            {step === "username"
              ? t("auth.forgotPassword.enterUsername", { ns: "auth" })
              : t("auth.forgotPassword.enterResetCode", { ns: "auth" })}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {step === "username" ? (
            <div className="space-y-2">
              <Label htmlFor="username" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                {t("auth.forgotPassword.username", { ns: "auth" })}
              </Label>
              <Input
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder={t("auth.forgotPassword.usernamePlaceholder", { ns: "auth" })}
                disabled={isLoading}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleSendCode();
                  }
                }}
              />
            </div>
          ) : (
            <>
              <div className="space-y-2">
                <Label htmlFor="verification-code" className="flex items-center gap-2">
                  <KeyRound className="h-4 w-4" />
                  {t("auth.forgotPassword.verificationCode", { ns: "auth" })}
                </Label>
                <Input
                  id="verification-code"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                  placeholder={t("auth.forgotPassword.verificationCodePlaceholder", { ns: "auth" })}
                  disabled={isLoading}
                  maxLength={6}
                />
                {!canSendCode && (
                  <p className="text-sm text-muted-foreground">
                    {t("auth.forgotPassword.resendAvailableIn", {
                      ns: "auth",
                      seconds: remainingTime,
                    })}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="new-password" className="flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  {t("auth.forgotPassword.newPassword", { ns: "auth" })}
                </Label>
                <Input
                  id="new-password"
                  type="password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  placeholder={t("auth.forgotPassword.newPasswordPlaceholder", { ns: "auth" })}
                  disabled={isLoading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirm-password" className="flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  {t("auth.forgotPassword.confirmPassword", { ns: "auth" })}
                </Label>
                <Input
                  id="confirm-password"
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder={t("auth.forgotPassword.confirmPasswordPlaceholder", { ns: "auth" })}
                  disabled={isLoading}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleResetPassword();
                    }
                  }}
                />
              </div>

              {canSendCode && (
                <Button
                  variant="link"
                  className="p-0 h-auto text-sm"
                  onClick={handleResendCode}
                  disabled={isLoading}
                >
                  {t("auth.forgotPassword.resendCode", { ns: "auth" })}
                </Button>
              )}
            </>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            {t("common.actions.cancel", { ns: "shared" })}
          </Button>
          {step === "username" ? (
            <Button onClick={handleSendCode} disabled={isLoading || !canSendCode}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t("common.loading", { ns: "shared" })}
                </>
              ) : (
                t("auth.forgotPassword.sendCode", { ns: "auth" })
              )}
            </Button>
          ) : (
            <Button onClick={handleResetPassword} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t("common.loading", { ns: "shared" })}
                </>
              ) : (
                t("auth.forgotPassword.resetPassword", { ns: "auth" })
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
