'use client';

import {
  Users,
  School,
  BarChart3,
  User,
  CreditCard,
  MapPin,
  AlertCircle,
} from "lucide-react";
import { TurkishLiraIcon } from "@/components/ui/turkish-lira-icon";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  Legend,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import { BadgeStatus } from "@/components/ui/badge-status";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useTheme } from "next-themes";

const COLORS = ["hsl(var(--chart-1))", "hsl(var(--chart-2))", "hsl(var(--chart-3))", "hsl(var(--chart-4))", "hsl(var(--chart-5))"];

export function FinancialOverviewChart({ data } : any) {
  const { t } = useSafeTranslation();
  const { theme } = useTheme();

  const monthlyData = (data?.incomeByMonth || []).map((inc : any, i : any) => ({
    name: t(`common.months.${inc.month.toLowerCase()}`),
    income: inc.amount,
    expenses: (data?.expensesByMonth || [])[i]?.amount || 0,
  }));

  const hasData = monthlyData.length > 0 && monthlyData.some((item: any) => item.income > 0 || item.expenses > 0);

  const tickColor = theme === 'dark' ? '#FFF' : '#000';

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-medium">{t("dashboard.financialOverview.title")}</CardTitle>
        <CardDescription>{t("dashboard.financialOverview.subtitle")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/50 dark:to-green-950/50 border border-green-200 dark:border-green-800 p-4 rounded-lg">
            <p className="text-sm text-green-700 dark:text-green-300 mb-1 font-medium">{t("dashboard.financialOverview.totalIncome")}</p>
            <p className="text-2xl font-bold text-green-800 dark:text-green-100">{(data?.totalIncome || 0).toLocaleString()} TL</p>
          </div>
          <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/50 dark:to-red-950/50 border border-red-200 dark:border-red-800 p-4 rounded-lg">
            <p className="text-sm text-red-700 dark:text-red-300 mb-1 font-medium">{t("dashboard.financialOverview.totalExpenses")}</p>
            <p className="text-2xl font-bold text-red-800 dark:text-red-100">{(data?.totalExpenses || 0).toLocaleString()} TL</p>
          </div>
          <div className={`bg-gradient-to-br p-4 rounded-lg border ${(data?.balance || 0) >= 0 
            ? 'from-blue-50 to-blue-100 dark:from-blue-900/50 dark:to-blue-950/50 border-blue-200 dark:border-blue-800' 
            : 'from-orange-50 to-orange-100 dark:from-orange-900/50 dark:to-orange-950/50 border-orange-200 dark:border-orange-800'}`}>
            <p className={`text-sm mb-1 font-medium ${(data?.balance || 0) >= 0 ? 'text-blue-700 dark:text-blue-300' : 'text-orange-700 dark:text-orange-300'}`}>
              {t("dashboard.financialOverview.netBalance")}
            </p>
            <p className={`text-2xl font-bold ${(data?.balance || 0) >= 0 ? 'text-blue-800 dark:text-blue-100' : 'text-orange-800 dark:text-orange-100'}`}>
              {(data?.balance || 0).toLocaleString()} TL
            </p>
          </div>
        </div>
        
        <div className="h-64">
          {hasData ? (
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={monthlyData}
                margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" tick={{ fill: tickColor }} />
                <YAxis tick={{ fill: tickColor }} />
                <Tooltip 
                  labelFormatter={(label) => label}
                  formatter={(value, name) => [
                    `${value} TL`,
                    name === 'income' ? t('dashboard.financialOverview.income') : t('dashboard.financialOverview.expenses')
                  ]}
                  contentStyle={theme === 'dark' ? { backgroundColor: '#333', border: '1px solid #555' } : {}}
                  labelStyle={theme === 'dark' ? { color: '#FFF' } : {}}
                />
                <Area 
                  type="monotone" 
                  dataKey="income" 
                  name="income"
                  stackId="1"
                  stroke="hsl(var(--chart-1))" 
                  fill="hsl(var(--chart-1))" 
                />
                <Area 
                  type="monotone" 
                  dataKey="expenses" 
                  name="expenses"
                  stackId="2"
                  stroke="hsl(var(--chart-2))" 
                  fill="hsl(var(--chart-2))" 
                />
              </AreaChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
              <BarChart3 className="h-16 w-16 mb-4 opacity-40" />
              <p className="text-lg font-medium mb-2">{t("dashboard.financialOverview.noData")}</p>
              <p className="text-sm text-center max-w-md">{t("dashboard.financialOverview.noDataSubtitle")}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function ExpenseBreakdownChart({ data } : any) {
  const { t } = useSafeTranslation();
  const { theme } = useTheme();

  // Transform data to include translated category names
  const translatedData = (data?.expensesByCategory || []).map((item: any) => ({
    ...item,
    category: t(`expenses.categories.${item.category}`)
  }));

  const hasData = translatedData.length > 0;

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">{t("dashboard.expenseBreakdown.title")}</CardTitle>
        <CardDescription>{t("dashboard.expenseBreakdown.subtitle")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-48">
          {hasData ? (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={translatedData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="amount"
                  nameKey="category"
                >
                  {translatedData.map((entry: any, index: number) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value, name) => [`${value} TL`, name]}
                  contentStyle={theme === 'dark' ? { backgroundColor: '#333', border: '1px solid #555' } : {}}
                  labelStyle={theme === 'dark' ? { color: '#FFF' } : {}}
                />
                <Legend wrapperStyle={theme === 'dark' ? { color: '#FFF' } : {}} />
              </PieChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
              <BarChart3 className="h-12 w-12 mb-3 opacity-50" />
              <p className="text-sm font-medium mb-1">{t("dashboard.expenseBreakdown.noData")}</p>
              <p className="text-xs text-center">{t("dashboard.expenseBreakdown.noDataSubtitle")}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function IncomeSourcesChart({ data }: { data: any }) {
  const { t } = useSafeTranslation();
  const { theme } = useTheme();

  // Transform data to include translated category names
  const categoryMap: Record<string, string> = {
    'Monthly Fees': 'payments.frequency.monthly',
    'Quarterly Fees': 'payments.frequency.quarterly', 
    'Equipment Sales': 'dashboard.incomeSources.categories.equipmentSales',
    'Registration Fees': 'dashboard.incomeSources.categories.registrationFees',
    'Private Lessons': 'dashboard.incomeSources.categories.privateLessons',
    'Tournaments': 'dashboard.incomeSources.categories.tournaments',
    'Other Fees': 'dashboard.incomeSources.categories.otherFees',
    'Other': 'expenses.categories.other'
  };

  const translatedData = (data?.incomeByCategory || []).map((item: any) => ({
    ...item,
    category: t(categoryMap[item.category] || item.category),
    originalCategory: item.category // Keep original for debugging
  }));

  const hasData = translatedData.length > 0;
  const tickColor = theme === 'dark' ? '#FFF' : '#000';

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium">{t("dashboard.incomeSources.title")}</CardTitle>
        <CardDescription>{t("dashboard.incomeSources.subtitle")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-48">
          {hasData ? (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={translatedData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="category" tick={{ fill: tickColor }} />
                <YAxis tick={{ fill: tickColor }} />
                <Tooltip 
                  formatter={(value, name) => [
                    `${value} TL`,
                    t("dashboard.incomeSources.amount")
                  ]}
                  labelFormatter={(label) => `${t("dashboard.incomeSources.category")}: ${label}`}
                  contentStyle={theme === 'dark' ? { backgroundColor: '#333', border: '1px solid #555' } : {}}
                  labelStyle={theme === 'dark' ? { color: '#FFF' } : {}}
                />
                <Bar dataKey="amount" fill="hsl(var(--chart-1))" />
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
              <CreditCard className="h-12 w-12 mb-3 opacity-50" />
              <p className="text-sm font-medium mb-1">{t("dashboard.incomeSources.noData")}</p>
              <p className="text-xs text-center">{t("dashboard.incomeSources.noDataSubtitle")}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export function OverduePaymentsList({ athletes }: { athletes: any[] }) {
  const { t } = useSafeTranslation();

  return (
    <Card className="h-fit">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium flex items-center">
          <AlertCircle className="mr-2 h-5 w-5 text-red-500" />
          {t("dashboard.overduePayments.title")}
        </CardTitle>
        <CardDescription>{t("dashboard.overduePayments.subtitle")}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {athletes.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-muted-foreground">
              <AlertCircle className="h-12 w-12 mb-3 opacity-50" />
              <p className="text-sm font-medium mb-1">{t("dashboard.overduePayments.noOverdue")}</p>
              <p className="text-xs text-center">{t("dashboard.overduePayments.allCaughtUp")}</p>
            </div>
          ) : (
            athletes.slice(0, 5).map((athlete: any) => (
              <div key={athlete.id} className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/50 border border-red-100 dark:border-red-800 rounded-lg hover:bg-red-100 dark:hover:bg-red-900 transition-colors">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-red-500 rounded-full mr-3"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">{athlete.name} {athlete.surname}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {athlete.parentName} {athlete.parentSurname}
                    </p>
                    {athlete.overdueCount && (
                      <p className="text-xs text-red-600 dark:text-red-400 font-medium">
                        {athlete.overdueCount} {athlete.overdueCount === 1 
                          ? t("dashboard.overduePayments.overduePayment") 
                          : t("dashboard.overduePayments.overduePaymentsPlural")
                        }
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center">
                  <span className="text-sm font-bold text-red-600 dark:text-red-400">
                    {athlete.overdueAmount ? athlete.overdueAmount.toFixed(2) : Math.abs(parseFloat(athlete.balance)).toFixed(2)} TL
                  </span>
                  <BadgeStatus status={athlete.status} className="ml-2" />
                </div>
              </div>
            ))
          )}
          
          {athletes.length > 5 && (
            <div className="text-center pt-2">
              <p className="text-sm text-muted-foreground">
                +{athletes.length - 5} {t("dashboard.overduePayments.overdueMore")}
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
