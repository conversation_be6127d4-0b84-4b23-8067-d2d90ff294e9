"use client";

import { useState, useEffect } from "react";
import { useTranslation } from 'react-i18next';
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Settings } from "lucide-react";
import { useDashboardContext } from "@/contexts/dashboard-context";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export function DashboardConfigDialog() {
  const { t } = useTranslation(['shared', 'dashboard']);
  const { state, setSelectedCards, resetToDefault, availableCards } = useDashboardContext();
  const { selectedCards } = state;
  
  const [open, setOpen] = useState(false);
  const [tempSelectedCards, setTempSelectedCards] = useState<string[]>([]);

  // Sync temp state when dialog opens or selectedCards changes
  useEffect(() => {
    console.log('📋 DashboardConfigDialog: Syncing temp state with:', selectedCards);
    setTempSelectedCards([...selectedCards]);
  }, [selectedCards, open]);

  const handleCardToggle = (cardId: string) => {
    console.log('🔄 DashboardConfigDialog: Toggling card:', cardId);
    setTempSelectedCards(prev => {
      const isSelected = prev.includes(cardId);
      let newSelection;
      
      if (isSelected) {
        // Always allow deselection
        newSelection = prev.filter(id => id !== cardId);
      } else {
        // Only allow selection if less than 4 cards are selected
        if (prev.length < 4) {
          newSelection = [...prev, cardId];
        } else {
          // Don't add if already at limit
          return prev;
        }
      }
      
      console.log('🔄 DashboardConfigDialog: New temp selection:', newSelection);
      return newSelection;
    });
  };

  const handleSave = () => {
    console.log('💾 DashboardConfigDialog: Saving selection:', tempSelectedCards);
    setSelectedCards(tempSelectedCards);
    setOpen(false);
    console.log('✅ DashboardConfigDialog: Dialog closed, changes saved');
  };

  const handleReset = () => {
    console.log('🔄 DashboardConfigDialog: Resetting to default');
    resetToDefault();
    setTempSelectedCards(['athletes', 'payments', 'expenses', 'sms']);
    console.log('✅ DashboardConfigDialog: Reset completed');
  };

  const handleCancel = () => {
    console.log('❌ DashboardConfigDialog: Canceling changes');
    setTempSelectedCards([...selectedCards]);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <DialogTrigger asChild>
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <Settings className="h-4 w-4" />
              </Button>
            </DialogTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>{t("dashboard.customize.button", { ns: 'dashboard' })}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <DialogContent className={"lg:max-w-screen-lg overflow-y-scroll max-h-screen max-w-4xl"}>
        <DialogHeader>
          <DialogTitle>{t("dashboard.customize.title", { ns: 'dashboard' })}</DialogTitle>
          <DialogDescription>
            {t("dashboard.customize.description", { ns: 'dashboard' })}
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {availableCards.map((card) => {
            const Icon = card.icon;
            const isSelected = tempSelectedCards.includes(card.id);
            const canSelect = !isSelected && tempSelectedCards.length < 4;
            const isDisabled = !isSelected && !canSelect;
            
            return (
              <Card
                key={card.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  isSelected ? 'ring-2 ring-blue-500' : ''
                } ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={() => !isDisabled && handleCardToggle(card.id)}
              >
                <CardContent className="p-4 text-center">
                  <div className={`w-12 h-12 ${card.bgColor} rounded-lg flex items-center justify-center mx-auto mb-2`}>
                    <Icon className={`w-6 h-6 ${card.textColor}`} />
                  </div>
                  <h3 className="font-medium text-sm mb-1">{card.title}</h3>
                  <p className="text-xs text-muted-foreground mb-2">{card.description}</p>
                  {isSelected && (
                    <Badge variant="secondary" className="text-xs">
                      {t("common.status.active", { ns: 'shared' })}
                    </Badge>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
        
        <div className="text-sm text-muted-foreground">
          {t("dashboard.customize.selected", { count: tempSelectedCards.length, ns: 'dashboard' })}
        </div>
        
        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleReset}>
            {t("dashboard.customize.reset", { ns: 'dashboard' })}
          </Button>
          <Button variant="outline" onClick={handleCancel}>
            {t("common.actions.cancel", { ns: 'shared' })}
          </Button>
          <Button 
            onClick={handleSave}
            disabled={tempSelectedCards.length !== 4}
          >
            {t("common.actions.save", { ns: 'shared' })}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}