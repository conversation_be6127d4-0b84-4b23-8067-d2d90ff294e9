"use client";

import { useMemo, useEffect } from "react";
import { useTranslation } from 'react-i18next';
import { DashboardCard } from "@/components/ui/dashboard-card";
import { DashboardConfigDialog } from "@/components/dashboard/dashboard-config-dialog";
import { useDashboardContext } from "@/contexts/dashboard-context";
import { Skeleton } from "@/components/ui/skeleton";

export function DashboardWidgets() {
  const { t } = useTranslation(['shared', 'dashboard']);
  const { state, getSelectedCards } = useDashboardContext();
  const { selectedCards, isLoading, version } = state;

  // Debug logging
  useEffect(() => {
    console.log('🎯 DashboardWidgets re-rendered with context');
    console.log('🎯 Selected cards:', selectedCards);
    console.log('🎯 Version:', version);
    console.log('🎯 Is loading:', isLoading);
  });

  // Use useMemo to ensure we get fresh card configs when state changes
  const selectedCardsConfig = useMemo(() => {
    const configs = getSelectedCards();
    return configs;
  }, [getSelectedCards]);

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-6 w-6" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Skeleton key={i} className="h-24 w-full" />
          ))}
        </div>
      </div>
    );
  }

  console.log('🎯 Rendering cards from context:', selectedCardsConfig.map(c => c.id));

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <h2 className="text-lg font-semibold">{t("common.actions.title", { ns: 'shared' })}</h2>
        <DashboardConfigDialog />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {selectedCardsConfig.map((card, index) => (
          <DashboardCard
            key={`${card.id}-${version}`} // Use version for force re-render
            title={card.title}
            description={card.description}
            icon={card.icon}
            href={card.href}
            bgColor={card.bgColor}
            textColor={card.textColor}
          />
        ))}
      </div>
    </div>
  );
}