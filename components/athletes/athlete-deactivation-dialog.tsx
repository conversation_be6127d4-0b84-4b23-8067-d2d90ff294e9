"use client";

import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';
import { Loader2, AlertTriangle, DollarSign, Users, CreditCard } from 'lucide-react';
import { useSafeTranslation } from '@/hooks/use-safe-translation';
import type { DeactivationSummary } from '@/lib/services/athlete-deactivation.service';
import { calculateAthleteDeactivation, executeAthleteDeactivation } from '@/lib/actions/athlete-deactivation';

interface AthleteDeactivationDialogProps {
  athleteId: string;
  athleteName: string;
  children?: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onDeactivationComplete?: () => void;
}

export function AthleteDeactivationDialog({
  athleteId,
  athleteName,
  children,
  open: controlledOpen,
  onOpenChange: controlledOnOpenChange,
  onDeactivationComplete
}: AthleteDeactivationDialogProps) {
  const [internalOpen, setInternalOpen] = useState(false);
  const [isCalculating, setIsCalculating] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);
  const [summary, setSummary] = useState<DeactivationSummary | null>(null);
  const { toast } = useToast();
  const { t } = useSafeTranslation();

  // Use controlled or internal state
  const isOpen = controlledOpen !== undefined ? controlledOpen : internalOpen;
  const setIsOpen = controlledOnOpenChange || setInternalOpen;

  const handleOpenDialog = useCallback(async () => {
    setIsOpen(true);
    setIsCalculating(true);
    setSummary(null);

    try {
      const result = await calculateAthleteDeactivation(athleteId);
      
      if (result.success && result.summary) {
        setSummary(result.summary);
      } else {
        toast({
          title: t('athletes.deactivation.calculationFailed', 'Calculation Failed'),
          description: result.error || t('athletes.deactivation.calculationError', 'Failed to calculate deactivation impacts'),
          variant: "destructive",
        });
        setIsOpen(false);
      }
    } catch (error) {
      console.error('Error calculating deactivation:', error);
      toast({
        title: t('common.error', 'Error'),
        description: t('athletes.deactivation.calculationError', 'Failed to calculate deactivation impacts'),
        variant: "destructive",
      });
      setIsOpen(false);
    } finally {
      setIsCalculating(false);
    }
  }, [athleteId, setIsOpen, setSummary, setIsCalculating, t, toast]);

  // Auto-calculate when dialog opens in controlled mode
  React.useEffect(() => {
    if (controlledOpen && !summary && !isCalculating) {
      handleOpenDialog();
    }
  }, [controlledOpen, summary, isCalculating, handleOpenDialog]);

  const handleConfirmDeactivation = async () => {
    if (!summary) return;

    setIsExecuting(true);
    
    try {
      const result = await executeAthleteDeactivation(summary);
      
      if (result.success) {
        toast({
          title: t('athletes.deactivation.deactivationSuccess', 'Athlete Deactivated'),
          description: t('athletes.deactivation.deactivationSuccessMessage', { athleteName, adjustmentCount: result.processedAdjustments }) || 
            `${athleteName} has been successfully deactivated. ${result.processedAdjustments} payment adjustments processed.`,
        });
        setIsOpen(false);
        onDeactivationComplete?.();
      } else {
        toast({
          title: t('athletes.deactivation.deactivationFailed', 'Deactivation Failed'),
          description: result.error || t('athletes.deactivation.deactivationError', 'Failed to deactivate athlete'),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error executing deactivation:', error);
      toast({
        title: t('common.error', 'Error'),
        description: t('athletes.deactivation.deactivationError', 'Failed to execute deactivation'),
        variant: "destructive",
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const formatCurrency = (amount: number) => {
    const formatted = new Intl.NumberFormat('tr-TR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
    return `${formatted} ${t('common.currency', 'TL')}`;
  };

  const getActionBadgeVariant = (action: string) => {
    switch (action) {
      case 'reduce_payment':
        return 'secondary';
      case 'cancel_payment':
        return 'destructive';
      case 'add_credit':
        return 'default';
      case 'adjust_partial_overpaid':
        return 'outline';
      case 'adjust_partial_underpaid':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const getActionText = (action: string) => {
    switch (action) {
      case 'reduce_payment':
        return t('athletes.deactivation.actions.reduce_payment', 'reduce payment');
      case 'cancel_payment':
        return t('athletes.deactivation.actions.cancel_payment', 'cancel payment');
      case 'add_credit':
        return t('athletes.deactivation.actions.add_credit', 'add credit');
      case 'adjust_partial_overpaid':
        return t('athletes.deactivation.actions.adjust_partial_overpaid', 'adjust partial overpaid');
      case 'adjust_partial_underpaid':
        return t('athletes.deactivation.actions.adjust_partial_underpaid', 'adjust partial underpaid');
      default:
        return action.replace('_', ' ');
    }
  };

  const getTotalCredits = () => {
    if (!summary) return 0;
    return summary.paymentAdjustments.reduce((total, adj) => total + adj.creditToBalance, 0);
  };

  const getTotalAdjustments = () => {
    if (!summary) return 0;
    return summary.paymentAdjustments.reduce((total, adj) => {
      const adjustmentAmount = adj.originalAmount - adj.finalAmount;
      return total + adjustmentAmount;
    }, 0);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      {children && (
        <DialogTrigger asChild onClick={handleOpenDialog}>
          {children}
        </DialogTrigger>
      )}
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            {t('athletes.deactivation.title', { athleteName }) || `Deactivate ${athleteName}`}
          </DialogTitle>
        </DialogHeader>

        {isCalculating ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            {t('athletes.deactivation.calculating', 'Calculating deactivation impacts...')}
          </div>
        ) : summary ? (
          <ScrollArea className="max-h-[60vh]">
            <div className="space-y-6">
              {/* Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {t('athletes.deactivation.summary.paymentAdjustments', 'Payment Adjustments')}
                    </CardTitle>
                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{summary.paymentAdjustments.length}</div>
                    <p className="text-xs text-muted-foreground">
                      {t('athletes.deactivation.summary.totalAdjustment', { amount: formatCurrency(getTotalAdjustments()) }) ||
                        `Total adjustment: ${formatCurrency(getTotalAdjustments())}`}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {t('athletes.deactivation.summary.creditsToBalance', 'Credits to Balance')}
                    </CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">
                      {formatCurrency(getTotalCredits())}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {t('athletes.deactivation.summary.availableForFutureUse', 'Will be available for future use')}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">
                      {t('athletes.deactivation.summary.teamsToRemove', 'Teams to Remove')}
                    </CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{summary.teamsToRemove.length}</div>
                    <p className="text-xs text-muted-foreground">
                      {t('athletes.deactivation.summary.teamMemberships', 'Team memberships')}
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Payment Adjustments Details */}
              {summary.paymentAdjustments.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      {t('athletes.deactivation.summary.paymentAdjustments', 'Payment Adjustments')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {summary.paymentAdjustments.map((adjustment, index) => (
                        <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant={getActionBadgeVariant(adjustment.action) as any}>
                                {getActionText(adjustment.action)}
                              </Badge>
                              <span className="font-medium">
                                {adjustment.paymentPlanName || t('athletes.deactivation.unknownPlan', 'Unknown Plan')}
                              </span>
                            </div>
                            <p className="text-sm text-muted-foreground mb-1">
                              {adjustment.description}
                            </p>
                            <div className="flex items-center gap-4 text-sm">
                              <span>{t('athletes.deactivation.summary.originalAmount', { amount: formatCurrency(adjustment.originalAmount) }) ||
                                `Original: ${formatCurrency(adjustment.originalAmount)}`}</span>
                              <span>→</span>
                              <span>{t('athletes.deactivation.summary.finalAmount', { amount: formatCurrency(adjustment.finalAmount) }) ||
                                `Final: ${formatCurrency(adjustment.finalAmount)}`}</span>
                              {adjustment.creditToBalance > 0 && (
                                <>
                                  <span>+</span>
                                  <span className="text-green-600 font-medium">
                                    {t('athletes.deactivation.summary.creditAmount', { amount: formatCurrency(adjustment.creditToBalance) }) ||
                                      `${formatCurrency(adjustment.creditToBalance)} credit`}
                                  </span>
                                </>
                              )}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-medium">
                              {(() => {
                                const adjustmentAmount = adjustment.originalAmount - adjustment.finalAmount;
                                return (adjustmentAmount > 0 ? '+' : '') + formatCurrency(adjustmentAmount);
                              })()}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Teams to Remove */}
              {summary.teamsToRemove.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      {t('athletes.deactivation.summary.teamsToRemove', 'Teams to Remove')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {summary.teamsToRemove.map((team, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded">
                          <span className="font-medium">{team.teamName}</span>
                          <Badge variant="outline">{t('athletes.deactivation.summary.remove', 'Remove')}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Payment Plans to Deactivate */}
              {summary.paymentPlansToDeactivate.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      {t('athletes.deactivation.summary.paymentPlansToDeactivate', 'Payment Plans to Deactivate')}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {summary.paymentPlansToDeactivate.map((plan, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded">
                          <span className="font-medium">{plan.planName}</span>
                          <Badge variant="destructive">{t('athletes.deactivation.summary.deactivate', 'Deactivate')}</Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Warning Message */}
              <Card className="border-orange-200 bg-orange-50">
                <CardContent className="pt-6">
                  <div className="flex items-start gap-2">
                    <AlertTriangle className="h-5 w-5 text-orange-500 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-orange-800">
                        {t('athletes.deactivation.importantNotice', 'Important Notice')}
                      </h4>
                      <p className="text-sm text-orange-700 mt-1">
                        {t('athletes.deactivation.warningMessage', 
                          'This action will immediately deactivate the athlete and process all financial adjustments. Credits will be added to the athlete\'s balance for future use. This action cannot be undone automatically.'
                        )}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </ScrollArea>
        ) : null}

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)} disabled={isExecuting}>
            {t('athletes.deactivation.cancel', 'Cancel')}
          </Button>
          {summary && (
            <Button 
              onClick={handleConfirmDeactivation} 
              disabled={isExecuting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isExecuting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  {t('athletes.deactivation.deactivating', 'Deactivating...')}
                </>
              ) : (
                t('athletes.deactivation.confirmDeactivation', 'Confirm Deactivation')
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
