'use client';

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserX, Calculator } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { useRouter } from "next/navigation";
import { Athlete } from "@/lib/types";
import { activateAthlete, deactivateAthlete, getAthleteActivationData } from "@/lib/actions/athletes";
import { getTeams } from "@/lib/actions/teams";
import { getPaymentPlans } from "@/lib/actions/payment-plans";
import { useToast } from "@/hooks/use-toast";
import { calculateProratedAmountForBillingCycle } from "@/lib/proration-utils";

interface AthleteActivationDialogProps {
  athlete: Athlete;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

interface ActivationData {
  athlete: {
    id: string;
    name: string;
    surname: string;
    status: 'active' | 'inactive' | 'suspended';
  };
  teams: Array<{
    teamId: string;
    teamName: string;
    branchName: string;
    schoolName: string;
    instructorName: string;
    instructorSurname: string;
  }>;
  paymentPlans: Array<{
    id: string;
    planId: string;
    teamId: string;
    assignedDate: string;
    isActive: boolean;
    lastPaymentDate: string | null;
    planName: string;
    monthlyValue: string;
    assignDay: number;
    dueDay: number;
    planStatus: string;
    planDescription: string | null;
    teamName: string | null;
    athleteName: string;
    athleteSurname: string;
  }>;
}

interface Team {
  id: string;
  name: string;
  branch?: {
    name: string;
  };
  school?: {
    name: string;
  };
}

interface PaymentPlan {
  id: string;
  name: string;
  monthlyValue: string;
  assignDay: number;
}

export const AthleteActivationDialog = ({ athlete, isOpen, onOpenChange, onSuccess }: AthleteActivationDialogProps) => {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [activationData, setActivationData] = useState<ActivationData | null>(null);
  const [availableTeams, setAvailableTeams] = useState<Team[]>([]);
  const [availablePaymentPlans, setAvailablePaymentPlans] = useState<PaymentPlan[]>([]);
  const [selectedTeams, setSelectedTeams] = useState<string[]>([]);
  const [teamPaymentPlans, setTeamPaymentPlans] = useState<Record<string, string>>({});
  const [prorationEnabled, setProrationEnabled] = useState<Record<string, boolean>>({});

  const isDeactivating = athlete.status === 'active';

  // Debug logging
  useEffect(() => {
  }, [isOpen, athlete.id, athlete.status]);

  const loadData = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Get activation data (current teams and payment plans)
      const activationResult = await getAthleteActivationData(athlete.id);
      
      if (activationResult.success && activationResult.data) {
        setActivationData(activationResult.data);
      } else {
        // Set empty data instead of closing dialog
        setActivationData({
          athlete: {
            id: athlete.id,
            name: athlete.name,
            surname: athlete.surname,
            status: athlete.status,
          },
          teams: [],
          paymentPlans: [],
        });
      }

      // If activating, also load available teams and payment plans
      if (!isDeactivating) {
        try {
          const [teamsResult, paymentPlansResult] = await Promise.all([
            getTeams(),
            getPaymentPlans(),
          ]);
          
          setAvailableTeams(teamsResult || []);
          setAvailablePaymentPlans(paymentPlansResult || []);
        } catch (error) {
          setAvailableTeams([]);
          setAvailablePaymentPlans([]);
        }
      }
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('athletes.messages.loadDataError', 'Failed to load data'),
        variant: "destructive",
      });
      // Don't close dialog on error, show empty state
      setActivationData({
        athlete: {
          id: athlete.id,
          name: athlete.name,
          surname: athlete.surname,
          status: athlete.status,
        },
        teams: [],
        paymentPlans: [],
      });
    } finally {
      setIsLoading(false);
    }
  }, [athlete.id, athlete.name, athlete.surname, athlete.status, isDeactivating, t, toast]);

  useEffect(() => {
    if (isOpen) {
      // Reset state when dialog opens
      setSelectedTeams([]);
      setTeamPaymentPlans({});
      setProrationEnabled({});
      setActivationData(null);
      setAvailableTeams([]);
      setAvailablePaymentPlans([]);
      void loadData();
    }
  }, [isOpen, athlete.id, loadData]);

  const handleTeamSelection = (teamId: string, checked: boolean) => {
    if (checked) {
      setSelectedTeams(prev => [...prev, teamId]);
    } else {
      setSelectedTeams(prev => prev.filter(id => id !== teamId));
      // Remove payment plan selection and proration for this team
      setTeamPaymentPlans(prev => {
        const newPlans = { ...prev };
        delete newPlans[teamId];
        return newPlans;
      });
      setProrationEnabled(prev => {
        const newProration = { ...prev };
        delete newProration[teamId];
        return newProration;
      });
    }
  };

  const handlePaymentPlanSelection = (teamId: string, planId: string) => {
    setTeamPaymentPlans(prev => ({
      ...prev,
      [teamId]: planId,
    }));
    // Reset proration when payment plan changes
    if (planId) {
      setProrationEnabled(prev => ({
        ...prev,
        [teamId]: false,
      }));
    } else {
      setProrationEnabled(prev => {
        const newProration = { ...prev };
        delete newProration[teamId];
        return newProration;
      });
    }
  };

  const handleProrationToggle = (teamId: string, enabled: boolean) => {
    setProrationEnabled(prev => ({
      ...prev,
      [teamId]: enabled,
    }));
  };

  const getPaymentPlanById = (planId: string) => {
    return availablePaymentPlans.find(plan => plan.id === planId);
  };

  const calculateProrationInfo = (planId: string) => {
    const plan = getPaymentPlanById(planId);
    if (!plan) return null;

    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth();
    const assignDay = plan.assignDay;
    
    // Calculate last and next billing dates
    let lastBillingDate = new Date(year, month, assignDay);
    if (lastBillingDate > today) {
      lastBillingDate = new Date(year, month - 1, assignDay);
    }
    
    let nextBillingDate = new Date(year, month, assignDay);
    if (nextBillingDate <= today) {
      nextBillingDate = new Date(year, month + 1, assignDay);
    }
    
    const totalBillingDays = Math.ceil((nextBillingDate.getTime() - lastBillingDate.getTime()) / (1000 * 60 * 60 * 24));
    const remainingDays = Math.ceil((nextBillingDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    
    const fullAmount = parseFloat(plan.monthlyValue);
    const proratedAmount = calculateProratedAmountForBillingCycle(fullAmount, assignDay);

    return {
      fullAmount,
      proratedAmount,
      totalBillingDays,
      remainingDays,
      nextBillingDate,
      lastBillingDate,
    };
  };

  const handleConfirm = async () => {
    try {
      setIsLoading(true);

      if (isDeactivating) {
        // Deactivate athlete
        const result = await deactivateAthlete(athlete.id);
        
        if (result.success) {
          toast({
            title: t('common.success'),
            description: t('athletes.messages.deactivateSuccess', 'Athlete deactivated successfully'),
          });
          onSuccess?.();
          onOpenChange(false);
          router.refresh();
        } else {
          let errorDescriptionKey = '';
          if(result.errorType == 'BusinessRuleError'){
            errorDescriptionKey = `errors.${result.error}`;
          }else{
            errorDescriptionKey = 'athletes.messages.deactivateError';
          }
          toast({
            title: t('common.error'),
            description: t(errorDescriptionKey),
            variant: "destructive",
          });
        }
      } else {
        // Activate athlete
        const teamAssignments = selectedTeams.map(teamId => ({
          teamId,
          paymentPlanId: teamPaymentPlans[teamId] || undefined,
          useProrated: prorationEnabled[teamId] || false,
        }));

        const result = await activateAthlete(athlete.id, teamAssignments);
        
        if (result.success) {
          toast({
            title: t('common.success'),
            description: t('athletes.messages.activateSuccess', 'Athlete activated successfully'),
          });
          onSuccess?.();
          onOpenChange(false);
          router.refresh();
        } else {
          let errorDescriptionKey = '';
          if(result.errorType == 'BusinessRuleError'){
            errorDescriptionKey = `errors.${result.error}`;
          }else{
            errorDescriptionKey = 'athletes.messages.activateError';
          }
          toast({
            title: t('common.error'),
            description: t(errorDescriptionKey),
            variant: "destructive",
          });
        }
      }
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('athletes.messages.operationError', 'Operation failed'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getTeamName = (team: Team) => {
    const branchName = team.branch?.name || '';
    const schoolName = team.school?.name || '';
    return `${team.name} ${branchName ? `(${branchName})` : ''} ${schoolName ? `- ${schoolName}` : ''}`.trim();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>
            {isDeactivating 
              ? t('athletes.actions.deactivateAthlete', 'Deactivate Athlete')
              : t('athletes.actions.activateAthlete', 'Activate Athlete')
            }
          </DialogTitle>
          <DialogDescription>
            {isDeactivating 
              ? t('athletes.deactivateDialog.description', 
                  'When you deactivate this athlete, they will be removed from all teams and payment plans will be deactivated.')
              : t('athletes.activateDialog.description', 
                  'Select teams and payment plans to assign to this athlete.')
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="text-sm font-medium">
            {t('athletes.details.name')}: {athlete.name} {athlete.surname}
          </div>

          {isLoading && (
            <div className="text-center py-4">
              <div className="text-sm text-muted-foreground">
                {t('common.loading', 'Loading...')}
              </div>
            </div>
          )}

          {!isLoading && activationData && (
            <div className="space-y-4">
              {isDeactivating ? (
                // Deactivation view - show current teams and payment plans
                <div className="space-y-4">
                  {activationData.teams.length > 0 && (
                    <div>
                      <Label className="text-sm font-medium text-destructive">
                        {t('athletes.deactivateDialog.teamsToRemove', 'Teams to remove from:')}
                      </Label>
                      <div className="mt-2 space-y-2">
                        {activationData.teams.map((team) => (
                          <div key={team.teamId} className="flex items-center space-x-2 p-2 bg-destructive/10 rounded">
                            <UserX className="h-4 w-4 text-destructive" />
                            <span className="text-sm">
                              {team.teamName} ({team.branchName}) - {team.schoolName}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {activationData.paymentPlans.length > 0 && (
                    <div>
                      <Label className="text-sm font-medium text-destructive">
                        {t('athletes.deactivateDialog.paymentPlansToRemove', 'Payment plans to deactivate:')}
                      </Label>
                      <div className="mt-2 space-y-2">
                        {activationData.paymentPlans.map((plan) => (
                          <div key={plan.id} className="flex items-center space-x-2 p-2 bg-destructive/10 rounded">
                            <UserX className="h-4 w-4 text-destructive" />
                            <span className="text-sm">
                              {plan.planName} - {plan.monthlyValue} {t('common.currency')} 
                              {plan.teamName && ` (${plan.teamName})`}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {activationData.teams.length === 0 && activationData.paymentPlans.length === 0 && (
                    <div className="text-sm text-muted-foreground">
                      {t('athletes.deactivateDialog.noActiveAssignments', 'No active team or payment plan assignments.')}
                    </div>
                  )}
                </div>
              ) : (
                // Activation view - show available teams and payment plans
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">
                      {t('athletes.activateDialog.selectTeams', 'Select teams to assign:')}
                    </Label>
                    <div className="mt-2 space-y-2 max-h-40 overflow-y-auto">
                      {availableTeams.map((team) => (
                        <div key={team.id} className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id={`team-${team.id}`}
                              checked={selectedTeams.includes(team.id)}
                              onCheckedChange={(checked) => 
                                handleTeamSelection(team.id, checked as boolean)
                              }
                            />
                            <Label htmlFor={`team-${team.id}`} className="text-sm">
                              {getTeamName(team)}
                            </Label>
                          </div>
                          
                          {selectedTeams.includes(team.id) && (
                            <div className="ml-6 space-y-3">
                              <div className="space-y-1">
                                <Label className="text-xs text-muted-foreground">
                                  {t('athletes.activateDialog.selectPaymentPlan', 'Select payment plan (optional):')}
                                </Label>
                                <Select
                                  value={teamPaymentPlans[team.id] || ""}
                                  onValueChange={(value) => handlePaymentPlanSelection(team.id, value)}
                                >
                                  <SelectTrigger className="h-8 text-sm">
                                    <SelectValue placeholder={t('athletes.activateDialog.noPaymentPlan', 'No payment plan')} />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {availablePaymentPlans.map((plan) => (
                                      <SelectItem key={plan.id} value={plan.id}>
                                        {plan.name} - {plan.monthlyValue} {t('common.currency')}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>

                              {/* Proration Section */}
                              {teamPaymentPlans[team.id] && (
                                <div className="space-y-2 p-3 bg-muted/50 rounded-md border">
                                  <div className="flex items-center space-x-2">
                                    <Checkbox
                                      id={`proration-${team.id}`}
                                      checked={prorationEnabled[team.id] || false}
                                      onCheckedChange={(checked) => 
                                        handleProrationToggle(team.id, checked as boolean)
                                      }
                                    />
                                    <Label htmlFor={`proration-${team.id}`} className="text-sm font-medium">
                                      {t('athletes.activateDialog.applyProration', 'Apply proration for partial billing cycle')}
                                    </Label>
                                    <Calculator className="h-4 w-4 text-muted-foreground" />
                                  </div>
                                  
                                  {(() => {
                                    const prorationInfo = calculateProrationInfo(teamPaymentPlans[team.id]);
                                    if (!prorationInfo) return null;
                                    
                                    const isProrated = prorationEnabled[team.id];
                                    const displayAmount = isProrated ? prorationInfo.proratedAmount : prorationInfo.fullAmount;
                                    
                                    return (
                                      <div className="space-y-1 text-sm">
                                        <div className="flex justify-between items-center">
                                          <span className="text-muted-foreground">
                                            {isProrated 
                                              ? t('athletes.activateDialog.proratedAmount', 'Prorated amount')
                                              : t('athletes.activateDialog.fullAmount', 'Full amount')
                                            }:
                                          </span>
                                          <span className="font-medium">
                                            {displayAmount.toFixed(2)} {t('common.currency')}
                                          </span>
                                        </div>
                                        
                                        {isProrated && (
                                          <>
                                            <div className="text-xs text-muted-foreground">
                                              {t('athletes.activateDialog.billingCycleInfo', {
                                                assignDay: prorationInfo.lastBillingDate.getDate(),
                                                nextAssignDay: prorationInfo.nextBillingDate.getDate(),
                                                daysInCycle: prorationInfo.totalBillingDays,
                                              })}
                                            </div>
                                            <div className="text-xs text-muted-foreground">
                                              {t('athletes.activateDialog.daysRemaining', {
                                                days: prorationInfo.remainingDays,
                                              })}
                                            </div>
                                            <div className="text-xs text-blue-600">
                                              {t('athletes.activateDialog.proratedCalculation', {
                                                proratedAmount: prorationInfo.proratedAmount.toFixed(2),
                                                daysRemaining: prorationInfo.remainingDays,
                                                totalDays: prorationInfo.totalBillingDays,
                                                fullAmount: prorationInfo.fullAmount.toFixed(2),
                                              })}
                                            </div>
                                          </>
                                        )}
                                      </div>
                                    );
                                  })()}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                    {availableTeams.length === 0 && (
                      <div className="text-sm text-muted-foreground mt-2">
                        {t('athletes.activateDialog.noAvailableTeams', 'No available teams.')}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            {t('common.actions.cancel', 'Cancel')}
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isLoading || (!isDeactivating && selectedTeams.length === 0)}
            variant={isDeactivating ? "destructive" : "default"}
          >
            {isLoading ? (
              t('common.actions.processing', 'Processing...')
            ) : isDeactivating ? (
              t('athletes.actions.deactivate', 'Deactivate')
            ) : (
              t('athletes.actions.activate', 'Activate')
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
