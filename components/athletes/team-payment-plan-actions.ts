"use server";

import { revalidatePath } from "next/cache";
import { assignPaymentPlan, deactivateAssignment } from "@/lib/api";
import { addAthleteToTeam } from "@/lib/api";

export async function assignPaymentPlanAction(
  athleteId: string,
  planId: string,
  teamId: string,
  isAthleteInTeam: boolean
) {
  try {
    // If athlete is not in team, add them first
    if (!isAthleteInTeam) {
      await addAthleteToTeam(athleteId, teamId);
    }

    // Assign the payment plan
    await assignPaymentPlan({
      athleteId,
      planId,
      teamId,
    });

    revalidatePath(`/athletes/${athleteId}`);
    return { success: true };
  } catch (error) {
    console.error("Error assigning payment plan:", error);
    return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
  }
}

export async function deactivatePaymentPlanAction(assignmentId: string, athleteId: string) {
  try {
    await deactivateAssignment(assignmentId);
    revalidatePath(`/athletes/${athleteId}`);
    return { success: true };
  } catch (error) {
    console.error("Error deactivating payment plan:", error);
    return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
  }
}
