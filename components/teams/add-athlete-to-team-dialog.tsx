"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Plus, Users, CreditCard, User, ChevronsUpDown, Check } from "lucide-react";
import { useSafeTranslation } from "@/hooks/use-safe-translation";
import { calculateProratedAmountForBillingCycle } from "@/lib/proration-utils";
import { Input } from "@/components/ui/input";
import { addAthleteToTeamWithPaymentPlan, checkAthleteInTeam, getAvailablePaymentPlansForTeam } from "@/lib/actions/athlete-team-management";
import { getAthletes } from "@/lib/actions";
import {useToast} from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface PaymentPlan {
  id: string;
  name: string;
  monthlyValue: string;
  assignDay: number;
  dueDay: number;
  status: "active" | "inactive";
  branches?: { id: string; name: string; description: string | null; }[];
}

interface Athlete {
  id: string;
  name: string;
  surname: string;
  status: string;
  parentPhone?: string;
  parentEmail?: string;
  teams?: string[];
}

interface AddAthleteToTeamDialogProps {
  teamId: string;
  teamName: string;
  teamBranchId: string;
  existingAthleteIds: string[];
  onAthleteAdded?: () => void;
}

export function AddAthleteToTeamDialog({
  teamId,
  teamName,
  teamBranchId,
  existingAthleteIds,
  onAthleteAdded
}: AddAthleteToTeamDialogProps) {
  const { t } = useSafeTranslation();
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedAthleteId, setSelectedAthleteId] = useState("");
  const [shouldAssignPaymentPlan, setShouldAssignPaymentPlan] = useState(false);
  const [useProrated, setUseProrated] = useState(false);
  const [proratedAmount, setProratedAmount] = useState("");
  const [calculatedProrated, setCalculatedProrated] = useState<number | null>(null);
  const [selectedPaymentPlanId, setSelectedPaymentPlanId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [availableAthletes, setAvailableAthletes] = useState<Athlete[]>([]);
  const [availablePaymentPlans, setAvailablePaymentPlans] = useState<PaymentPlan[]>([]);
  const [loadingData, setLoadingData] = useState(false);
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Filter athletes based on search term
  const filteredAthletes = searchTerm.length < 3 
    ? [] 
    : availableAthletes.filter(athlete => {
        const searchLower = searchTerm.toLowerCase();
        return (
          athlete.name?.toLowerCase().includes(searchLower) ||
          athlete.surname?.toLowerCase().includes(searchLower) ||
          (athlete.parentPhone && athlete.parentPhone.includes(searchTerm))
        );
      });

  const loadDialogData = useCallback(async () => {
    setLoadingData(true);
    try {
      const [allAthletes, paymentPlans] = await Promise.all([
        getAthletes(),
        getAvailablePaymentPlansForTeam(teamBranchId)
      ]);

      // Filter out athletes that are already in the team and inactive athletes
      const filteredAthletes = (allAthletes as any[]).filter((athlete: any) => 
        !existingAthleteIds.includes(athlete.id) && 
        athlete.status === 'active'
      );

      setAvailableAthletes(filteredAthletes);
      setAvailablePaymentPlans(paymentPlans || []);
    } catch (error) {
      console.error("Error loading dialog data:", error);
      toast({
        title: t('common.error'),
        description: t('common.error'),
        variant: "destructive",
      });
    } finally {
      setLoadingData(false);
    }
  }, [teamBranchId, existingAthleteIds, t, toast]);

  // Load data when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadDialogData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]); // Only depend on isOpen to avoid infinite loop

  // Calculate prorated amount when useProrated changes or payment plan is selected
  useEffect(() => {
    if (useProrated && shouldAssignPaymentPlan && selectedPaymentPlanId) {
      const selectedPlan = availablePaymentPlans.find(plan => plan.id === selectedPaymentPlanId);
      if (selectedPlan) {
        const monthlyAmount = parseFloat(selectedPlan.monthlyValue);
        // Use the new billing cycle-based proration calculation
        const prorated = calculateProratedAmountForBillingCycle(
          monthlyAmount,
          selectedPlan.assignDay
        );
        setCalculatedProrated(prorated);
        setProratedAmount(prorated.toFixed(2));
      }
    } else if (useProrated) {
      setCalculatedProrated(null);
      setProratedAmount("");
    }
  }, [useProrated, shouldAssignPaymentPlan, selectedPaymentPlanId, availablePaymentPlans]);

  const handleAddAthlete = async () => {
    if (!selectedAthleteId) {
      toast({
        title: t('common.error'),
        description: t('teams.management.selectAthlete'),
        variant: "destructive",
      });
      return;
    }

    if (shouldAssignPaymentPlan && !selectedPaymentPlanId) {
      toast({
        title: t('common.error'),
        description: t('teams.management.selectPaymentPlan'),
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      // Double-check if athlete is already in team
      const checkResult = await checkAthleteInTeam(selectedAthleteId, teamId);
      
      if (checkResult) {
        toast({
          title: t('common.error'),
          description: t('teams.management.athleteAlreadyInTeam'),
          variant: "destructive",
        });
        return;
      }

      const result = await addAthleteToTeamWithPaymentPlan({
        athleteId: selectedAthleteId,
        teamId,
        paymentPlanId: shouldAssignPaymentPlan ? selectedPaymentPlanId : undefined,
        assignPaymentPlan: shouldAssignPaymentPlan,
        useProrated: shouldAssignPaymentPlan ? useProrated : false,
        customProratedAmount: useProrated && proratedAmount ? proratedAmount : undefined,
        locale: 'tr' // You might want to get this from a context
      });

      if (result.success) {
        const selectedAthlete = availableAthletes.find(a => a.id === selectedAthleteId);
        const selectedPlan = availablePaymentPlans.find(p => p.id === selectedPaymentPlanId);
        
        let successMessage = t('teams.management.athleteAddedSuccess', {
          athleteName: `${selectedAthlete?.name} ${selectedAthlete?.surname}`,
          teamName
        });

        if (shouldAssignPaymentPlan && selectedPlan) {
          successMessage += ` ${t('teams.management.withPaymentPlan', { 
            planName: selectedPlan.name 
          })}`;
        }
        toast({
          title: t('common.success'),
          description: successMessage,
        });
        
        // Reset form
        setSelectedAthleteId("");
        setSelectedPaymentPlanId("");
        setShouldAssignPaymentPlan(false);
        setUseProrated(false);
        setProratedAmount("");
        setCalculatedProrated(null);
        setShouldAssignPaymentPlan(false);
        setIsOpen(false);
        
        // Notify parent component
        onAthleteAdded?.();
      } else {
        let errorDescriptionKey = '';
        if(result.errorType == 'BusinessRuleError'){
          errorDescriptionKey = `errors.${result.error}`;
        }else{
          errorDescriptionKey = 'athletes.management.addAthleteError';
        }
        toast({
          title: t('common.error'),
          description: t(errorDescriptionKey),
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error adding athlete to team:", error);
      toast({
        title: t('common.error'),
        description: t('teams.management.addAthleteError'),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: string) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(parseFloat(amount));
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      setIsOpen(open);
      // Reset form when dialog closes
      if (!open) {
        setSelectedAthleteId("");
        setSelectedPaymentPlanId("");
        setShouldAssignPaymentPlan(false);
        setUseProrated(false);
        setProratedAmount("");
        setCalculatedProrated(null);
        setAvailableAthletes([]);
        setAvailablePaymentPlans([]);
      }
    }}>
      <DialogTrigger asChild>
        <Button size="sm">
          <Plus className="h-4 w-4 mr-2" />
          {t('teams.management.addAthlete')}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t('teams.management.addAthleteToTeam')}
          </DialogTitle>
          <DialogDescription>
            {t('teams.management.addAthleteDescription', { teamName })}
          </DialogDescription>
        </DialogHeader>

        {loadingData ? (
          <div className="space-y-4">
            <div className="h-10 bg-muted animate-pulse rounded-md" />
            <div className="h-10 bg-muted animate-pulse rounded-md" />
            <div className="h-10 bg-muted animate-pulse rounded-md" />
          </div>
        ) : (
          <div className="space-y-4">
            {/* Athlete Selection */}
            <div className="space-y-2">
              <Label>{t('athletes.title')} *</Label>
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className="w-full justify-between h-auto min-h-[40px] p-3"
                  >
                    {selectedAthleteId ? (
                      <div className="flex items-center gap-2 text-left">
                        <div className="flex flex-col">
                          <span className="font-medium">
                            {availableAthletes.find((athlete) => athlete.id === selectedAthleteId)?.name} {availableAthletes.find((athlete) => athlete.id === selectedAthleteId)?.surname}
                          </span>
                          {availableAthletes.find((athlete) => athlete.id === selectedAthleteId)?.parentPhone && (
                            <span className="text-sm text-muted-foreground">
                              {availableAthletes.find((athlete) => athlete.id === selectedAthleteId)?.parentPhone}
                            </span>
                          )}
                        </div>
                      </div>
                    ) : (
                      t('teams.management.selectAthleteToAdd')
                    )}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full min-w-[400px] p-0" align="start">
                  <Command>
                    <CommandInput 
                      placeholder={t('athletes.placeholders.searchAthletes')}
                      value={searchTerm}
                      onValueChange={setSearchTerm}
                    />
                    <CommandList className="max-h-[300px]">
                      {searchTerm.length < 3 ? (
                        <div className="p-4 text-center text-sm text-muted-foreground">
                          {t('common.search.enterMinChars', { count: 3 })}
                        </div>
                      ) : (
                        <>
                          <CommandEmpty>{t('teams.management.noAvailableAthletes')}</CommandEmpty>
                          <CommandGroup>
                            {filteredAthletes.map((athlete) => (
                              <CommandItem
                                key={athlete.id}
                                value={`${athlete.name} ${athlete.surname} ${athlete.parentPhone || ''}`}
                                onSelect={() => {
                                  setSelectedAthleteId(athlete.id);
                                  setOpen(false);
                                  setSearchTerm(""); // Reset search query
                                }}
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4",
                                    selectedAthleteId === athlete.id ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                <div className="flex-1">
                                  <div className="font-medium">{athlete.name} {athlete.surname}</div>
                                  <div className="text-sm text-muted-foreground">
                                    {athlete.parentPhone && `${t('athletes.table.parentPhone')}: ${athlete.parentPhone}`}
                                    {athlete.parentPhone && athlete.parentEmail && ' | '}
                                    {athlete.parentEmail && `${t('athletes.table.parentEmail')}: ${athlete.parentEmail}`}
                                  </div>
                                </div>
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </>
                      )}
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            {/* Payment Plan Assignment Option */}
            <Card className="border-dashed">
              <CardContent className="p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <Checkbox
                    id="assignPaymentPlan"
                    checked={shouldAssignPaymentPlan}
                    onCheckedChange={(checked) => setShouldAssignPaymentPlan(checked === true)}
                  />
                  <Label htmlFor="assignPaymentPlan" className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    {t('teams.management.assignPaymentPlan')}
                  </Label>
                </div>

                {shouldAssignPaymentPlan && (
                  <div className="space-y-2">
                    <Label>{t('payments.plans.plan')} *</Label>
                    <Select value={selectedPaymentPlanId} onValueChange={setSelectedPaymentPlanId}>
                      <SelectTrigger>
                        <SelectValue placeholder={t('payments.plans.select')} />
                      </SelectTrigger>
                      <SelectContent>
                        {availablePaymentPlans.length === 0 ? (
                          <div className="p-2 text-center text-muted-foreground">
                            {t('teams.management.noAvailablePaymentPlans')}
                          </div>
                        ) : (
                          availablePaymentPlans.map((plan) => (
                            <SelectItem key={plan.id} value={plan.id}>
                              {plan.name} - {formatCurrency(plan.monthlyValue)}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    {shouldAssignPaymentPlan && availablePaymentPlans.length === 0 && (
                      <p className="text-sm text-muted-foreground">
                        {t('teams.management.noPaymentPlansForBranch')}
                      </p>
                    )}
                    
                    {/* Prorated Balance Option */}
                    {shouldAssignPaymentPlan && selectedPaymentPlanId && (
                      <div className="mt-3 p-3 bg-muted/50 rounded-md">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="useProrated"
                            checked={useProrated}
                            onCheckedChange={(checked) => setUseProrated(checked === true)}
                          />
                          <Label htmlFor="useProrated" className="text-sm font-normal">
                            {t('athletes.proratedBalance', 'Apply prorated balance for remaining days of the month')}
                          </Label>
                        </div>
                        
                        {useProrated && (
                          <div className="mt-3 space-y-3">
                            {/* Calculation Details */}
                            {calculatedProrated !== null && selectedPaymentPlanId && (
                              <div className="p-3 bg-background rounded border text-sm">
                                <div className="space-y-2 text-left">
                                  <div className="flex justify-between">
                                    <span><strong>{t('athletes.selectedPlan', 'Selected Payment Plan')}:</strong></span>
                                  </div>
                                  <div className="pl-4 text-muted-foreground">
                                    {availablePaymentPlans.find(p => p.id === selectedPaymentPlanId)?.name} - {parseFloat(availablePaymentPlans.find(p => p.id === selectedPaymentPlanId)?.monthlyValue || '0').toFixed(2)} {t('common.currency', 'TL')}/{t('common.month', 'month')}
                                  </div>
                                  <div className="flex justify-between">
                                    <span><strong>{t('athletes.billingCycleDays', 'Billing Cycle Days')}:</strong></span>
                                    <span>{(() => {
                                      const selectedPlan = availablePaymentPlans.find(p => p.id === selectedPaymentPlanId);
                                      if (!selectedPlan) return "N/A";
                                      
                                      const today = new Date();
                                      const year = today.getFullYear();
                                      const month = today.getMonth();
                                      const assignDay = selectedPlan.assignDay;
                                      
                                      // Calculate last billing date
                                      let lastBillingDate = new Date(year, month, assignDay);
                                      if (lastBillingDate > today) {
                                        lastBillingDate = new Date(year, month - 1, assignDay);
                                      }
                                      
                                      // Calculate next billing date
                                      let nextBillingDate = new Date(year, month, assignDay);
                                      if (nextBillingDate <= today) {
                                        nextBillingDate = new Date(year, month + 1, assignDay);
                                      }
                                      
                                      const totalBillingDays = Math.ceil((nextBillingDate.getTime() - lastBillingDate.getTime()) / (1000 * 60 * 60 * 24));
                                      const remainingDays = Math.ceil((nextBillingDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
                                      
                                      return `${remainingDays}/${totalBillingDays} (${t('athletes.day', 'Day')} ${assignDay})`;
                                    })()}</span>
                                  </div>
                                  <div className="flex justify-between font-medium text-primary border-t pt-2">
                                    <span><strong>{t('athletes.calculatedAmount', 'Calculated Prorated Amount')}:</strong></span>
                                    <span>{calculatedProrated.toFixed(2)} {t('common.currency', 'TL')}</span>
                                  </div>
                                </div>
                              </div>
                            )}
                            
                            {/* Editable Prorated Amount */}
                            <div className="space-y-2">
                              <Label htmlFor="proratedAmount" className="text-sm">
                                {t('athletes.proratedAmountLabel', 'Prorated Amount')}
                              </Label>
                              <Input
                                id="proratedAmount"
                                type="number"
                                step="0.01"
                                min="0"
                                value={proratedAmount}
                                onChange={(e) => setProratedAmount(e.target.value)}
                                placeholder={t('athletes.enterProratedAmount', 'Enter prorated amount')}
                                className="text-sm"
                              />
                              <p className="text-xs text-muted-foreground">
                                {t('athletes.proratedEditHint', 'You can modify the automatically calculated amount or enter a custom value.')}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
              >
                {t('common.actions.cancel')}
              </Button>
              <Button
                onClick={handleAddAthlete}
                disabled={
                  isLoading || 
                  !selectedAthleteId || 
                  availableAthletes.length === 0 ||
                  (shouldAssignPaymentPlan && !selectedPaymentPlanId)
                }
              >
                {isLoading ? t('common.actions.saving') : t('common.actions.add')}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
