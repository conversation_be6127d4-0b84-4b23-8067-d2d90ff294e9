#!/usr/bin/env node

/**
 * Translation File Comparison and Sync Tool
 * 
 * This script compares Turkish and English translation files to ensure they have
 * the same keys and structure. It can automatically fix missing keys.
 * 
 * Usage:
 *   node scripts/compare-translations.js                # Compare only
 *   node scripts/compare-translations.js --fix          # Compare and auto-fix
 *   node scripts/compare-translations.js --verbose      # Show detailed stats
 *   node scripts/compare-translations.js --fix --verbose # Fix with details
 */

const fs = require('fs');
const path = require('path');

function flattenKeys(obj, prefix = '') {
  const keys = [];
  
  for (const key in obj) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      keys.push(...flattenKeys(obj[key], fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

function compareTranslationFiles(trFile, enFile) {
  try {
    // Read and parse JSON files
    const trContent = JSON.parse(fs.readFileSync(trFile, 'utf8'));
    const enContent = JSON.parse(fs.readFileSync(enFile, 'utf8'));
    
    // Flatten all keys
    const trKeys = flattenKeys(trContent).sort();
    const enKeys = flattenKeys(enContent).sort();
    
    console.log('=== Translation File Comparison ===\n');
    console.log(`Turkish file (${trFile}): ${trKeys.length} keys`);
    console.log(`English file (${enFile}): ${enKeys.length} keys`);
    console.log(`Line count - TR: ${fs.readFileSync(trFile, 'utf8').split('\n').length}, EN: ${fs.readFileSync(enFile, 'utf8').split('\n').length}\n`);
    
    // Find missing keys in English
    const missingInEn = trKeys.filter(key => !enKeys.includes(key));
    if (missingInEn.length > 0) {
      console.log('🔴 Keys missing in English file:');
      missingInEn.forEach(key => console.log(`  - ${key}`));
      console.log();
    }
    
    // Find missing keys in Turkish
    const missingInTr = enKeys.filter(key => !trKeys.includes(key));
    if (missingInTr.length > 0) {
      console.log('🔴 Keys missing in Turkish file:');
      missingInTr.forEach(key => console.log(`  - ${key}`));
      console.log();
    }
    
    // Find common keys
    const commonKeys = trKeys.filter(key => enKeys.includes(key));
    console.log(`✅ Common keys: ${commonKeys.length}`);
    
    if (missingInEn.length === 0 && missingInTr.length === 0) {
      console.log('🎉 All keys match between both files!');
      
      // Check for formatting differences
      const trLines = fs.readFileSync(trFile, 'utf8').split('\n');
      const enLines = fs.readFileSync(enFile, 'utf8').split('\n');
      
      if (trLines.length !== enLines.length) {
        console.log('\n⚠️  Line count difference detected. This might be due to:');
        console.log('   - Different indentation');
        console.log('   - Different line endings');
        console.log('   - Different JSON formatting');
        
        // Check for trailing whitespace or empty lines
        const trEmptyLines = trLines.filter(line => line.trim() === '').length;
        const enEmptyLines = enLines.filter(line => line.trim() === '').length;
        
        console.log(`   - Empty/whitespace lines - TR: ${trEmptyLines}, EN: ${enEmptyLines}`);
      }
    }
    
    return {
      trKeys: trKeys.length,
      enKeys: enKeys.length,
      missingInEn,
      missingInTr,
      equal: missingInEn.length === 0 && missingInTr.length === 0
    };
    
  } catch (error) {
    console.error('Error comparing files:', error.message);
    return null;
  }
}

// Function to get value from nested object by path
function getValueByPath(obj, path) {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);
}

// Function to set value in nested object by path
function setValueByPath(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  target[lastKey] = value;
}

// Function to fix missing keys
function fixMissingKeys(sourceFile, targetFile, missingKeys) {
  if (missingKeys.length === 0) return;
  
  try {
    const sourceContent = JSON.parse(fs.readFileSync(sourceFile, 'utf8'));
    const targetContent = JSON.parse(fs.readFileSync(targetFile, 'utf8'));
    
    console.log(`\n🔧 Fixing ${missingKeys.length} missing keys in ${path.basename(targetFile)}...`);
    
    missingKeys.forEach(keyPath => {
      const value = getValueByPath(sourceContent, keyPath);
      if (value !== undefined) {
        setValueByPath(targetContent, keyPath, `[TRANSLATE] ${value}`);
        console.log(`  ✅ Added: ${keyPath}`);
      }
    });
    
    // Write back with proper formatting
    fs.writeFileSync(targetFile, JSON.stringify(targetContent, null, 2) + '\n', 'utf8');
    console.log(`📝 Updated ${path.basename(targetFile)}`);
    
  } catch (error) {
    console.error('Error fixing missing keys:', error.message);
  }
}

// Main execution
const args = process.argv.slice(2);
const autoFix = args.includes('--fix');
const verbose = args.includes('--verbose');

const trFile = path.join(__dirname, '../public/locales/tr/athletes.json');
const enFile = path.join(__dirname, '../public/locales/en/athletes.json');

if (!fs.existsSync(trFile) || !fs.existsSync(enFile)) {
  console.error('Translation files not found!');
  console.error(`TR: ${trFile} - ${fs.existsSync(trFile) ? 'EXISTS' : 'MISSING'}`);
  console.error(`EN: ${enFile} - ${fs.existsSync(enFile) ? 'EXISTS' : 'MISSING'}`);
  process.exit(1);
}

const result = compareTranslationFiles(trFile, enFile);

if (result && !result.equal) {
  if (autoFix) {
    console.log('\n🔧 Auto-fixing missing keys...');
    
    if (result.missingInEn.length > 0) {
      fixMissingKeys(trFile, enFile, result.missingInEn);
    }
    
    if (result.missingInTr.length > 0) {
      fixMissingKeys(enFile, trFile, result.missingInTr);
    }
    
    console.log('\n🔄 Re-checking after fixes...');
    compareTranslationFiles(trFile, enFile);
  } else {
    console.log('\n💡 Run with --fix flag to automatically add missing keys');
    console.log('   Example: node scripts/compare-translations.js --fix');
  }
} else if (result) {
  console.log('\n✨ Translation files are perfectly synchronized!');
}

if (verbose && result) {
  console.log('\n📊 Detailed Statistics:');
  console.log(`   Total translation keys: ${result.trKeys}`);
  console.log(`   Files are identical: ${result.equal}`);
}
