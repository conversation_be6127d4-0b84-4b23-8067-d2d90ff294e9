{"title": "Sms Yönetimi", "description": "Sms yapılandırmalarını yönetin, mesaj gö<PERSON> ve kullanımı takip edin", "navigation": {"overview": "Sms Genel Bakış", "configuration": "Yapılandırma", "balance": "Bakiye", "history": "Geçmiş"}, "overview": {"title": "Sms Yönetimi", "description": "Sms yapılandırmalarını yönetin, mesaj gö<PERSON> ve kullanımı takip edin"}, "balance": {"title": "Sms Bakiyesi", "description": "Sms kredilerinizi ve kullanımınızı yönetin", "credits": "Mevcut Sms kredisi", "status": {"sufficient": "<PERSON><PERSON><PERSON>", "low": "Düşük", "empty": "Boş"}, "warnings": {"empty": "Sms bakiyeniz boş. Sms mesajı göndermek için kredi ekleyin.", "low": "Sms bakiyeniz düşük. Yakında daha fazla kredi eklemeyi düşünün."}, "current": {"title": "Mevcut Ba<PERSON>ye", "credits": "Mevcut Sms kredisi"}, "history": {"title": "Bakiye Geçmişi", "lastUpdated": "<PERSON> g<PERSON><PERSON><PERSON><PERSON>", "accountCreated": "<PERSON><PERSON><PERSON>", "never": "<PERSON><PERSON><PERSON>", "unknown": "Bilinmiyor", "tip": "Ödeme hatırlatmaları ve takım iletişimi için kesintisiz hizmet sağlamak amacıyla en az 50 Sms kredisi bulundurmanızı öneririz."}, "management": {"disabled": "Ödeme entegrasyonu devam ediyor - sadece manuel <PERSON>", "temporarilyDisabled": "SMS bakiye yönetimi, ödeme sistemimizle entegrasyon sırasında geçici olarak devre dışı bırakıldı. <PERSON> baki<PERSON> güncellemeleri için yöneticinizle iletişime geçin."}, "add": {"title": "Sms Kredisi Ekle", "description": "Ödeme hatırlatmaları ve takım mesajları göndermek için bakiyenize kredi ekleyin", "quickAmounts": "Hızlı Ekleme Miktarları", "customAmount": "<PERSON><PERSON>", "placeholder": "<PERSON><PERSON><PERSON> girin...", "addCredits": "<PERSON><PERSON><PERSON>", "preview": {"current": "Mevcut Bakiye:", "adding": "Ekleniyor:", "newBalance": "<PERSON>ni <PERSON>:", "credits": "Krediler:", "pricePerCredit": "<PERSON><PERSON><PERSON>:", "totalPrice": "Toplam Fiyat:", "tier": "Fiyatlandırma <PERSON>ı", "calculating": "<PERSON>yat he<PERSON>planıyor..."}}, "info": {"title": "Sms Kredi Bilgileri", "description": "Sms kredilerini ve kullanımını anlama", "usage": {"title": "<PERSON><PERSON><PERSON>", "items": ["1 kredi = 1 Sms mesajı (160 karaktere kadar)", "<PERSON><PERSON> uzun mesajlar birden fazla kredi kullana<PERSON>ir", "Başarısız mesajlar kredi tüketmez", "<PERSON><PERSON><PERSON> hi<PERSON><PERSON> zaman sona ermez"]}, "types": {"title": "<PERSON><PERSON>", "items": ["Ödeme hatırlatma Sms'i", "Tak<PERSON>m duyuru me<PERSON>ı", "S<PERSON><PERSON><PERSON> ö<PERSON> me<PERSON>", "Otomatik zamanlanmış hatırlatmalar"]}}, "payment": {"title": "Ödeme Bilgileri", "description": "SMS kredi satın alma işleminizi tamamlayın", "cardNumber": "<PERSON><PERSON>", "expiryDate": "<PERSON>", "cvv": "CVV", "cardHolder": "<PERSON><PERSON> <PERSON>", "payNow": "Ş<PERSON><PERSON>", "success": "{credits} SMS kredisi başarıyla satın alındı!", "error": "Ödeme başarısız. Lütfen tekrar deneyin.", "demoNote": "Demo Modu: <PERSON><PERSON> simüle edilmiş bir ödeme arayüzüdür. <PERSON><PERSON><PERSON>, bu gerçek bir ödeme işlemcisi ile entegre olacaktır. Şimdilik 'Şimdi Öde' butonuna tıklamak test amaçlı hesabınıza kredi ekleyecektir.", "summary": {"credits": "SMS Kredileri", "pricePerCredit": "<PERSON><PERSON><PERSON>", "total": "Toplam Tutar"}}, "pricing": {"title": "SMS Fiyatlandırması", "description": "<PERSON><PERSON><PERSON><PERSON> fi<PERSON><PERSON><PERSON> ka<PERSON> - ne kadar çok alırsanız, kredi başına o kadar az ödersiniz", "perCredit": "kredi ba<PERSON><PERSON>na", "tiers": {"starter": "Başlangıç", "standard": "<PERSON><PERSON>", "professional": "Profesyonel", "enterprise": "<PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON>"}, "notes": {"title": "Fiyatlandırma Notları", "graduated": "<PERSON><PERSON><PERSON>i <PERSON>ı<PERSON> - büyük alımlar için kredi başına daha düşük maliyet", "automatic": "Fiyatlandırma katman<PERSON>ı satın alma miktarına göre otomatik olarak uygulanır", "noExpiry": "SMS kredileri hiçbir zaman sona ermez", "multiSms": "Uzun mesajlar birden fazla SMS kredisi kullanabilir"}}, "messages": {"success": "{amount} Sms kredisi başarıyla eklendi", "error": "Sms kredisi eklenemedi", "successBalance": "Krediler başarıyla eklendi! Yeni bakiyeniz {balance} kredi.", "errorBalance": "Kredi eklenemedi. Lütfen tekrar deneyin.", "waitPriceCalculation": "Fiyat hesaplanıyor... Lütfen bekleyin.", "templateDownloadError": "Şablon indirilemedi. Lütfen tekrar deneyin."}}, "stats": {"totalSent": "Toplam Gönderilen Sms", "paymentReminders": "Ödeme <PERSON>ı", "teamMessages": "Ta<PERSON><PERSON>m <PERSON>", "allTime": "<PERSON><PERSON><PERSON>", "paymentReminderSms": "Ödeme hatırlatma Sms'i", "teamMessagesSent": "G<PERSON>nderilen takım mesajları"}, "cards": {"configuration": {"title": "Yapılandırma", "description": "Sms şablonlarını ve hatırlatma ayarlarını yönetin", "content": "Ödeme hatırlatma şablonlarını, takım mesaj a<PERSON>ını ve otomatik hatırlatma programlarını yapılandırın.", "action": "Yapılandırmayı Yönet"}, "sendSms": {"title": "Sms Gönder", "description": "Ödeme hatırlatmaları ve takım mesajları gönderin", "content": "Sporculara ve velilere ödeme hatırlatma Sms'i, takım duyu<PERSON>ları veya özel mesajlar gönderin.", "paymentReminders": "Ödeme <PERSON>ı", "teamMessages": "Ta<PERSON><PERSON>m <PERSON>"}, "balance": {"title": "Bakiye Yönetimi", "description": "Sms kredilerinizi ve kullanımınızı yönetin", "content": "Hesabınıza Sms kredisi ekleyin ve kesintisiz hizmet için kullanımınızı izleyin.", "manageBalance": "Bakiyeyi Yönet"}, "history": {"title": "Sms Geçmişi", "description": "Gönderilen Sms mesajlarınızı izleyin", "content": "Gönderilen Sms mesajlarınızın ayrıntılı geçmişini gö<PERSON>ü<PERSON><PERSON><PERSON>in, arama yapın ve filtreleyin.", "action": "Geçmişi Görüntüle", "searchPlaceholder": "Sms ara...", "noResults": "<PERSON><PERSON><PERSON> bulu<PERSON>adı", "filters": {"type": "<PERSON><PERSON><PERSON>", "status": "Durum", "sender": "<PERSON><PERSON><PERSON><PERSON>", "dateRange": "<PERSON><PERSON><PERSON>"}}}, "quickActions": {"title": "Hızlı İşlemler", "description": "Yaygın Sms yönetim görevleri", "addCredits": "<PERSON><PERSON><PERSON>", "pendingReminders": "<PERSON><PERSON><PERSON>", "overdueReminders": "Geciken <PERSON>"}, "configuration": {"title": "Sms Yapılandırması", "description": "Sms şablonlarını ve otomatik hatırlatma ayarlarını yapılandırın", "current": {"title": "Mevcut Yapılandırma", "active": "Aktif", "inactive": "<PERSON><PERSON><PERSON>", "version": "S<PERSON>r<PERSON><PERSON>", "lastUpdated": "<PERSON> g<PERSON><PERSON><PERSON><PERSON>", "noConfig": "Aktif Sms yapılandırması bulunamadı"}, "update": {"title": "Sms Yapılandırmasını Güncelle", "description": "Sms şablonlarını ve hatırlatma programlarını yapılandırın. Değişiklikler yeni bir sürüm oluşturacaktır."}, "templates": {"pending": {"title": "<PERSON><PERSON><PERSON>", "placeholder": "Bekleyen ödeme hatırlatmaları için şablon girin...", "days": "Bekleyen Ödeme Hatırlatma Günleri", "daysDescription": "Vade tarihinden önceki <PERSON> (negatif <PERSON><PERSON><PERSON> kullanın)", "default": "Sayın {{parentName}} {{parentSurname}}, {{athleteName}} öğren<PERSON>i adına {{clubName}} için {{amount}} tutarında ödemenizin son ödeme tarihi {{paymentDueDate}} tarihindedir. Ödeme tarihinin yaklaştığını hatırlatmak isteriz. Lütfen ödemenizi zamanında yapınız."}, "overdue": {"title": "Geciken Ödeme <PERSON>", "placeholder": "Geciken ödeme hatırlatmaları için şablon girin...", "days": "Geciken Ödeme Hatırlatma Günleri", "daysDescription": "Vade tarihinden <PERSON><PERSON> (pozitif sayılar kullanın)", "default": "Sayın {{parentName}} {{parentSurname}}, {{athleteName}} öğrencisi adına {{clubName}} için vadesi geçmiş {{amount}} tutarında borcunuz bulunmaktadır. Lütfen ödemenizi en kısa sürede tamamlayınız."}}, "variables": {"title": "Kullanılabilir Şablon Değişkenleri", "description": "Sms şablonlarınızda bu değişkenleri kullanın. Gerçek değerlerle otomatik olarak değiştirilecektir.", "payment": "<PERSON><PERSON><PERSON>", "organization": "Organizasyon <PERSON>i", "athleteName": "Sporcunun tam adı", "parentName": "<PERSON><PERSON><PERSON> tam adı", "amount": "Ödeme tutarı", "paymentDueDate": "Ödeme vade tarihi", "clubName": "<PERSON><PERSON><PERSON><PERSON> adı", "example": "Örnek Şablon"}, "preview": {"athleteName": "<PERSON><PERSON>", "parentName": "<PERSON><PERSON>", "clubName": "Spor Akademisi", "amount": "150,00 TL", "paymentDueDate": "15.01.2024"}, "actions": {"preview": "<PERSON><PERSON><PERSON><PERSON>", "hidePreview": "Önizlemeyi Gizle", "reset": "Sıfırla", "save": "Yapılandırmayı Kaydet", "activate": "Etkinleştir", "deactivate": "Devre Dışı Bırak"}, "messages": {"success": "SMS yapılandırması başarıyla güncellendi", "activated": "SMS yapılandırması başarıyla etkinleştirildi", "deactivated": "SMS yapılandırması başarıyla devre dışı bırakıldı", "error": "SMS yapılandırması güncellenemedi"}}, "test": {"title": "Sms Test", "description": "Sms yapılandırmanızı test edin ve her şeyin doğru çalıştığını doğrulayın", "balance": {"title": "Sms Bakiye Durumu", "available": "Mevcut Sms kredisi"}, "form": {"title": "Test Sms Gönderimi", "description": "Yapılandırmanızın doğru çalıştığını doğrulamak için test Sms'i gönderin. Bu, bakiyenizden 1 Sms kredisi kullanacaktır.", "phoneNumber": "Telefon Numarası", "phonePlaceholder": "+905551234567", "phoneHelp": "<PERSON><PERSON><PERSON> kodunu dahil edin (örn. ABD için +1, <PERSON><PERSON><PERSON><PERSON><PERSON> için +90)", "message": "Test Mesajı", "messagePlaceholder": "Test mesajınızı girin...", "messageHelp": "Tek Sms için mesajları 160 karakterin altında tutun. Daha uzun mesajlar bölünebilir.", "characters": "karakter", "sendTest": "Test Sms Gönder"}, "results": {"success": "Test Sms baş<PERSON><PERSON><PERSON> gönderildi! {count} mesaj <PERSON>.", "error": "Test Sms gönderilemedi. Lütfen yapılandırmanızı kontrol edin.", "unknownError": "Bilinmeyen hata oluştu"}, "notes": {"title": "<PERSON><PERSON><PERSON><PERSON>", "testMode": "Sistem şu anda test için sahte Sms sağlayıcısı kullanacak şekilde yapılandırılmıştır. Gerçek telefon numaralarına gerçek Sms mesajı gönderilmeyecektir.", "beforeLive": {"title": "Canlıya geçmeden önce:", "items": ["Ortam değişkenlerinde NetGSM kimlik bilgilerini yapılandırın", "Sms servisinde MockSmsProvider'dan <PERSON>GsmProvider'a geçin", "Kontrol ettiğiniz gerçek bir telefon numarasıyla test edin", "Bakiyenize yeterli Sms kredisi ekleyin", "Sms şablonlarınızı ve hatırlatma programlarınızı yapılandırın"]}, "features": {"title": "Mevcut Sms Özellikleri:", "items": ["Ödeme hatırlatma Sms'i (bekleyen ve geciken)", "Tak<PERSON>m duyuru me<PERSON>ı", "Seçilen sporculara özel mesajlar", "Otomatik hatırlatma zamanlama", "Değişkenli şablon tabanlı mesajlaşma", "Sms bakiye yönetimi ve takibi"]}}, "mockProvider": "Bu sistem şu anda test için sahte Sms sağlayıcısı kullanıyor. Telefon numarasına gerçek Sms gönderilmeyecektir. Simüle edilen Sms gönderim sürecini görmek için konsol loglarını kontrol edin."}, "sending": {"paymentReminder": {"title": "Ödeme Hatırlatma Sms Gönder", "descriptionSingle": "1 seçili ödeme için Sms hatırlatması gönder", "descriptionPlural": "{count} se<PERSON><PERSON> ödeme için Sms hatırlatması gönder", "selectedPayments": "<PERSON><PERSON><PERSON>", "templateType": "Şablon Türü", "pending": "Be<PERSON>en Ödeme <PERSON>", "overdue": "Geciken Ödeme <PERSON>", "customTemplate": "<PERSON><PERSON>", "combinePayments": "<PERSON><PERSON><PERSON> ba<PERSON><PERSON>na birden fazla ödemeyi birleştir", "combinePaymentsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, her spor<PERSON> i<PERSON>in tüm ödemeler toplam tutar ile tek bir SMS'te birleştirilecektir. Vade tarihi de<PERSON>keni kullanılamayacaktır.", "combiningPaymentsNote": "Not: <PERSON><PERSON><PERSON> ba<PERSON>ına birden fazla ödeme tek SMS mesajında birleştirilecektir.", "dueDateNotAvailableWhenCombining": "Ödemeler birleştirilirken vade tarihi kullanı<PERSON>az.", "messageTemplate": "<PERSON><PERSON>", "customPlaceholder": "Özel Sms şablonunuzu girin...", "availableVariables": "Kullanılabil<PERSON>", "preview": "<PERSON><PERSON><PERSON><PERSON>", "hidePreview": "Önizlemeyi Gizle", "warningRecipientSingle": "Bu, 1 alıcıya Sms mesajı gönderecektir.", "warningRecipientPlural": "<PERSON><PERSON>, {count} alıcıya Sms mesajı gönderecektir.", "warningSmsCountSingle": "Toplam: Bakiyenizden {total} Sms kredisi tüketilecektir.", "warningSmsCountPlural": "Her alıcı {smsPerRecipient} Sms mesajı alacaktır. Toplam: Bakiyenizden {total} Sms kredisi tüketilecektir.", "warningLongMessage": "Not: Mesaj <PERSON> birden fazla Sms parçası gerektiriyor. Alıcı isimleri ve diğer değişkenlere bağlı olarak gerçek sayı biraz değişebilir.", "warningSingle": "Bu, 1 alıcıya Sms mesajı gönderecek ve bakiyenizden 1 Sms kredisi tüketecektir.", "warningPlural": "<PERSON><PERSON>, {count} alıcıya Sms mesajı gönderecek ve bakiyenizden {count} Sms kredisi tüketecektir.", "send": "Sms Gönder", "successSingle": "1 alıcıya Sms hatırlatması başarıyla gönderildi", "successPlural": "{count} alıcıya Sms hatırlatması başarıyla gönderildi", "error": "Sms hatırlatmaları gönderilemedi"}, "teamMessage": {"title": "<PERSON><PERSON><PERSON>m <PERSON>", "description": "{teamName} takım üyelerine Sms gönder", "athletesWithPhoneSingle": "Geçerli telefon numarası olan 1 sporcu", "athletesWithPhonePlural": "Geçerli telefon numarası olan {count} sporcu", "message": "<PERSON><PERSON>", "messagePlaceholder": "Takıma mesajınızı girin...", "sendToAll": "<PERSON>üm takım üyelerine g<PERSON>", "selectRecipients": "Alıcıları Seç", "selectAll": "Tümünü Seç", "clear": "<PERSON><PERSON><PERSON>", "noAthletes": "Geçerli telefon numarası olan sporcu yok", "selected": "Seçilen:", "noRecipients": "Mesaj göndermek için en az bir alıcı seçin.", "warningRecipientSingle": "Bu, 1 alıcıya Sms mesajı gönderecektir.", "warningRecipientPlural": "<PERSON><PERSON>, {count} alıcıya Sms mesajı gönderecektir.", "warningSmsCountSingle": "Toplam: Bakiyenizden {total} Sms kredisi tüketilecektir.", "warningSmsCountPlural": "Her alıcı {smsPerRecipient} Sms mesajı alacaktır. Toplam: Bakiyenizden {total} Sms kredisi tüketilecektir.", "warningLongMessage": "Not: Mesaj <PERSON> birden fazla Sms parçası gerektiriyor. Alıcı isimleri ve diğer değişkenlere bağlı olarak gerçek sayı biraz değişebilir.", "warningSingle": "Bu, 1 alıcıya Sms mesajı gönderecek ve bakiyenizden 1 Sms kredisi tüketecektir.", "warningPlural": "<PERSON><PERSON>, {count} alıcıya Sms mesajı gönderecek ve bakiyenizden {count} Sms kredisi tüketecektir.", "successSingle": "Takım mesajı 1 alıcıya başarıyla gönderildi", "successPlural": "<PERSON><PERSON><PERSON><PERSON> mesajı {count} al<PERSON><PERSON><PERSON>ya başarıyla gö<PERSON>ildi", "error": "Takım mesajı gönderilemedi"}, "templateSms": {"successSingle": "Şablon SMS 1 alıcıya başarıyla gönderildi", "successPlural": "Şablon SMS {count} alıcıya başarıyla gönderildi", "error": "Şablon SMS gönderilemedi"}, "bulk": {"title": "Toplu Sms Hatırlatmaları", "description": "Birden fazla alıcıya aynı anda ödeme hatırlatma Sms'i gönderin", "pending": "<PERSON><PERSON><PERSON>", "overdue": "<PERSON><PERSON><PERSON><PERSON>", "totalEligible": "Toplam Uygun", "quickActions": "Hızlı İşlemler", "selected": "seç<PERSON>", "clearSelection": "<PERSON><PERSON><PERSON><PERSON>", "selectAll": "Tümünü Seç", "sendPending": "Bekleyen Hatırlatmaları Gönder", "sendOverdue": "Geciken Hatırlatmaları Gönder", "sendToAllPending": "<PERSON><PERSON><PERSON>", "sendToAllOverdue": "<PERSON><PERSON><PERSON>", "selectPayments": "Sms için <PERSON>", "noPayments": "<PERSON><PERSON>me bulunamadı", "noEligible": "Sms hatırlatması için uygun ödeme yok", "statistics": {"pendingCount": "{count} be<PERSON>en ödeme{plural}", "overdueCount": "{count} g<PERSON>iken ödeme{plural}", "eligibleCount": "{count} u<PERSON>gun ödeme{plural}"}, "notes": {"title": "<PERSON><PERSON><PERSON><PERSON>", "items": ["Sadece geçerli veli telefon numarası olan ödemeler uygundur", "<PERSON><PERSON><PERSON><PERSON> her 160 karakter, bakiyenizden 1 SMS kredisi tüketecektir", "Mesajlar anında gönderilir ve iptal edilemez", "Başarısız mesajlar kredi tüketmez"]}}}, "actions": {"sendSmsReminder": "Sms Hatırlatması Gönder", "sendTeamSms": "Takım Sms Gönder", "showPreview": "Önizlemeyi Göster", "hidePreview": "Önizlemeyi Gizle", "selectAll": "Tümünü Seç", "clearSelection": "<PERSON><PERSON><PERSON><PERSON>", "addCredits": "<PERSON><PERSON><PERSON>"}, "templates": {"title": "Şablon SMS", "description": "Seçili kullanıcılara şablon mesajları gönderin", "types": {"general": "<PERSON><PERSON>", "event": "Etkin<PERSON>", "meeting": "Toplantı Çağrısı", "holiday": "<PERSON><PERSON>", "custom": "<PERSON><PERSON>", "passwordReset": "Şifre <PERSON>ırl<PERSON>"}, "passwordReset": {"message": "Şifre sıfırlama kodunuz: {{code}}\n\nBu kodu kimseyle paylaşmayınız. Kod 60 dakika içinde geçerliliğini yitirecektir.\n\n- Sports Club Management"}, "form": {"selectUsers": "Alıcıları Seçin", "selectAll": "Tümünü Seç", "selectNone": "Hiçbirini Seçme", "selectedCount": "{count} se<PERSON><PERSON>", "templateType": "Şablon Türü", "customMessage": "<PERSON><PERSON>", "customPlaceholder": "Özel mesajınızı girin...", "useCustom": "<PERSON><PERSON> mesaj kullan", "sendTemplate": "Şablon SMS Gönder"}}, "steps": {"selectRecipients": "Alıcıları Seçin", "customizeMessage": "Mesajı Ö<PERSON>leştirin", "reviewAndSend": "İnceleyin ve Gönderin", "step": "<PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON>", "nextStep": "<PERSON><PERSON><PERSON>", "cancel": "İptal", "confirmation": "SMS Gönderimi Onayı", "confirmMessage": "SMS hatırlatmaları göndermek istediğinizden emin misiniz? Bu işlem geri alınamaz.", "recipients": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smsCredits": "SMS Kredileri", "yesSend": "<PERSON><PERSON>, SMS Gönder"}, "colorful": {"clickToSendReminders": "Hatırlatma göndermek için tıklayın", "clickForTemplateSms": "Şablon SMS için tıklayın", "noEligibleUsers": "Uygun kullanıcı yok", "noOverduePayments": "Geciken ödeme yok", "noPendingPayments": "Bekleyen ödeme yok", "totalWithPhoneNumbers": "Telefon numarası olan toplam", "templateType": "Şablon Türü", "customMessage": "<PERSON><PERSON>", "defaultTemplate": "Varsayılan Şablon", "hidePreview": "Önizlemeyi Gizle", "showPreview": "Önizlemeyi Göster", "totalSmsCredits": "Toplam SMS kredisi", "smsPerRecipient": "Alıcı başına SMS", "messagePreview": "<PERSON><PERSON>", "sendImmediately": "Bu işlem SMS mesajlarını hemen gönderecek ve geri alınamaz.", "reviewCarefully": "Devam etmeden önce lütfen tüm detayları dikkatli bir şekilde inceleyin.", "paymentReminderType": "Ödeme Hatırlatma Türü", "pendingPaymentReminder": "Be<PERSON>en Ödeme <PERSON>", "overduePaymentReminder": "Geciken Ödeme <PERSON>", "basedOnSelectedPayments": "Seçili ö<PERSON>melere göre"}, "status": {"pending": "<PERSON><PERSON><PERSON>", "sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failed": "Başarısız", "overdue": "Gec<PERSON><PERSON>", "paid": "Ödendi", "cancelled": "İptal Edildi", "partially_paid": "<PERSON><PERSON><PERSON><PERSON>"}, "common": {"cancel": "İptal", "save": "<PERSON><PERSON>", "send": "<PERSON><PERSON><PERSON>", "loading": "Yükleniyor...", "error": "<PERSON><PERSON>", "success": "Başarılı", "credits": "kredi", "days": "g<PERSON>n", "messageTemplate": "<PERSON><PERSON>", "customTemplate": "<PERSON><PERSON>", "defaultTemplate": "Varsayılan Şablon", "characters": "karakter", "approximately": "(yaklaşık)", "selectAll": "Tümünü Seç", "selectNone": "Hiçbirini Seçme", "selected": "seç<PERSON>", "pleaseSelectAtLeastOne": "Lütfen en az bir ödeme seçin", "pleaseSelectAtLeastOneUser": "Lütfen en az bir kullanıcı seçin", "templateSmsSentTo": "Şablon SMS {{count}} alıcıya gönderildi", "failedToSendTemplateSms": "Şablon SMS gönderimi başarısız", "preview": "<PERSON><PERSON><PERSON><PERSON>", "smsPerRecipient": "Alıcı başına SMS", "totalSmsCredits": "Toplam SMS kredisi", "messagePreview": "<PERSON><PERSON>", "creditUsageInfo": "SMS kredi kullanımı değişken uzunluklarına (isim<PERSON>, okul isimleri, tutarlar vb.) göre değişebilir. Bu bir tahmindir. Gerçek tutar bakiyenizden düşülecektir.", "estimatedCreditUsage": "<PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "status": "Durum", "receiver": "Alıcı", "message": "<PERSON><PERSON>", "sentAt": "Gönderildi<PERSON><PERSON>", "sender": "<PERSON><PERSON><PERSON><PERSON>", "selectType": "<PERSON><PERSON><PERSON><PERSON>", "selectStatus": "<PERSON><PERSON><PERSON>", "selectSender": "<PERSON><PERSON><PERSON><PERSON>", "fromDate": "Başlangıç <PERSON>", "toDate": "Bitiş Tarihi", "selectFromDate": "Başlangıç tarihi se<PERSON>", "selectToDate": "Bitiş tarihi se<PERSON>", "sent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failed": "Başarısız", "types": {"paymentReminder": "<PERSON><PERSON><PERSON>", "teamMessage": "Takım", "custom": "<PERSON><PERSON>"}}}