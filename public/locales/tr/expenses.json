{"expenses": {"title": "<PERSON><PERSON><PERSON>", "description": "Giderlerinizi yönetin ve harcamaları takip edin", "new": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "viewDetails": "Gider Detayları", "details": {"date": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "instructor": "Eğitmen", "facility": "<PERSON><PERSON>", "expenseDetails": "<PERSON>ider Bilgileri", "expenseDate": "<PERSON><PERSON>", "expenseType": "<PERSON><PERSON>ü<PERSON>"}, "filters": {"fromDate": "Başlangıç <PERSON>", "toDate": "Bitiş Tarihi", "selectFromDate": "Başlangıç tarihi se<PERSON>", "selectToDate": "Bitiş tarihi se<PERSON>"}, "categories": {"equipment": "<PERSON><PERSON><PERSON><PERSON>", "insurance": "Sigorta", "other": "<PERSON><PERSON><PERSON>", "salary": "Maaş", "rent": "<PERSON>"}, "types": {"general": "<PERSON><PERSON>", "instructorOnly": "Eğitmen <PERSON>şkili", "facilityOnly": "<PERSON><PERSON>", "instructorFacility": "<PERSON><PERSON><PERSON><PERSON> ve <PERSON>"}, "actions": {"edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Sil", "view": "Detayları Görüntüle", "save": "<PERSON><PERSON><PERSON>", "openMenu": "Menüyü a<PERSON>", "deleteConfirm": "Bu gideri silmek istediğinizden emin misiniz?"}, "messages": {"requiredFields": "Lütfen tüm gerekli alanları doldurun", "invalidAmount": "Lütfen geçerli bir tutar girin", "createSuccess": "Gider başarıyla oluşturuldu", "createError": "Gider oluşturulamadı", "updateSuccess": "<PERSON><PERSON> başar<PERSON><PERSON> g<PERSON>", "updateError": "<PERSON><PERSON>", "deleteSuccess": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON><PERSON>", "deleteError": "<PERSON><PERSON> si<PERSON>", "confirmDelete": "Bu gideri silmek istediğinizden emin misiniz?", "deleteWarning": "Bu işlem geri alınamaz."}, "deleteDialog": {"title": "<PERSON><PERSON><PERSON>", "description": "Bu gideri silmek istediğinizden emin misiniz? Bu işlem geri alınamaz."}, "placeholders": {"enterAmount": "0.00", "selectCategory": "<PERSON><PERSON><PERSON>", "enterDescription": "Gider açıklaması girin", "searchExpenses": "Giderleri ara...", "selectInstructor": "<PERSON><PERSON><PERSON><PERSON>", "selectFacility": "<PERSON><PERSON>", "filterByCategory": "Kategoriye göre filtrele", "searchMinChars": "En az 3 karakter girin", "selectDate": "<PERSON><PERSON><PERSON>"}}}