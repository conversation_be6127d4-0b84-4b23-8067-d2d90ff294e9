{"title": "Sms Management", "description": "Manage Sms configurations, send messages, and track usage", "navigation": {"overview": "Sms Overview", "configuration": "Configuration", "balance": "Balance", "history": "History"}, "overview": {"title": "Sms Management", "description": "Manage Sms configurations, send messages, and track usage"}, "balance": {"title": "Sms Balance", "description": "Manage your Sms credits and monitor usage", "credits": "Sms credits available", "status": {"sufficient": "Sufficient", "low": "Low", "empty": "Empty"}, "warnings": {"empty": "Sms balance is empty. Please add credits to send Sms messages.", "low": "Sms balance is low. Consider adding more credits soon."}, "current": {"title": "Current Balance", "credits": "Sms credits available"}, "history": {"title": "Balance History", "lastUpdated": "Last updated", "accountCreated": "Account created", "never": "Never", "unknown": "Unknown", "tip": "We recommend maintaining at least 50 Sms credits to ensure uninterrupted service for payment reminders and team communications."}, "management": {"disabled": "Payment integration in progress - manual management only", "temporarilyDisabled": "SMS balance management is temporarily disabled while we integrate with our payment system. Contact your administrator for manual balance updates."}, "add": {"title": "Add Sms Credits", "description": "Add credits to your Sms balance to send payment reminders and team messages", "quickAmounts": "Quick Add Amounts", "customAmount": "Custom Amount", "placeholder": "Enter amount...", "addCredits": "Add Credits", "preview": {"current": "Current Balance:", "adding": "Adding:", "newBalance": "New Balance:", "credits": "Credits:", "pricePerCredit": "Price per Credit:", "totalPrice": "Total Price:", "tier": "Pricing Tier", "calculating": "Calculating price..."}}, "info": {"title": "Sms Credit Information", "description": "Understanding Sms credits and usage", "usage": {"title": "Credit Usage", "items": ["1 credit = 1 Sms message (up to 160 characters)", "Longer messages may use multiple credits", "Failed messages do not consume credits", "Credits never expire"]}, "types": {"title": "Message Types", "items": ["Payment reminder Sms", "Team announcement messages", "Custom messages to athletes", "Automatic scheduled reminders"]}}, "payment": {"title": "Payment Information", "description": "Complete your SMS credit purchase", "cardNumber": "Card Number", "expiryDate": "Expiry Date", "cvv": "CVV", "cardHolder": "Card Holder Name", "payNow": "Pay Now", "success": "Successfully purchased {credits} SMS credits!", "error": "Payment failed. Please try again.", "demoNote": "Demo Mode: This is a simulated payment interface. In production, this would integrate with a real payment processor. For now, clicking 'Pay Now' will add credits to your account for testing purposes.", "summary": {"credits": "SMS Credits", "pricePerCredit": "Price per Credit", "total": "Total Amount"}}, "pricing": {"title": "SMS Pricing", "description": "Graduated pricing tiers - the more you buy, the less you pay per credit", "perCredit": "per credit", "tiers": {"starter": "Starter", "standard": "Standard", "professional": "Professional", "enterprise": "Enterprise", "custom": "Custom"}, "notes": {"title": "Pricing Notes", "graduated": "Graduated pricing - lower cost per credit for larger purchases", "automatic": "Pricing tiers are applied automatically based on purchase amount", "noExpiry": "SMS credits never expire", "multiSms": "Long messages may use multiple SMS credits"}}, "messages": {"success": "Successfully added {amount} Sms credits", "error": "Failed to add Sms credits", "successBalance": "Credits added successfully! Your new balance is {balance} credits.", "errorBalance": "Failed to add credits. Please try again.", "waitPriceCalculation": "Calculating price... Please wait.", "templateDownloadError": "Failed to download template. Please try again."}}, "stats": {"totalSent": "Total Sms Sent", "paymentReminders": "Payment Reminders", "teamMessages": "Team Messages", "allTime": "All time", "paymentReminderSms": "Payment reminder Sms", "teamMessagesSent": "Team messages sent"}, "cards": {"configuration": {"title": "Configuration", "description": "Manage Sms templates and reminder settings", "content": "Configure payment reminder templates, team message settings, and automatic reminder schedules.", "action": "Manage Configuration"}, "sendSms": {"title": "Send Sms", "description": "Send payment reminders and team messages", "content": "Send payment reminder Sms, team announcements, or custom messages to athletes and parents.", "paymentReminders": "Payment Reminders", "teamMessages": "Team Messages"}, "balance": {"title": "Balance Management", "description": "Manage your Sms credits and usage", "content": "Add Sms credits to your account and monitor your usage to ensure uninterrupted service.", "manageBalance": "Manage Balance"}, "history": {"title": "SMS History", "description": "View sent SMS messages and logs", "content": "Track all sent SMS messages, view delivery status, and filter by type, date, or recipient.", "action": "View SMS History", "searchPlaceholder": "Search by phone number or message...", "noResults": "No SMS messages found", "filters": {"type": "Message Type", "status": "Status", "sender": "Sender", "dateRange": "Date Range"}}}, "quickActions": {"title": "Quick Actions", "description": "Common Sms management tasks", "addCredits": "Add Credits", "pendingReminders": "Pending Reminders", "overdueReminders": "Overdue Reminders"}, "configuration": {"title": "Sms Configuration", "description": "Configure Sms templates and automatic reminder settings", "current": {"title": "Current Configuration", "active": "Active", "inactive": "Inactive", "version": "Version", "lastUpdated": "Last updated", "noConfig": "No active Sms configuration found"}, "update": {"title": "Update Sms Configuration", "description": "Configure Sms templates and reminder schedules. Changes will create a new version."}, "templates": {"pending": {"title": "Pending Payment Template", "placeholder": "Enter template for pending payment reminders...", "days": "Pending Payment Reminder Days", "daysDescription": "Days before due date to send reminders (use negative numbers)", "default": "Dear {{parentName}} {{parentSurname}}, your child {{athleteName}}'s payment of {{amount}} for {{clubName}} is due on {{paymentDueDate}}. The payment deadline is approaching. Please make your payment on time."}, "overdue": {"title": "Overdue Payment Template", "placeholder": "Enter template for overdue payment reminders...", "days": "Overdue Payment Reminder Days", "daysDescription": "Days after due date to send reminders (use positive numbers)", "default": "Dear {{parentName}} {{parentSurname}}, you have an overdue payment of {{amount}} for {{athleteName}} student at {{clubName}}. Please complete your payment as soon as possible."}}, "variables": {"title": "Available Template Variables", "description": "Use these variables in your Sms templates. They will be automatically replaced with actual values.", "payment": "Payment Variables", "organization": "Organization Variables", "athleteName": "Athlete's full name", "parentName": "<PERSON><PERSON>'s full name", "amount": "Payment amount", "paymentDueDate": "Payment due date", "clubName": "Club name", "example": "Example Template"}, "preview": {"athleteName": "<PERSON>", "parentName": "<PERSON>", "clubName": "Sports Academy", "amount": "150.00 TL", "paymentDueDate": "2024-01-15"}, "actions": {"preview": "Preview", "hidePreview": "Hide Preview", "reset": "Reset", "save": "Save Configuration", "activate": "Activate", "deactivate": "Deactivate"}, "messages": {"success": "SMS configuration updated successfully", "activated": "SMS configuration activated successfully", "deactivated": "SMS configuration deactivated successfully", "error": "Failed to update SMS configuration"}}, "test": {"title": "Sms Test", "description": "Test your Sms configuration and verify everything is working correctly", "balance": {"title": "Sms Balance Status", "available": "Available Sms credits"}, "form": {"title": "Test Sms Sending", "description": "Send a test Sms to verify your configuration is working correctly. This will use 1 Sms credit from your balance.", "phoneNumber": "Phone Number", "phonePlaceholder": "+1234567890", "phoneHelp": "Include country code (e.g., +1 for US, +90 for Turkey)", "message": "Test Message", "messagePlaceholder": "Enter your test message...", "messageHelp": "Keep messages under 160 characters for single Sms. Longer messages may be split.", "characters": "characters", "sendTest": "Send Test Sms"}, "results": {"success": "Test Sms sent successfully! {count} message(s) processed.", "error": "Failed to send test Sms. Please check your configuration.", "unknownError": "Unknown error occurred"}, "notes": {"title": "Important Notes", "testMode": "The system is currently configured to use a mock Sms provider for testing. No actual Sms messages will be sent to real phone numbers.", "beforeLive": {"title": "Before going live:", "items": ["Configure NetGSM credentials in environment variables", "Switch from MockSmsProvider to NetGsmProvider in Sms service", "Test with a real phone number you control", "Add sufficient Sms credits to your balance", "Configure your Sms templates and reminder schedules"]}, "features": {"title": "Sms Features Available:", "items": ["Payment reminder Sms (pending and overdue)", "Team announcement messages", "Custom messages to selected athletes", "Automatic reminder scheduling", "Template-based messaging with variables", "Sms balance management and tracking"]}}, "mockProvider": "This system is currently using a mock Sms provider for testing. No actual Sms will be sent to the phone number. Check the console logs to see the simulated Sms sending process."}, "sending": {"paymentReminder": {"title": "Send Payment Reminder Sms", "descriptionSingle": "Send Sms reminder for 1 selected payment", "descriptionPlural": "Send Sms reminders for {count} selected payments", "selectedPayments": "Selected Payments", "templateType": "Template Type", "pending": "Pending Payment Reminder", "overdue": "Overdue Payment Reminder", "customTemplate": "Use custom template", "combinePayments": "Combine multiple payments per athlete", "combinePaymentsDescription": "When enabled, all payments for each athlete will be combined into a single SMS with total amount. Due date variable will not be available.", "combiningPaymentsNote": "Note: Multiple payments per athlete will be combined into single SMS messages.", "dueDateNotAvailableWhenCombining": "Due date not available when combining payments", "messageTemplate": "Message Template", "customPlaceholder": "Enter your custom Sms template...", "availableVariables": "Available Variables", "preview": "Preview", "hidePreview": "Hide Preview", "warningRecipientSingle": "This will send Sms message to 1 recipient.", "warningRecipientPlural": "This will send Sms messages to {count} recipients.", "warningSmsCountSingle": "Total: {total} Sms credit will be consumed from your balance.", "warningSmsCountPlural": "Each recipient will receive {smsPerRecipient} Sms messages. Total: {total} Sms credits will be consumed from your balance.", "warningLongMessage": "Note: Message length requires multiple Sms parts. Actual count may vary slightly based on recipient names and other variables.", "warningSingle": "This will send Sms message to 1 recipient and will consume 1 Sms credit from your balance.", "warningPlural": "This will send Sms messages to {count} recipients and will consume {count} Sms credits from your balance.", "send": "Send Sms", "successSingle": "Sms reminder sent successfully to 1 recipient", "successPlural": "Sms reminders sent successfully to {count} recipients", "error": "Failed to send Sms reminders"}, "teamMessage": {"title": "Send Team Message", "description": "Send Sms to {teamName} team members", "athletesWithPhoneSingle": "1 athlete with valid phone number", "athletesWithPhonePlural": "{count} athletes with valid phone numbers", "message": "Message", "messagePlaceholder": "Enter your message to the team...", "sendToAll": "Send to all team members", "selectRecipients": "Select Recipients", "selectAll": "Select All", "clear": "Clear", "noAthletes": "No athletes with valid phone numbers", "selected": "Selected:", "noRecipients": "Please select at least one recipient to send the message.", "warningRecipientSingle": "This will send Sms message to 1 recipient.", "warningRecipientPlural": "This will send Sms messages to {count} recipients.", "warningSmsCountSingle": "Total: {total} Sms credit will be consumed from your balance.", "warningSmsCountPlural": "Each recipient will receive {smsPerRecipient} Sms messages. Total: {total} Sms credits will be consumed from your balance.", "warningLongMessage": "Note: Message length requires multiple Sms parts. Actual count may vary slightly based on recipient names and other variables.", "warningSingle": "This will send Sms message to 1 recipient and will consume 1 Sms credit from your balance.", "warningPlural": "This will send Sms messages to {count} recipients and will consume {count} Sms credits from your balance.", "successSingle": "Team message sent successfully to 1 recipient", "successPlural": "Team message sent successfully to {count} recipients", "error": "Failed to send team message"}, "templateSms": {"successSingle": "Template SMS sent successfully to 1 recipient", "successPlural": "Template SMS sent successfully to {count} recipients", "error": "Failed to send template SMS"}, "bulk": {"title": "Bulk Sms Reminders", "description": "Send payment reminder Sms to multiple recipients at once", "pending": "Pending Payments", "overdue": "Overdue Payments", "totalEligible": "Total Eligible", "quickActions": "Quick Actions", "selected": "selected", "clearSelection": "Clear Selection", "selectAll": "Select All", "sendPending": "Send Pending Reminders", "sendOverdue": "Send Overdue Reminders", "sendToAllPending": "Send to All Pending", "sendToAllOverdue": "Send to All Overdue", "selectPayments": "Select Payments for Sms", "noPayments": "No payments available", "noEligible": "No payments eligible for Sms reminders", "statistics": {"pendingCount": "pending payments", "overdueCount": "overdue payments", "eligibleCount": "eligible payments"}, "notes": {"title": "Important Notes", "items": ["Only payments with valid parent phone numbers are eligible", "Each 160 characters in a message will consume 1 SMS credit from your balance", "Messages are sent immediately and cannot be cancelled", "Failed messages will not consume credits"]}}}, "actions": {"sendSmsReminder": "Send Sms Reminder", "sendTeamSms": "Send Team Sms", "showPreview": "Show Preview", "hidePreview": "Hide Preview", "selectAll": "Select All", "clearSelection": "Clear Selection", "addCredits": "Add Credits"}, "templates": {"title": "Template SMS", "description": "Send template messages to selected users", "types": {"general": "General Announcement", "event": "Event Announcement", "meeting": "Meeting Call", "holiday": "Holiday Notification", "custom": "Custom Message", "passwordReset": "Password Reset"}, "passwordReset": {"message": "Your password reset code: {{code}}\n\nDo not share this code with anyone. This code will expire in 60 minutes.\n\n- Sports Club Management"}, "form": {"selectUsers": "Select Recipients", "selectAll": "Select All", "selectNone": "Select None", "selectedCount": "{count} selected", "templateType": "Template Type", "customMessage": "Custom Message", "customPlaceholder": "Enter your custom message...", "useCustom": "Use custom message", "sendTemplate": "Send Template SMS"}}, "steps": {"selectRecipients": "Select Recipients", "customizeMessage": "Customize Message", "reviewAndSend": "Review & Send", "step": "Step", "previous": "Previous", "nextStep": "Next Step", "cancel": "Cancel", "confirmation": "Confirm SMS Sending", "confirmMessage": "Are you sure you want to send SMS reminders? This action cannot be undone.", "recipients": "Recipients", "smsCredits": "SMS Credits", "yesSend": "Yes, Send SMS"}, "colorful": {"clickToSendReminders": "Click to send reminders", "clickForTemplateSms": "Click for template SMS", "noEligibleUsers": "No eligible users", "noOverduePayments": "No overdue payments", "noPendingPayments": "No pending payments", "totalWithPhoneNumbers": "Total with phone numbers", "templateType": "Template Type", "customMessage": "Custom Message", "defaultTemplate": "<PERSON><PERSON><PERSON>", "hidePreview": "Hide Preview", "showPreview": "Show Preview", "totalSmsCredits": "Total SMS credits", "smsPerRecipient": "SMS per recipient", "messagePreview": "Message Preview", "sendImmediately": "This action will send SMS messages immediately and cannot be undone.", "reviewCarefully": "Please review all details carefully before proceeding.", "paymentReminderType": "Payment Reminder Type", "pendingPaymentReminder": "Pending Payment Reminder", "overduePaymentReminder": "Overdue Payment Reminder", "basedOnSelectedPayments": "Based on selected payments"}, "status": {"pending": "Pending", "sent": "<PERSON><PERSON>", "failed": "Failed", "cancelled": "Cancelled", "overdue": "Overdue", "paid": "Paid", "partially_paid": "Partially Paid"}, "common": {"cancel": "Cancel", "save": "Save", "send": "Send", "loading": "Loading...", "error": "Error", "success": "Success", "credits": "credits", "days": "days", "messageTemplate": "Message Template", "customTemplate": "Custom Template", "defaultTemplate": "<PERSON><PERSON><PERSON>", "characters": "characters", "approximately": "(approx.)", "selectAll": "Select All", "selectNone": "Select None", "selected": "selected", "pleaseSelectAtLeastOne": "Please select at least one payment", "pleaseSelectAtLeastOneUser": "Please select at least one user", "templateSmsSentTo": "Template SMS sent to {{count}} recipients", "failedToSendTemplateSms": "Failed to send template SMS", "smsPerRecipient": "SMS per recipient", "totalSmsCredits": "Total SMS credits", "messagePreview": "Message Preview", "preview": "Preview", "creditUsageInfo": "SMS credit usage may vary based on variable lengths (names, school names, amounts, etc.). This is an estimation. Actual amount will be deducted from your balance.", "estimatedCreditUsage": "Estimated Credit Usage", "type": "Type", "status": "Status", "receiver": "Receiver", "message": "Message", "sentAt": "<PERSON><PERSON>", "sender": "Sender", "selectType": "Select type", "selectStatus": "Select status", "selectSender": "Select sender", "fromDate": "From Date", "toDate": "To Date", "selectFromDate": "Select from date", "selectToDate": "Select to date", "sent": "<PERSON><PERSON>", "failed": "Failed", "types": {"paymentReminder": "Payment Reminder", "teamMessage": "Team", "custom": "Custom"}}}