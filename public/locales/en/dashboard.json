{"dashboard": {"cards": {"schools": "Manage your schools and branches", "instructors": "Manage your instructors", "teams": "Manage your teams and groups", "athletes": "Manage your athletes", "payments": "Track payments and fees", "expenses": "Track all expenses", "facilities": "Manage your facilities", "sms": "Manage SMS notifications"}, "customize": {"button": "Customize", "title": "Customize Dashboard Cards", "description": "Select exactly 4 cards to display on your dashboard. Click cards to select/deselect them.", "selected": "Selected: {{count}}/4", "reset": "Reset to De<PERSON>ult", "position": "Position {{position}}"}, "stats": {"totalRevenue": "Total Revenue", "totalExpenses": "Total Expenses", "netIncome": "Net Income", "overduePayments": "Overdue Payments"}, "financialOverview": {"title": "Financial Overview", "subtitle": "Your financial summary", "totalIncome": "Total Income", "totalExpenses": "Total Expenses", "netBalance": "Net Balance", "income": "Income", "expenses": "Expenses", "noData": "No Financial Data", "noDataSubtitle": "Start recording payments and expenses to see your financial overview."}, "expenseBreakdown": {"title": "Expense Breakdown", "subtitle": "Expenses by category", "noData": "No Expenses Recorded", "noDataSubtitle": "Add expenses to see the breakdown by category."}, "incomeSources": {"title": "Income Sources", "subtitle": "Income by category", "amount": "Amount", "category": "Category", "noData": "No Income Recorded", "noDataSubtitle": "Record payments to see income sources.", "categories": {"equipmentSales": "Equipment Sales", "registrationFees": "Registration Fees", "privateLessons": "Private Lessons", "tournaments": "Tournaments", "otherFees": "Other Fees"}}, "overduePayments": {"title": "Overdue Payments", "subtitle": "Athletes with outstanding balances", "noOverdue": "No overdue payments", "allCaughtUp": "All athletes are caught up with their payments!", "overdueMore": "more", "overduePayment": "overdue payment", "overduePaymentsPlural": "overdue payments"}}}