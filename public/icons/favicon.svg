<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" xml:space="preserve" viewBox="174.92 178 153.94 162.82">
<desc>Created with Fabric.js 3.6.3</desc>
<defs>
</defs>
<g transform="matrix(-1.0350882734058287 -0.0034324952817825173 0.003504353773955857 -1.0567576062052093 251.76124969281662 259.4793682527208)">
<g style="">
		<g transform="matrix(1 0 0 1 0.04999999999999716 2.2900000000000063)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(62,70,76); fill-rule: evenodd; opacity: 1;" transform=" translate(-104.185, -103.59)" d="M 166.75 75.84 c 0 -0.01 0 -0.02 0 -0.04 c 0 -0.05 0 -0.1 0 -0.15 h 0 c -0.1 -3.89 -3.28 -7.01 -7.19 -7.01 c -3.91 0 -7.09 3.12 -7.19 7.01 h 0 c 0 0.07 0 0.15 0 0.22 c 0 26.66 -21.58 48.27 -48.19 48.27 C 77.57 124.15 56 102.54 56 75.88 c 0 -0.07 0 -0.15 0 -0.22 h 0 c -0.1 -3.89 -3.28 -7.01 -7.19 -7.01 s -7.09 3.12 -7.19 7.01 h 0 c 0 0.05 0 0.1 0 0.15 c 0 0.01 0 0.02 0 0.04 c 0 0 0 0.01 0 0.01 c 0 0.01 0 0.02 0 0.02 c 0 34.61 28.01 62.66 62.56 62.66 c 34.55 0 62.57 -28.06 62.57 -62.66 c 0 -0.01 0 -0.02 0 -0.02 C 166.75 75.85 166.75 75.85 166.75 75.84 z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 0.05000000000000071 -60.19)">
<ellipse style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(224,80,98); fill-rule: evenodd; opacity: 1;" cx="0" cy="0" rx="16.7" ry="16.72"/>
</g>
		<g transform="matrix(1 0 0 1 13.719553718843052 17.954387777154636)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(62,70,76); fill-rule: evenodd; opacity: 1;" transform=" translate(-117.85455371884305, -119.25438777715463)" d="M 163.17 68.83 L 163.17 68.83 c -0.04 -0.03 -0.09 -0.05 -0.13 -0.08 c -0.01 -0.01 -0.02 -0.01 -0.03 -0.02 c 0 0 -0.01 0 -0.01 -0.01 c -0.01 0 -0.01 -0.01 -0.02 -0.01 c -29.93 -17.3 -68.2 -7.04 -85.48 22.92 c -17.28 29.96 -7.02 68.28 22.91 85.59 c 0.01 0 0.01 0.01 0.02 0.01 c 0 0 0.01 0 0.01 0.01 c 0.01 0.01 0.02 0.01 0.03 0.02 c 0.04 0.02 0.09 0.05 0.13 0.07 l 0 0 c 3.41 1.86 7.7 0.66 9.66 -2.72 c 1.95 -3.39 0.84 -7.71 -2.47 -9.74 l 0 0 c -0.06 -0.04 -0.13 -0.07 -0.19 -0.11 c -23.05 -13.33 -30.96 -42.84 -17.65 -65.92 c 13.31 -23.08 42.78 -30.99 65.84 -17.66 c 0.06 0.04 0.13 0.08 0.19 0.11 l 0 0 c 3.41 1.86 7.7 0.66 9.66 -2.72 C 167.59 75.18 166.48 70.86 163.17 68.83 z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 57.622778040908386 39.05500000000003)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(68,171,182); fill-rule: evenodd; opacity: 1;" transform=" translate(-161.7577780409084, -140.35500000000002)" d="M 170.1 125.89 c -7.98 -4.61 -18.19 -1.88 -22.79 6.11 c -4.61 7.99 -1.87 18.21 6.11 22.82 c 7.98 4.61 18.19 1.88 22.79 -6.11 C 180.81 140.72 178.08 130.5 170.1 125.89 z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -13.69289352552748 17.935426308061366)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(62,70,76); fill-rule: evenodd; opacity: 1;" transform=" translate(-90.44210647447252, -119.23542630806136)" d="M 130.81 91.61 c -17.28 -29.96 -55.56 -40.22 -85.5 -22.92 c -0.01 0 -0.01 0.01 -0.02 0.01 c 0 0 -0.01 0 -0.01 0.01 c -0.01 0.01 -0.02 0.01 -0.03 0.02 c -0.04 0.03 -0.09 0.05 -0.13 0.08 l 0 0 c -3.32 2.03 -4.43 6.35 -2.47 9.74 c 1.95 3.39 6.25 4.58 9.66 2.72 l 0 0 c 0.06 -0.04 0.13 -0.08 0.19 -0.11 c 23.06 -13.33 52.55 -5.42 65.86 17.66 c 13.31 23.08 5.41 52.59 -17.65 65.92 c -0.06 0.04 -0.13 0.07 -0.19 0.11 l 0 0 c -3.32 2.03 -4.43 6.35 -2.47 9.74 c 1.95 3.39 6.25 4.58 9.66 2.72 l 0 0 c 0.04 -0.02 0.09 -0.05 0.13 -0.07 c 0.01 -0.01 0.02 -0.01 0.03 -0.02 c 0 0 0.01 0 0.01 -0.01 c 0.01 0 0.01 -0.01 0.02 -0.01 C 137.83 159.89 148.09 121.57 130.81 91.61 z" stroke-linecap="round"/>
</g>
		<g transform="matrix(1 0 0 1 -57.610000000000014 39.015000000000036)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(253,196,70); fill-rule: evenodd; opacity: 1;" transform=" translate(-46.52499999999999, -140.31500000000003)" d="M 60.98 131.96 c -4.61 -7.99 -14.82 -10.73 -22.8 -6.11 c -7.98 4.61 -10.72 14.83 -6.11 22.82 c 4.61 7.99 14.82 10.73 22.8 6.11 C 62.85 150.17 65.59 139.95 60.98 131.96 z" stroke-linecap="round"/>
</g>
</g>
</g>
</svg>