CREATE TABLE "password_reset_sms_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"username" varchar(255) NOT NULL,
	"user_id" varchar(255) NOT NULL,
	"receiver" varchar(20) NOT NULL,
	"message" text NOT NULL,
	"verification_code" varchar(20) NOT NULL,
	"status" varchar(20) DEFAULT 'pending' NOT NULL,
	"sent_at" timestamp,
	"credits_used" integer DEFAULT 1 NOT NULL,
	"provider_response" text,
	"expires_at" timestamp NOT NULL,
	"used_at" timestamp,
	"error_message" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
