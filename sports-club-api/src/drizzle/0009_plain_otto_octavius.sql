CREATE TABLE "sms_log_payments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"sms_log_id" uuid NOT NULL,
	"payment_id" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "sms_log_payments_sms_log_id_payment_id_unique" UNIQUE("sms_log_id","payment_id")
);
--> statement-breakpoint
ALTER TABLE "sms_logs" DROP CONSTRAINT "sms_logs_payment_id_payments_id_fk";
--> statement-breakpoint
ALTER TABLE "sms_log_payments" ADD CONSTRAINT "sms_log_payments_sms_log_id_sms_logs_id_fk" FOREIGN KEY ("sms_log_id") REFERENCES "public"."sms_logs"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sms_log_payments" ADD CONSTRAINT "sms_log_payments_payment_id_payments_id_fk" FOREIGN KEY ("payment_id") REFERENCES "public"."payments"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sms_logs" DROP COLUMN "payment_id";