CREATE TYPE "public"."transaction_method" AS ENUM('cash', 'bank_transfer', 'credit_card', 'existing_balance');--> statement-breakpoint
ALTER TYPE "public"."payment_status" ADD VALUE 'partially_paid';--> statement-breakpoint
CREATE TABLE "payment_transactions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" varchar(255) NOT NULL,
	"athlete_id" uuid NOT NULL,
	"payment_id" uuid,
	"amount" numeric(10, 2) NOT NULL,
	"transaction_method" "transaction_method" NOT NULL,
	"transaction_date" timestamp DEFAULT now() NOT NULL,
	"notes" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" bigint NOT NULL,
	"updated_by" bigint NOT NULL
);
--> statement-breakpoint
ALTER TABLE "payments" ADD COLUMN "amount_paid" numeric(10, 2) DEFAULT '0' NOT NULL;--> statement-breakpoint
ALTER TABLE "payment_transactions" ADD CONSTRAINT "payment_transactions_athlete_id_athletes_id_fk" FOREIGN KEY ("athlete_id") REFERENCES "public"."athletes"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "payment_transactions" ADD CONSTRAINT "payment_transactions_payment_id_payments_id_fk" FOREIGN KEY ("payment_id") REFERENCES "public"."payments"("id") ON DELETE cascade ON UPDATE no action;