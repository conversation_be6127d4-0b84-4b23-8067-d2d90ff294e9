import { BaseService } from './base';
import { ServiceResult } from '../errors/types';
import { calculateProratedAmountForBillingCycle } from '../proration-utils';
import { getPaymentTranslation } from '../utils/server-translation';
import logger from '@/lib/logger';
import { paymentTransactionService } from './payment-transaction.service';

// Import actions instead of direct DB access
import { getAthleteTeams } from '../actions/athlete-teams';
import { getAthleteAssignedPlans } from '../actions/payment-plan-assignments';
import { getPaymentPlans } from '../actions/payment-plans';
import { getPaymentsPaginated } from '../actions/payments';

export interface PaymentAdjustment {
  paymentId: string;
  paymentPlanName: string;
  originalAmount: number;
  proratedAdjustment: number;
  finalAmount: number;
  creditToBalance: number;
  status: 'pending' | 'completed' | 'overdue' | 'partially_paid';
  action: 'reduce_payment' | 'create_balance_transaction' | 'adjust_partial_overpaid' | 'adjust_partial_underpaid' | 'cancel_payment';
  description: string;
}

export interface DeactivationSummary {
  athleteId: string;
  paymentAdjustments: PaymentAdjustment[];
  totalCreditToBalance: number;
  teamsToRemove: { teamId: string; teamName: string }[];
  paymentPlansToDeactivate: { planId: string; planName: string }[];
}

export class AthleteDeactivationService extends BaseService {
  constructor() {
    super('AthleteDeactivationService');
  }

  /**
   * Calculate what will happen during deactivation without executing it
   */
  async calculateDeactivationAdjustments(
    athleteId: string,
    locale: string = 'en'
  ): Promise<ServiceResult<DeactivationSummary>> {
    return this.executeDatabaseOperation(
      'calculateDeactivationAdjustments',
      async () => {
        // Get athlete's current team assignments
        const athleteTeams = await getAthleteTeams(athleteId);
        const teamsToRemove = athleteTeams
          .filter((team: any) => !team.leftAt) // Only active team memberships
          .map((team: any) => ({
            teamId: team.teamId,
            teamName: team.teamName || team.teamId
          }));

        // Get athlete's active payment plans
        const paymentPlans = await getAthleteAssignedPlans(athleteId);
        const activePlans = paymentPlans.filter((plan: any) => plan.isActive);
        const paymentPlansToDeactivate = activePlans.map((plan: any) => ({
          planId: plan.id,
          planName: plan.planName || plan.id
        }));

        // Get all payment plans details for reference
        const allPaymentPlans = await getPaymentPlans();

        // Calculate payment adjustments for each active plan
        const paymentAdjustments: PaymentAdjustment[] = [];
        let totalCreditToBalance = 0;

        for (const plan of activePlans) {
          try {
            // Get payment plan details
            const paymentPlanDetails = allPaymentPlans.find(p => p.id === plan.planId);
            if (!paymentPlanDetails) continue;

            // Get current period payments for this plan
            const currentPeriodPayments = await this.getCurrentPeriodPayments(
              athleteId, 
              plan.id
            );

            for (const payment of currentPeriodPayments) {
              const adjustment = await this.calculatePaymentAdjustment(
                payment,
                paymentPlanDetails,
                locale
              );
              
              if (adjustment) {
                paymentAdjustments.push(adjustment);
                
                // For cancelled payments, the debt reduction is equivalent to credit
                if (adjustment.action === 'cancel_payment') {
                  totalCreditToBalance += adjustment.originalAmount;
                } else {
                  totalCreditToBalance += adjustment.creditToBalance;
                }
              }
            }
          } catch (planError) {
            logger.error(`Error processing plan ${plan.id} for deactivation calculation:`, { error: planError });
            // Continue with other plans
          }
        }

        return {
          athleteId,
          paymentAdjustments,
          totalCreditToBalance,
          teamsToRemove,
          paymentPlansToDeactivate,
        };
      }
    );
  }

  /**
   * Get payments for current billing period only
   */
  private async getCurrentPeriodPayments(
    athleteId: string,
    athletePaymentPlanId: string
  ) {
    // Get all payments and filter by athlete payment plan
    const allPayments = await getPaymentsPaginated(1, 1000, '', 'date', 'desc', {});
    const planPayments = allPayments.data.filter((payment: any) => 
      payment.athletePaymentPlanId === athletePaymentPlanId
    );
    
    // Filter to current billing period (simplified - within last 30 days)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    return planPayments.filter((payment: any) => {
      const paymentDate = new Date(payment.date);
      return paymentDate >= thirtyDaysAgo && paymentDate <= today;
    });
  }

  /**
   * Calculate adjustment for a single payment
   */
  private async calculatePaymentAdjustment(
    payment: any,
    paymentPlan: any,
    locale: string
  ): Promise<PaymentAdjustment | null> {
    try {
      const originalAmount = parseFloat(payment.amount);
      const monthlyAmount = parseFloat(paymentPlan.monthlyValue);
      
      // Check if this is a same-day payment (created today)
      const paymentDate = new Date(payment.date);
      const today = new Date();
      const isSameDayPayment = paymentDate.toDateString() === today.toDateString();
      
      // For same-day payments that are pending/overdue, cancel them entirely
      if (isSameDayPayment && (payment.status === 'pending' || payment.status === 'overdue')) {
        return {
          paymentId: payment.id,
          paymentPlanName: paymentPlan.name,
          originalAmount,
          proratedAdjustment: originalAmount, // Full cancellation
          finalAmount: 0, // Payment will be cancelled
          creditToBalance: 0, // No credit needed since payment is cancelled
          status: payment.status,
          action: 'cancel_payment',
          description: getPaymentTranslation(
            'descriptions.deactivationSameDayCancel',
            locale,
            { planName: paymentPlan.name }
          )
        };
      }
      
      // For non-same-day payments, calculate prorated adjustment using existing logic
      const proratedAmount = calculateProratedAmountForBillingCycle(monthlyAmount, paymentPlan.assignDay);
      const proratedAdjustment = originalAmount - proratedAmount;
      
      // Skip if no adjustment needed
      if (proratedAdjustment <= 0) {
        return null;
      }
      
      // Handle different payment statuses
      switch (payment.status) {
        case 'pending':
        case 'overdue':
          return {
            paymentId: payment.id,
            paymentPlanName: paymentPlan.name,
            originalAmount,
            proratedAdjustment,
            finalAmount: proratedAmount,
            creditToBalance: 0,
            status: payment.status,
            action: 'reduce_payment',
            description: getPaymentTranslation(
              'descriptions.deactivationProrated',
              locale,
              { planName: paymentPlan.name, date: new Date().toLocaleDateString() }
            )
          };

        case 'completed':
          return {
            paymentId: payment.id,
            paymentPlanName: paymentPlan.name,
            originalAmount,
            proratedAdjustment,
            finalAmount: originalAmount, // Keep original payment as is
            creditToBalance: proratedAdjustment,
            status: payment.status,
            action: 'create_balance_transaction',
            description: getPaymentTranslation(
              'descriptions.deactivationCredit',
              locale,
              { planName: paymentPlan.name, date: new Date().toLocaleDateString() }
            )
          };

        case 'partially_paid':
          const amountPaid = parseFloat(payment.amountPaid || '0');
          if (amountPaid > proratedAmount) {
            // Overpaid - add excess as credit
            const overpayment = amountPaid - proratedAmount;
            return {
              paymentId: payment.id,
              paymentPlanName: paymentPlan.name,
              originalAmount,
              proratedAdjustment,
              finalAmount: proratedAmount,
              creditToBalance: overpayment,
              status: payment.status,
              action: 'adjust_partial_overpaid',
              description: getPaymentTranslation(
                'descriptions.deactivationPartialOverpaid',
                locale,
                { planName: paymentPlan.name, date: new Date().toLocaleDateString() }
              )
            };
          } else {
            // Underpaid or exact - just reduce payment amount
            return {
              paymentId: payment.id,
              paymentPlanName: paymentPlan.name,
              originalAmount,
              proratedAdjustment,
              finalAmount: proratedAmount,
              creditToBalance: 0,
              status: payment.status,
              action: 'adjust_partial_underpaid',
              description: getPaymentTranslation(
                'descriptions.deactivationPartialUnderpaid',
                locale,
                { planName: paymentPlan.name, date: new Date().toLocaleDateString() }
              )
            };
          }

        default:
          return null;
      }
    } catch (error) {
      logger.error('Error calculating payment adjustment:', { error, paymentId: payment.id });
      return null;
    }
  }
}
