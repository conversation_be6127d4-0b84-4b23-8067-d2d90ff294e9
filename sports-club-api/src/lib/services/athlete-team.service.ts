import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { NotFoundError, BusinessRuleError } from '../errors/errors';
import { TenantAwareDB } from '../db';
import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import { AssignPaymentPlanParams, assignPaymentPlanToAthlete, getAthletePaymentPlans, deactivatePaymentPlanAssignment } from '../payment-plan-utils';
import { getPaymentPlans } from '../actions/payment-plans';
import { athleteService } from './index';
import { createPayment }  from '../actions/payments';
import { calculateProratedAmount, calculateProratedAmountForBillingCycle } from '../proration-utils';
import { getPaymentTranslation } from '../utils/server-translation';

export interface AddAthleteToTeamParams {
  athleteId: string;
  teamId: string;
  paymentPlanId?: string;
  assignPaymentPlan?: boolean;
  useProrated?: boolean;
  customProratedAmount?: string;
  locale?: string;
}

export interface RemoveAthleteFromTeamParams {
  athleteId: string;
  teamId: string;
  removePaymentPlans?: boolean;
}

export class AthleteTeamService extends BaseService {
  constructor() {
    super('AthleteTeamService');
  }

  /**
   * Common function to validate athlete and team existence and retrieve context
   * @private
   */
  private async validateAthleteTeamOperation(
    athleteId: string,
    teamId: string,
    userId?: string,
    tenantId?: string,
    checkTeamMembership?: boolean
  ) {
    const effectiveTenantId = tenantId || await getServerTenantId();
    const effectiveUserId = userId || await getServerUserId();

    if (!effectiveTenantId) {
      throw new Error('Missing tenant context');
    }

    if (!effectiveUserId) {
      throw new Error('Missing user context');
    }

    // Check if athlete exists
    const athlete = await TenantAwareDB.getAthleteById(athleteId, effectiveTenantId);
    if (!athlete) {
      throw new NotFoundError('Athlete', athleteId);
    }

    // Check if team exists
    const team = await TenantAwareDB.getTeamById(teamId, effectiveTenantId);
    if (!team) {
      throw new NotFoundError('Team', teamId);
    }

    // Check team membership
    const athleteResult = await athleteService().getAthleteById(athleteId, effectiveUserId.toString(), effectiveTenantId);
    if (!athleteResult.success || !athleteResult.data) {
      throw new NotFoundError('Athlete', athleteId);
    }
    const athleteTeams = athleteResult.data.teamDetails || [];

    // Check if athlete is already in team (for adding) or not in team (for removing)
    const isInTeam = athleteTeams.some((t: any) => t.teamId === teamId);

    if (checkTeamMembership !== undefined) {
      if (checkTeamMembership && !isInTeam) {
        throw new BusinessRuleError(
          'athlete_not_in_team',
          'Athlete is not a member of this team',
          undefined,
          'This athlete is not assigned to the selected team.'
        );
      } else if (!checkTeamMembership && isInTeam) {
        throw new BusinessRuleError(
          'athlete_already_in_team',
          'Athlete is already a member of this team',
          undefined,
          'This athlete is already assigned to the selected team.'
        );
      }
    }

    return {
      effectiveTenantId,
      effectiveUserId: effectiveUserId.toString(),
      athlete,
      team,
      athleteTeams,
      isInTeam
    };
  }

  /**
   * Basic operation to add an athlete to a team
   */
  async addAthleteToTeam(
    athleteId: string,
    teamId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'addAthleteToTeam',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        if (!effectiveTenantId) {
          throw new Error('Missing tenant context');
        }

        return await TenantAwareDB.addAthleteToTeam(athleteId, teamId);
      },
      { userId, tenantId }
    );
  }

  /**
   * Basic operation to remove an athlete from a team
   */
  async removeAthleteFromTeam(
    athleteId: string,
    teamId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'removeAthleteFromTeam',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        if (!effectiveTenantId) {
          throw new Error('Missing tenant context');
        }

        return await TenantAwareDB.removeAthleteFromTeam(athleteId, teamId);
      },
      { userId, tenantId }
    );
  }

  /**
   * Add an athlete to a team with optional payment plan assignment and prorated balance calculation
   */
  async addAthleteToTeamWithPaymentPlan(
    params: AddAthleteToTeamParams,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const { 
      athleteId, 
      teamId
    } = params;

    const validationFunctions = [
      (data: AddAthleteToTeamParams): ValidationError | null => {
        if (!data.athleteId || data.athleteId.trim().length === 0) {
          return { field: 'athleteId', message: 'Athlete ID is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        return null;
      },
      (data: AddAthleteToTeamParams): ValidationError | null => {
        if (!data.teamId || data.teamId.trim().length === 0) {
          return { field: 'teamId', message: 'Team ID is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        return null;
      },
      (data: AddAthleteToTeamParams): ValidationError | null => {
        if (data.assignPaymentPlan && (!data.paymentPlanId || data.paymentPlanId.trim().length === 0)) {
          return { field: 'paymentPlanId', message: 'Payment plan ID is required when assignPaymentPlan is true', code: 'REQUIRED_FIELD_MISSING' };
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'addAthleteToTeamWithPaymentPlan',
      params,
      validationFunctions,
      async (validatedData) => {
        // Validate athlete and team, and check that athlete is not already in team
        const { effectiveTenantId, effectiveUserId } = await this.validateAthleteTeamOperation(
          validatedData.athleteId,
          validatedData.teamId,
          userId,
          tenantId,
          false // false = check that athlete is NOT already in team
        );

        // Add athlete to team
        const addResult = await this.addAthleteToTeam(validatedData.athleteId, validatedData.teamId, effectiveUserId, effectiveTenantId);
        if (!addResult.success) {
          throw new Error(addResult.error?.message || 'Failed to add athlete to team');
        }

        let paymentPlanAssignmentId: string | null = null;

        // If payment plan should be assigned and plan ID is provided
        if (validatedData.assignPaymentPlan && validatedData.paymentPlanId) {
          // Check if payment plan exists
          const paymentPlan = await TenantAwareDB.getPaymentPlanById(validatedData.paymentPlanId, effectiveTenantId);
          if (!paymentPlan) {
            throw new NotFoundError('PaymentPlan', validatedData.paymentPlanId);
          }

          // Assign payment plan
          const assignParams: AssignPaymentPlanParams = {
            athleteId: validatedData.athleteId,
            planId: validatedData.paymentPlanId,
            teamId: validatedData.teamId,
            isActive: true,
            tenantId: effectiveTenantId,
            userId: BigInt(effectiveUserId)
          };

          const assignmentResult = await assignPaymentPlanToAthlete(assignParams);
          paymentPlanAssignmentId = assignmentResult.id;

          // Create prorated payment if requested and payment plan was assigned
          if (validatedData.useProrated && paymentPlan) {
            await this.createProratedPaymentForTeamJoin({
              athleteId: validatedData.athleteId,
              paymentPlan,
              assignmentId: paymentPlanAssignmentId,
              customProratedAmount: validatedData.customProratedAmount,
              locale: validatedData.locale || 'en'
            }, effectiveUserId, effectiveTenantId);
          }
        }

        return { success: true };
      },
      {
        userId,
        tenantId,
        resource: 'athleteTeam',
        metadata: { athleteId, teamId, operation: 'addWithPaymentPlan' }
      }
    );
  }

  /**
   * Remove an athlete from a team with optional payment plan cleanup
   */
  async removeAthleteFromTeamWithCleanup(
    params: RemoveAthleteFromTeamParams,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    const { athleteId, teamId, removePaymentPlans = true } = params;

    const validationFunctions = [
      (data: RemoveAthleteFromTeamParams): ValidationError | null => {
        if (!data.athleteId || data.athleteId.trim().length === 0) {
          return { field: 'athleteId', message: 'Athlete ID is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        return null;
      },
      (data: RemoveAthleteFromTeamParams): ValidationError | null => {
        if (!data.teamId || data.teamId.trim().length === 0) {
          return { field: 'teamId', message: 'Team ID is required', code: 'REQUIRED_FIELD_MISSING' };
        }
        return null;
      },
    ];

    return this.executeWithValidation(
      'removeAthleteFromTeamWithCleanup',
      params,
      validationFunctions,
      async (validatedData) => {
        // Validate athlete and team, and check that athlete is in team
        const { effectiveTenantId, effectiveUserId } = await this.validateAthleteTeamOperation(
          validatedData.athleteId,
          validatedData.teamId,
          userId,
          tenantId,
          true // true = check that athlete IS in team
        );

        // If removing payment plans, find and deactivate them first
        if (validatedData.removePaymentPlans) {
          const athletePaymentPlans = await getAthletePaymentPlans(validatedData.athleteId, effectiveTenantId);
          
          // Find active payment plans for this specific team
          const teamPaymentPlans = athletePaymentPlans.filter(
            (plan: any) => plan.teamId === validatedData.teamId && plan.isActive
          );

          // Deactivate each payment plan for this team
          for (const plan of teamPaymentPlans) {
            await deactivatePaymentPlanAssignment(plan.id, effectiveTenantId, BigInt(effectiveUserId));
          }
        }

        // Remove athlete from team
        const removeResult = await this.removeAthleteFromTeam(validatedData.athleteId, validatedData.teamId, effectiveUserId, effectiveTenantId);
        if (!removeResult.success) {
          throw new Error(removeResult.error?.message || 'Failed to remove athlete from team');
        }

        return { success: true };
      },
      {
        userId,
        tenantId,
        resource: 'athleteTeam',
        metadata: { athleteId, teamId, operation: 'removeWithCleanup' }
      }
    );
  }

  /**
   * Get available payment plans for a team (filtered by team's branch)
   */
  async getAvailablePaymentPlansForTeam(
    teamBranchId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getAvailablePaymentPlansForTeam',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        if (!effectiveTenantId) {
          throw new Error('Missing tenant context');
        }

        const allPaymentPlans = await getPaymentPlans();
        
        // Filter payment plans that are active and either:
        // 1. Have no branch restrictions (branches array is empty)
        // 2. Include the team's branch in their allowed branches
        const availablePlans = (allPaymentPlans || []).filter((plan: any) => 
          plan.status === 'active' && 
          (
            !plan.branches?.length || 
            plan.branches.some((branch: any) => branch.id === teamBranchId)
          )
        );

        return availablePlans;
      },
      {
        userId,
        tenantId,
        resource: 'paymentPlans',
        metadata: { teamBranchId }
      }
    );
  }

  /**
   * Check if athlete is already in team
   */
  async checkAthleteInTeam(
    athleteId: string, 
    teamId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'checkAthleteInTeam',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        if (!effectiveTenantId) {
          throw new Error('Missing tenant context');
        }

        const athleteResult = await athleteService().getAthleteById(athleteId, userId?.toString(), effectiveTenantId);
        if (!athleteResult.success || !athleteResult.data) {
          return false;
        }
        const athleteTeams = athleteResult.data.teamDetails || [];
        return athleteTeams.some((team: any) => team.teamId === teamId);
      },
      {
        userId,
        tenantId,
        resource: 'athleteTeam',
        metadata: { athleteId, teamId, operation: 'checkMembership' }
      }
    );
  }

  /**
   * Get athlete's active payment plans for a specific team
   */
  async getAthleteTeamPaymentPlans(
    athleteId: string, 
    teamId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getAthleteTeamPaymentPlans',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        if (!effectiveTenantId) {
          throw new Error('Missing tenant context');
        }

        const athletePaymentPlans = await getAthletePaymentPlans(athleteId, effectiveTenantId);
        return athletePaymentPlans.filter(
          (plan: any) => plan.teamId === teamId && plan.isActive
        );
      },
      {
        userId,
        tenantId,
        resource: 'paymentPlans',
        metadata: { athleteId, teamId }
      }
    );
  }

  /**
   * Create prorated payment when athlete joins a team with a payment plan
   * Private method to handle prorated balance calculation for team join
   */
  private async createProratedPaymentForTeamJoin(
    data: {
      athleteId: string;
      paymentPlan: any;
      assignmentId: string;
      customProratedAmount?: string;
      locale: string;
    },
    userId?: string,
    tenantId?: string
  ): Promise<void> {
    const { getPaymentDueDate } = await import('../utils/date-utils');

    // Use custom amount if provided, otherwise calculate automatically
    let proratedAmount: number;
    if (data.customProratedAmount && data.customProratedAmount.trim() !== '') {
      proratedAmount = parseFloat(data.customProratedAmount);
    } else {
      const monthlyAmount = parseFloat(data.paymentPlan.monthlyValue);
      // Use the new billing cycle-based proration calculation
      proratedAmount = calculateProratedAmountForBillingCycle(
        monthlyAmount, 
        data.paymentPlan.assignDay
      );
    }
    
    if (proratedAmount > 0) {
      const billingDate = new Date();
      await createPayment({
        athleteId: data.athleteId,
        athletePaymentPlanId: data.assignmentId,
        amount: proratedAmount.toFixed(2),
        date: billingDate.toISOString().split('T')[0],
        dueDate: getPaymentDueDate(billingDate),
        status: 'pending',
        type: 'fee',
        description: getPaymentTranslation(
          'descriptions.proratedBalance',
          data.locale,
          { planName: data.paymentPlan.name }
        ),
      });
    }
  }
}

// Factory function
export const athleteTeamService = () => new AthleteTeamService();
