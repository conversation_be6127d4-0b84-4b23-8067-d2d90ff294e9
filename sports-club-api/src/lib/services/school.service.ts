import { BaseService } from './base';
import { ServiceResult } from '../errors/types';
import { NotFoundError, BusinessRuleError } from '../errors/errors';
import { TenantAwareDB } from '../db';
import { getServerTenantId } from '../tenant-utils-server';

export interface CreateSchoolData {
  name: string;
  foundedYear: number;
  logo?: string;
  address?: string;
  phone?: string;
  email?: string;
}

export interface UpdateSchoolData {
  name?: string;
  foundedYear?: number;
  logo?: string;
  address?: string;
  phone?: string;
  email?: string;
}

export class SchoolService extends BaseService {
  constructor() {
    super('SchoolService');
  }

  /**
   * Get all schools
   */
  async getSchools(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getSchools',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getSchools(effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'schools',
      }
    );
  }

  /**
   * Get school by ID
   */
  async getSchoolById(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
      'getSchoolById',
      'School',
      id,
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getSchoolById(id, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'school',
      }
    );
  }

  /**
   * Delete a school
   */
  async deleteSchool(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.deleteWithValidation(
      'deleteSchool',
      id,
      async (id, tenantId) => {
        // Check for dependent teams
        const teams = await TenantAwareDB.getTeamsBySchoolId(id, tenantId);
        if (teams && teams.length > 0) {
          throw new BusinessRuleError('has_teams', 'Cannot delete school with existing teams');
        }

        await TenantAwareDB.deleteSchool(id, tenantId);
        return true;
      },
      (id, tenantId) => TenantAwareDB.getSchoolById(id, tenantId),
      'School',
      userId,
      tenantId
    );
  }
  
  /**
   * Update a school with branches
   */
  async updateSchoolWithBranches(
    id: string,
    data: UpdateSchoolData & { branches?: string[] },
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'updateSchoolWithBranches',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Update the school
        const schoolData = {
          name: data.name,
          address: data.address,
          phone: data.phone,
          email: data.email,
          logo: data.logo,
          foundedYear: data.foundedYear,
        };
        
        const updateResult = await TenantAwareDB.updateSchool(id, schoolData, effectiveTenantId || undefined);
        
        // If branches are provided, update the school-branch associations
        if (data.branches !== undefined) {
          await TenantAwareDB.assignBranchesToSchool(id, data.branches);
        }
        
        return updateResult;
      },
      {
        userId,
        tenantId,
        resource: 'school',
        metadata: { operation: 'updateWithBranches', schoolId: id }
      }
    );
  }

  /**
   * Create a school with branches
   */
  async createSchoolWithBranches(
    data: CreateSchoolData & { branches?: string[] },
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'createSchoolWithBranches',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Create the school
        const schoolData = {
          name: data.name,
          foundedYear: data.foundedYear,
          address: data.address,
          phone: data.phone,
          email: data.email,
          logo: data.logo,
        };
        
        const school = await TenantAwareDB.createSchool(schoolData, effectiveTenantId || undefined);
        
        // If branches are provided, assign them to the school
        if (data.branches && data.branches.length > 0 && school) {
          await TenantAwareDB.assignBranchesToSchool(school.id, data.branches);
        }
        
        return school;
      },
      {
        userId,
        tenantId,
        resource: 'school',
        metadata: { operation: 'createWithBranches' }
      }
    );
  }

  /**
   * Assign branches to a school
   */
  async assignBranchesToSchool(
    schoolId: string,
    branchIds: string[],
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'assignBranchesToSchool',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        
        // Check if school exists
        const existingSchool = await TenantAwareDB.getSchoolById(schoolId, effectiveTenantId || undefined);
        if (!existingSchool) {
          throw new NotFoundError('School not found');
        }
        
        await TenantAwareDB.assignBranchesToSchool(schoolId, branchIds);
        return true;
      },
      {
        userId,
        tenantId,
        resource: 'school',
        metadata: { operation: 'assignBranches', schoolId }
      }
    );
  }
}

// Factory function
export const schoolService = () => new SchoolService();
