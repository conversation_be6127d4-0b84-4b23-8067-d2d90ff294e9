import { BaseService } from './base';
import { ServiceResult } from '../errors/types';
import { TenantAwareDB } from '../db';
import { getServerTenantId, getServerUserId } from '../tenant-utils-server';

export interface CreateSmsLogData {
  type: 'payment_reminder' | 'team_message' | 'custom';
  status: 'pending' | 'sent' | 'failed' | 'cancelled';
  receiver: string;
  message: string;
  senderIdentifier: string;
  creditsUsed?: number;
  providerResponse?: string;
  athleteId?: string;
  paymentId?: string;
  teamId?: string;
  senderType: 'user' | 'system';
  senderId?: string;
}

export interface UpdateSmsLogData {
  status?: 'pending' | 'sent' | 'failed' | 'cancelled';
  creditsUsed?: number;
  providerResponse?: string;
}

export interface SmsLogFilters {
  type?: string;
  status?: string;
  senderType?: string;
  dateFrom?: string;
  dateTo?: string;
}

/**
 * SMS Logging Service
 * Handles SMS log creation, updates, and retrieval
 */
export class SmsLoggingService extends BaseService {
  constructor() {
    super('SmsLoggingService');
  }

  /**
   * Get all SMS logs
   */
  async getSmsLogs(
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getSmsLogs',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getSmsLogs(effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'sms_logs',
      }
    );
  }

  /**
   * Get SMS logs with pagination
   */
  async getSmsLogsPaginated(
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy?: string,
    sortOrder?: 'asc' | 'desc',
    filters?: SmsLogFilters,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'getSmsLogsPaginated',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getSmsLogsPaginated(effectiveTenantId || undefined, {
          page,
          limit,
          search,
          sortBy,
          sortOrder,
          filters,
        });
      },
      {
        userId,
        tenantId,
        resource: 'sms_logs',
        metadata: { page, limit, search, sortBy, sortOrder, filters },
      }
    );
  }

  /**
   * Get SMS log by ID
   */
  async getSmsLogById(
    id: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
      'getSmsLogById',
      'SMS Log',
      id,
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getSmsLogById(id, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'sms_log',
      }
    );
  }

  /**
   * Create SMS log
   */
  async createSmsLog(
    data: CreateSmsLogData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'createSmsLog',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.createSmsLog(data, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'sms_log',
      }
    );
  }

  /**
   * Update SMS log
   */
  async updateSmsLog(
    id: string,
    data: UpdateSmsLogData,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'updateSmsLog',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.updateSmsLog(id, data, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'sms_log',
        metadata: { logId: id },
      }
    );
  }



  /**
   * Get SMS logs by athlete ID
   */
  async getSmsLogsByAthleteId(
    athleteId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getSmsLogsByAthleteId',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getSmsLogsByAthleteId(athleteId, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'sms_logs',
        metadata: { athleteId },
      }
    );
  }

  /**
   * Get SMS logs by team ID
   */
  async getSmsLogsByTeamId(
    teamId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getSmsLogsByTeamId',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getSmsLogsByTeamId(teamId, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'sms_logs',
        metadata: { teamId },
      }
    );
  }

  /**
   * Get SMS statistics
   */
  async getSmsStats(
    dateFrom?: Date,
    dateTo?: Date,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getSmsStats',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.getSmsStats(effectiveTenantId || undefined, dateFrom, dateTo);
      },
      {
        userId,
        tenantId,
        resource: 'sms_stats',
        metadata: { dateFrom, dateTo },
      }
    );
  }

  /**
   * Log SMS sending attempt
   */
  async logSmsSending(
    type: 'payment_reminder' | 'team_message' | 'custom',
    receiver: string,
    message: string,
    senderIdentifier: string,
    senderType: 'user' | 'system',
    senderId?: string,
    athleteId?: string,
    paymentId?: string,
    teamId?: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.createSmsLog(
      {
        type,
        status: 'pending',
        receiver,
        message,
        senderIdentifier,
        senderType,
        senderId,
        athleteId,
        paymentId,
        teamId,
      },
      userId,
      tenantId
    );
  }

  /**
   * Log SMS success
   */
  async logSmsSuccess(
    logId: string,
    creditsUsed?: number,
    providerResponse?: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.updateSmsLog(
      logId,
      {
        status: 'sent',
        creditsUsed,
        providerResponse,
      },
      userId,
      tenantId
    );
  }

  /**
   * Log SMS failure
   */
  async logSmsFailure(
    logId: string,
    providerResponse?: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.updateSmsLog(
      logId,
      {
        status: 'failed',
        providerResponse,
      },
      userId,
      tenantId
    );
  }

  /**
   * Create SMS log payment relation
   */
  async createSmsLogPaymentRelation(
    smsLogId: string,
    paymentId: string,
    userId?: string,
    tenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'createSmsLogPaymentRelation',
      async () => {
        const effectiveTenantId = tenantId || await getServerTenantId();
        return TenantAwareDB.createSmsLogPaymentRelation(smsLogId, paymentId, effectiveTenantId || undefined);
      },
      {
        userId,
        tenantId,
        resource: 'sms_log_payment_relation',
        metadata: { smsLogId, paymentId },
      }
    );
  }
}
