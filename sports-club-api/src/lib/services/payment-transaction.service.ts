import { BaseService } from './base';
import { TenantAwareDB } from '../db';
import { ServiceResult } from '../errors/types';
import { balanceProcessorService } from './balance-processor.service';
import { updateAthleteBalance } from '../balance-calculator';
import logger from '@/lib/logger';

export class PaymentTransactionService extends BaseService {
  constructor() {
    super('PaymentTransactionService');
  }

  async getPaymentTransactions(
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeDatabaseOperation(
      'getPaymentTransactions',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getPaymentTransactions(resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getPaymentTransactionsPaginated(
    page: number = 1,
    limit: number = 10,
    search?: string,
    sortBy: string = 'transactionDate',
    sortOrder: 'asc' | 'desc' = 'desc',
    filters: Record<string, string> = {},
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'getPaymentTransactionsPaginated',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getPaymentTransactionsPaginated(
          page,
          limit,
          search,
          sortBy,
          sortOrder,
          filters,
          resolvedTenantId
        );
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getPaymentTransactionById(
    id: string,
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'getPaymentTransactionById',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getPaymentTransactionById(id, resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getTransactionsByPaymentId(
    paymentId: string,
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeDatabaseOperation(
      'getTransactionsByPaymentId',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getTransactionsByPaymentId(paymentId, resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getTransactionsByAthleteId(
    athleteId: string,
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeDatabaseOperation(
      'getTransactionsByAthleteId',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getTransactionsByAthleteId(athleteId, resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async createPaymentTransaction(
    data: {
      athleteId: string;
      paymentId?: string | null;
      amount: string;
      transactionMethod: 'cash' | 'bank_transfer' | 'credit_card' | 'existing_balance';
      transactionDate?: Date;
      notes?: string | null;
    },
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'createPaymentTransaction',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;

        // Create the transaction
        const transaction = await TenantAwareDB.createPaymentTransaction(data, resolvedTenantId, userIdBigInt);

        // If this is a payment transaction (not a balance top-up), update the payment status
        if (data.paymentId) {
          // Get the payment to calculate new amounts
          const payment = await TenantAwareDB.getPaymentById(data.paymentId, resolvedTenantId);
          if (!payment) {
            throw new Error('Payment not found');
          }

          const totalAmount = parseFloat(payment.amount);
          const currentPaid = parseFloat(payment.amountPaid || '0');
          const transactionAmount = parseFloat(data.amount);
          const newAmountPaid = currentPaid + transactionAmount;

          // Determine new status
          let newStatus: 'pending' | 'partially_paid' | 'completed' | 'overdue' = payment.status as any;
          if (newAmountPaid >= totalAmount) {
            newStatus = 'completed';
          } else if (newAmountPaid > 0) {
            newStatus = 'partially_paid';
          }

          // Update payment with new amount paid and status
          await TenantAwareDB.updatePayment(data.paymentId, {
            amountPaid: newAmountPaid.toFixed(2),
            status: newStatus,
          }, resolvedTenantId, userIdBigInt);

          // Handle overpayment by creating balance credit
          if (newAmountPaid > totalAmount) {
            const overpayment = newAmountPaid - totalAmount;
            await TenantAwareDB.createPaymentTransaction({
              athleteId: data.athleteId,
              paymentId: null, // Balance top-up not tied to specific payment
              amount: overpayment.toFixed(2),
              transactionMethod: 'existing_balance',
              notes: `overpayment_credit:${payment.description || payment.type || 'Payment'}`,
            }, resolvedTenantId, userIdBigInt);
          }

          // Update athlete balance
          await updateAthleteBalance(data.athleteId, resolvedTenantId!, userIdBigInt);
        }

        return transaction;
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  async updatePaymentTransaction(
    id: string,
    data: {
      athleteId?: string;
      paymentId?: string | null;
      amount?: string;
      transactionMethod?: 'cash' | 'bank_transfer' | 'credit_card' | 'existing_balance';
      transactionDate?: Date;
      notes?: string | null;
    },
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'updatePaymentTransaction',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;
        return await TenantAwareDB.updatePaymentTransaction(id, data, resolvedTenantId, userIdBigInt);
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  async deletePaymentTransaction(
    id: string,
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'deletePaymentTransaction',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.deletePaymentTransaction(id, resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  /**
   * Process a payment by creating a transaction and updating payment status
   */
  async processPaymentWithTransaction(
    paymentId: string,
    transactionData: {
      amount: string;
      transactionMethod: 'cash' | 'bank_transfer' | 'credit_card';
      notes?: string | null;
    },
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'processPaymentWithTransaction',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;

        // Get the payment details first
        const payment = await TenantAwareDB.getPaymentById(paymentId, resolvedTenantId);
        if (!payment) {
          throw new Error('Payment not found');
        }

        // Create the transaction
        const transaction = await TenantAwareDB.createPaymentTransaction({
          athleteId: payment.athleteId,
          paymentId: paymentId,
          amount: transactionData.amount,
          transactionMethod: transactionData.transactionMethod,
          notes: transactionData.notes,
        }, resolvedTenantId, userIdBigInt);

        // Update payment amount_paid and status
        const currentAmountPaid = parseFloat(payment.amountPaid || '0');
        const transactionAmount = parseFloat(transactionData.amount);
        const totalAmount = parseFloat(payment.amount);
        const newAmountPaid = currentAmountPaid + transactionAmount;

        let newStatus: 'pending' | 'completed' | 'overdue' | 'cancelled' | 'partially_paid';
        if (newAmountPaid >= totalAmount) {
          newStatus = 'completed';
        } else if (newAmountPaid > 0) {
          newStatus = 'partially_paid';
        } else {
          newStatus = payment.status; // Keep current status
        }

        // Update the payment
        await TenantAwareDB.updatePayment(paymentId, {
          amountPaid: newAmountPaid.toFixed(2),
          status: newStatus,
        }, resolvedTenantId, userIdBigInt);

        // If overpayment, add to athlete balance
        if (newAmountPaid > totalAmount) {
          const overpayment = newAmountPaid - totalAmount;
          // Create a balance top-up transaction
          await TenantAwareDB.createPaymentTransaction({
            athleteId: payment.athleteId,
            paymentId: null, // Balance top-up not tied to specific payment
            amount: overpayment.toFixed(2),
            transactionMethod: 'existing_balance',
            notes: `Overpayment credit from payment ${paymentId}`,
          }, resolvedTenantId, userIdBigInt);
        }

        return { transaction, payment: { ...payment, amountPaid: newAmountPaid.toFixed(2), status: newStatus } };
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  /**
   * Add balance to athlete account and automatically allocate to pending payments
   */
  async addAthleteBalance(
    athleteId: string,
    amount: string,
    transactionMethod: 'cash' | 'bank_transfer' | 'credit_card',
    notes?: string | null,
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'addAthleteBalance',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;

        // Create balance top-up transaction
        const transaction = await TenantAwareDB.createPaymentTransaction({
          athleteId,
          paymentId: null, // Balance top-up not tied to specific payment
          amount,
          transactionMethod,
          notes: notes || 'Balance top-up',
        }, resolvedTenantId, userIdBigInt);

        // Process automatic balance allocation to pending payments
        try {
          const allocationResult = await balanceProcessorService().processAthleteBalanceAllocation(
            athleteId,
            resolvedTenantId,
            userId,
            resolvedTenantId,
            parseFloat(amount) // Pass the amount that was just added
          );

          return {
            transaction,
            balanceAllocation: allocationResult.success ? allocationResult.data : null,
          };
        } catch (error) {
          logger.error('Error processing balance allocation:', { error });
          // Return transaction even if allocation fails
          return {
            transaction,
            balanceAllocation: null,
            allocationError: error instanceof Error ? error.message : 'Unknown error',
          };
        }
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }
}

// Export singleton instance
let paymentTransactionServiceInstance: PaymentTransactionService | null = null;

export function paymentTransactionService(): PaymentTransactionService {
  if (!paymentTransactionServiceInstance) {
    paymentTransactionServiceInstance = new PaymentTransactionService();
  }
  return paymentTransactionServiceInstance;
}
