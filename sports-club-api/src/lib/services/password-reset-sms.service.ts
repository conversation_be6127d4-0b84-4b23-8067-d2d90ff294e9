import { TenantAwareDB } from '@/lib/db';
import logger from '@/lib/logger';

export class PasswordResetSmsService {
  /**
   * Create a new password reset SMS log entry
   */
  async createLog(
    username: string,
    userId: string,
    receiver: string,
    message: string,
    verificationCode: string,
    expiresInMinutes: number = 10
  ) {
    try {
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + expiresInMinutes);

      const log = await TenantAwareDB.createPasswordResetSmsLog({
        username,
        userId,
        receiver,
        message,
        verificationCode,
        expiresAt,
        status: 'pending'
      });

      logger.info('Password reset SMS log created', {
        logId: log.id,
        userId,
        username
      });

      return {
        success: true,
        data: log
      };
    } catch (error) {
      logger.error('Failed to create password reset SMS log', { error });
      return {
        success: false,
        error: {
          message: 'Failed to create password reset SMS log',
          details: error
        }
      };
    }
  }

  /**
   * Mark SMS as successfully sent
   */
  async markAsSent(
    logId: string,
    creditsUsed: number,
    providerResponse?: any
  ) {
    try {
      const updatedLog = await TenantAwareDB.updatePasswordResetSmsLogStatus(
        logId,
        'sent',
        {
          sentAt: new Date(),
          creditsUsed,
          providerResponse: providerResponse ? JSON.stringify(providerResponse) : undefined
        }
      );

      logger.info('Password reset SMS marked as sent', { logId });

      return {
        success: true,
        data: updatedLog
      };
    } catch (error) {
      logger.error('Failed to mark password reset SMS as sent', { error, logId });
      return {
        success: false,
        error: {
          message: 'Failed to update password reset SMS log',
          details: error
        }
      };
    }
  }

  /**
   * Mark SMS as failed
   */
  async markAsFailed(
    logId: string,
    errorMessage: string,
    providerResponse?: any
  ) {
    try {
      const updatedLog = await TenantAwareDB.updatePasswordResetSmsLogStatus(
        logId,
        'failed',
        {
          errorMessage,
          providerResponse: providerResponse ? JSON.stringify(providerResponse) : undefined
        }
      );

      logger.warn('Password reset SMS marked as failed', { 
        logId,
        errorMessage 
      });

      return {
        success: true,
        data: updatedLog
      };
    } catch (error) {
      logger.error('Failed to mark password reset SMS as failed', { error, logId });
      return {
        success: false,
        error: {
          message: 'Failed to update password reset SMS log',
          details: error
        }
      };
    }
  }

  /**
   * Mark verification code as used (when password is successfully reset)
   */
  async markCodeAsUsed(verificationCode: string, userId: string) {
    try {
      const updatedLog = await TenantAwareDB.markPasswordResetCodeAsUsed(
        verificationCode,
        userId
      );

      if (!updatedLog) {
        logger.warn('No matching verification code found to mark as used', {
          userId,
          codePrefix: verificationCode.substring(0, 2) + '****'
        });
        return {
          success: false,
          error: {
            message: 'Verification code not found or already used'
          }
        };
      }

      logger.info('Password reset code marked as used', {
        logId: updatedLog.id,
        userId
      });

      return {
        success: true,
        data: updatedLog
      };
    } catch (error) {
      logger.error('Failed to mark password reset code as used', { error });
      return {
        success: false,
        error: {
          message: 'Failed to mark code as used',
          details: error
        }
      };
    }
  }

  /**
   * Check if user has an active verification code
   */
  async getActiveVerificationCode(userId: string) {
    try {
      const activeCode = await TenantAwareDB.getActivePasswordResetVerificationCode(userId);
      
      return {
        success: true,
        data: activeCode
      };
    } catch (error) {
      logger.error('Failed to get active verification code', { error, userId });
      return {
        success: false,
        error: {
          message: 'Failed to check for active verification code',
          details: error
        }
      };
    }
  }

  /**
   * Get password reset SMS logs for a user
   */
  async getLogsByUserId(userId: string, limit: number = 10) {
    try {
      const logs = await TenantAwareDB.getPasswordResetSmsLogsByUserId(userId, limit);
      
      return {
        success: true,
        data: logs
      };
    } catch (error) {
      logger.error('Failed to get password reset SMS logs by user ID', { error, userId });
      return {
        success: false,
        error: {
          message: 'Failed to retrieve password reset SMS logs',
          details: error
        }
      };
    }
  }

  /**
   * Get password reset SMS logs for a username
   */
  async getLogsByUsername(username: string, limit: number = 10) {
    try {
      const logs = await TenantAwareDB.getPasswordResetSmsLogsByUsername(username, limit);
      
      return {
        success: true,
        data: logs
      };
    } catch (error) {
      logger.error('Failed to get password reset SMS logs by username', { error, username });
      return {
        success: false,
        error: {
          message: 'Failed to retrieve password reset SMS logs',
          details: error
        }
      };
    }
  }

  /**
   * Get recent password reset SMS logs
   */
  async getRecentLogs(hours: number = 24) {
    try {
      const logs = await TenantAwareDB.getRecentPasswordResetSmsLogs(hours);
      
      return {
        success: true,
        data: logs
      };
    } catch (error) {
      logger.error('Failed to get recent password reset SMS logs', { error });
      return {
        success: false,
        error: {
          message: 'Failed to retrieve recent password reset SMS logs',
          details: error
        }
      };
    }
  }

  /**
   * Get password reset SMS statistics
   */
  async getStats(days: number = 30) {
    try {
      const stats = await TenantAwareDB.getPasswordResetSmsStats(days);
      
      return {
        success: true,
        data: stats
      };
    } catch (error) {
      logger.error('Failed to get password reset SMS stats', { error });
      return {
        success: false,
        error: {
          message: 'Failed to retrieve password reset SMS statistics',
          details: error
        }
      };
    }
  }

  /**
   * Clean up old/expired password reset logs
   */
  async cleanupExpiredLogs(daysToKeep: number = 30) {
    try {
      await TenantAwareDB.cleanupExpiredPasswordResetLogs(daysToKeep);
      
      logger.info('Cleaned up expired password reset SMS logs', { daysToKeep });
      
      return {
        success: true,
        data: {
          message: `Cleaned up logs older than ${daysToKeep} days`
        }
      };
    } catch (error) {
      logger.error('Failed to cleanup expired password reset SMS logs', { error });
      return {
        success: false,
        error: {
          message: 'Failed to cleanup expired logs',
          details: error
        }
      };
    }
  }
}

// Create singleton instance
let passwordResetSmsService: PasswordResetSmsService | null = null;

export function getPasswordResetSmsService(): PasswordResetSmsService {
  if (!passwordResetSmsService) {
    passwordResetSmsService = new PasswordResetSmsService();
  }
  return passwordResetSmsService;
}
