import Netgsm from '@netgsm/sms';
import { SmsProvider, SendSmsParams, SmsProviderResponse } from '../types';
import logger from '@/lib/logger';

/**
 * NetGSM SMS Provider
 * Implementation for NetGSM SMS service using official @netgsm/sms SDK
 */
export class NetGsmProvider implements SmsProvider {
  private readonly netgsm: Netgsm;
  private readonly username: string;
  private readonly password: string;
  private readonly appname?: string;

  constructor(config?: {
    username?: string;
    password?: string;
    appname?: string;
  }) {
    // Configuration from parameters or environment variables
    this.username = config?.username || process.env.NETGSM_USERNAME || '';
    this.password = config?.password || process.env.NETGSM_PASSWORD || '';
    this.appname = config?.appname || process.env.NETGSM_APPNAME;

    // Initialize NetGSM client
    this.netgsm = new Netgsm({
      username: this.username,
      password: this.password,
      appname: this.appname
    });
  }

  /**
   * Send SMS messages via NetGSM API using official SDK
   * @param params SMS parameters
   * @returns Promise with NetGSM response
   */
  async sendSms(params: SendSmsParams): Promise<SmsProviderResponse> {
    try {
      logger.info('NetGSM Provider - sendSms called with:', {
        senderIdentifier: params.senderIdentifier,
        encoding: params.encoding,
        messageCount: params.messages.length,
        // Don't log actual messages for privacy
      });

      // Note: Validation is handled at the service layer
      // Provider assumes all parameters are valid

      // Prepare messages in NetGSM SDK format
      const netgsmMessages = params.messages.map(message => ({
        msg: message.message,
        no: message.receiver
      }));

      // Send SMS using NetGSM SDK
      const response = await this.netgsm.sendRestSms({
        msgheader: params.senderIdentifier,
        encoding: params.encoding ?? 'TR',
        messages: netgsmMessages
        // Note: Not using startdate and stopdate as per user request
      });

      // Check if the response indicates success
      // NetGSM returns jobid on success, error codes on failure
      if (response.jobid) {
        return {
          success: true,
          data: {
            provider: 'NetGSM',
            sentAt: new Date().toISOString(),
            messageCount: params.messages.length,
            jobId: response.jobid,
            netgsmResponse: response
          },
          messageIds: [response.jobid] // NetGSM returns a single job ID for the batch
        };
      } else {
        // Handle NetGSM error response
        const errorMessage = this.getNetGsmErrorMessage(response);
        return {
          success: false,
          error: errorMessage,
          errorCode: 'NETGSM_API_ERROR',
          data: response
        };
      }

    } catch (error) {
      logger.error('NetGSM Provider error:', { error });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        errorCode: 'PROVIDER_ERROR'
      };
    }
  }

  /**
   * Get provider name
   */
  getProviderName(): string {
    return 'NetGSM';
  }

  /**
   * Validate NetGSM configuration
   */
  isConfigured(): boolean {
    return !!(this.username && this.password);
  }

  /**
   * Get NetGSM client instance (for advanced usage)
   */
  getClient(): Netgsm {
    return this.netgsm;
  }

  /**
   * Map NetGSM error codes to human-readable messages
   * Based on NetGSM API documentation
   */
  private getNetGsmErrorMessage(response: any): string {
    // If response is a string, it might be an error code
    if (typeof response === 'string') {
      const errorCode = response.trim();

      const errorMessages: Record<string, string> = {
        '20': 'Message text not found',
        '30': 'Invalid username, password or API access not allowed',
        '40': 'Message header (sender) not defined',
        '50': 'File format error',
        '60': 'Message text exceeds character limit',
        '70': 'Invalid phone number',
        '80': 'Message text not found',
        '85': 'Invalid message header',
        '100': 'System error',
        '101': 'System error'
      };

      return errorMessages[errorCode] || `NetGSM API error: ${errorCode}`;
    }

    // If response is an object, try to extract error information
    if (typeof response === 'object' && response !== null) {
      if (response.error) {
        return `NetGSM API error: ${response.error}`;
      }
      if (response.message) {
        return `NetGSM API error: ${response.message}`;
      }
    }

    return `NetGSM API error: ${JSON.stringify(response)}`;
  }
}
