import { ValidationError } from '../errors/types';

/**
 * Common validation functions
 */

export const validators = {
  /**
   * Validates required fields
   */
  required: (field: string, message?: string, t?: (key: string, options?: any) => string) => 
    (value: any): ValidationError | null => {
      if (value === null || value === undefined || value === '') {
        return {
          field,
          message: message || (t ? t('common.validation.required') : `${field} is required`),
          value,
          code: 'REQUIRED_FIELD_MISSING',
        };
      }
      return null;
    },

  /**
   * Validates string length
   */
  stringLength: (field: string, min?: number, max?: number, message?: string) =>
    (value: string): ValidationError | null => {
      if (typeof value !== 'string') return null;
      
      if (min !== undefined && value.length < min) {
        return {
          field,
          message: message || `${field} must be at least ${min} characters`,
          value,
          code: 'INVALID_LENGTH',
        };
      }
      
      if (max !== undefined && value.length > max) {
        return {
          field,
          message: message || `${field} must be no more than ${max} characters`,
          value,
          code: 'INVALID_LENGTH',
        };
      }
      
      return null;
    },

  /**
   * Validates email format
   */
  email: (field: string, message?: string, t?: (key: string, options?: any) => string) =>
    (value: string): ValidationError | null => {
      if (!value) return null;
      
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        return {
          field,
          message: message || (t ? t('common.validation.email') : `${field} must be a valid email address`),
          value,
          code: 'INVALID_FORMAT',
        };
      }
      
      return null;
    },

  /**
   * Validates phone number format
   */
  phone: (field: string, message?: string, t?: (key: string, options?: any) => string) =>
    (value: string): ValidationError | null => {
      if (!value) return null;
      
      // Turkish phone number format (starts with +90 or 0, followed by 10 digits)
      const phoneRegex = /^(\+90|0)?[1-9]\d{9}$/;
      if (!phoneRegex.test(value.replace(/\s/g, ''))) {
        return {
          field,
          message: message || (t ? t('common.validation.phone') : `${field} must be a valid phone number`),
          value,
          code: 'INVALID_FORMAT',
        };
      }
      
      return null;
    },

  /**
   * Validates parent phone number format - exactly 10 digits, cannot start with 0
   */
  parentPhone: (field: string, message?: string, t?: (key: string, options?: any) => string) =>
    (value: string): ValidationError | null => {
      if (!value) return null;
      
      // Remove all spaces, dashes, and formatting
      const cleanedValue = value.replace(/[\s\-]/g, '').replace(/\+90/g, '').replace(/^0/, '');
      
      // Must be exactly 10 digits and cannot start with 0
      const parentPhoneRegex = /^[1-9]\d{9}$/;
      if (!parentPhoneRegex.test(cleanedValue)) {
        return {
          field,
          message: message || 'parent_phone_format_validation',
          value,
          code: 'INVALID_FORMAT',
        };
      }
      
      return null;
    },

  /**
   * Validates national ID format (Turkish)
   */
  nationalId: (field: string, message?: string, t?: (key: string, options?: any) => string) =>
    (value: string): ValidationError | null => {
      if (!value) return null;
      
      // Turkish national ID is 11 digits
      if (!/^\d{11}$/.test(value)) {
        return {
          field,
          message: message || (t ? t('common.validation.nationalId') : `${field} must be 11 digits`),
          value,
          code: 'INVALID_FORMAT',
        };
      }
      
      // Validate checksum (Turkish national ID algorithm)
      const digits = value.split('').map(Number);
      const checksum1 = (digits[0] + digits[2] + digits[4] + digits[6] + digits[8]) * 7 - 
                       (digits[1] + digits[3] + digits[5] + digits[7]);
      const checksum2 = (digits[0] + digits[1] + digits[2] + digits[3] + digits[4] + 
                        digits[5] + digits[6] + digits[7] + digits[8] + digits[9]) % 10;
      
      if (checksum1 % 10 !== digits[9] || checksum2 !== digits[10]) {
        return {
          field,
          message: message || (t ? t('common.validation.nationalIdInvalid') : `${field} is not a valid national ID`),
          value,
          code: 'INVALID_FORMAT',
        };
      }
      
      return null;
    },

  /**
   * Validates numeric range
   */
  numericRange: (field: string, min?: number, max?: number, message?: string) =>
    (value: number): ValidationError | null => {
      if (typeof value !== 'number' || isNaN(value)) {
        return {
          field,
          message: message || `${field} must be a valid number`,
          value,
          code: 'INVALID_FORMAT',
        };
      }
      
      if (min !== undefined && value < min) {
        return {
          field,
          message: message || `${field} must be at least ${min}`,
          value,
          code: 'INVALID_RANGE',
        };
      }
      
      if (max !== undefined && value > max) {
        return {
          field,
          message: message || `${field} must be no more than ${max}`,
          value,
          code: 'INVALID_RANGE',
        };
      }
      
      return null;
    },

  /**
   * Validates positive number
   */
  positiveNumber: (field: string, message?: string) =>
    (value: number): ValidationError | null => {
      if (typeof value !== 'number' || isNaN(value) || value <= 0) {
        return {
          field,
          message: message || `${field} must be a positive number`,
          value,
          code: 'INVALID_RANGE',
        };
      }
      
      return null;
    },

  /**
   * Validates date format and range
   */
  date: (field: string, minDate?: Date, maxDate?: Date, message?: string) =>
    (value: string | Date): ValidationError | null => {
      let date: Date;
      
      if (typeof value === 'string') {
        date = new Date(value);
      } else if (value instanceof Date) {
        date = value;
      } else {
        return {
          field,
          message: message || `${field} must be a valid date`,
          value,
          code: 'INVALID_FORMAT',
        };
      }
      
      if (isNaN(date.getTime())) {
        return {
          field,
          message: message || `${field} must be a valid date`,
          value,
          code: 'INVALID_FORMAT',
        };
      }
      
      if (minDate && date < minDate) {
        return {
          field,
          message: message || `${field} must be after ${minDate.toDateString()}`,
          value,
          code: 'INVALID_RANGE',
        };
      }
      
      if (maxDate && date > maxDate) {
        return {
          field,
          message: message || `${field} must be before ${maxDate.toDateString()}`,
          value,
          code: 'INVALID_RANGE',
        };
      }
      
      return null;
    },

  /**
   * Validates that value is in allowed list
   */
  oneOf: <T>(field: string, allowedValues: T[], message?: string) =>
    (value: T): ValidationError | null => {
      if (!allowedValues.includes(value)) {
        return {
          field,
          message: message || `${field} must be one of: ${allowedValues.join(', ')}`,
          value,
          code: 'INVALID_VALUE',
        };
      }
      
      return null;
    },

  /**
   * Custom validation function
   */
  custom: <T>(field: string, validateFn: (value: T) => boolean, message: string) =>
    (value: T): ValidationError | null => {
      if (!validateFn(value)) {
        return {
          field,
          message,
          value,
          code: 'CUSTOM_VALIDATION_FAILED',
        };
      }
      
      return null;
    },
};

/**
 * Combines multiple validators for a field
 */
export function combineValidators<T>(
  ...validators: Array<(value: T) => ValidationError | null>
) {
  return (value: T): ValidationError | null => {
    for (const validator of validators) {
      const error = validator(value);
      if (error) return error;
    }
    return null;
  };
}

/**
 * Creates a validator for nested object properties
 */
export function validateProperty<T, K extends keyof T>(
  property: K,
  validator: (value: T[K]) => ValidationError | null
) {
  return (obj: T): ValidationError | null => {
    return validator(obj[property]);
  };
}

/**
 * Common validation patterns for entities
 */
export const commonValidators = {
  /**
   * Standard name validation (required, 2-255 characters)
   */
  name: (field: string = 'name') => combineValidators(
    validators.required(field),
    validators.stringLength(field, 2, 255)
  ),

  /**
   * Standard description validation (optional, max 1000 characters)
   */
  description: (field: string = 'description') => validators.stringLength(field, undefined, 1000),

  /**
   * Standard email validation
   */
  email: (field: string = 'email') => combineValidators(
    validators.required(field),
    validators.email(field)
  ),

  /**
   * Standard phone validation (Turkish format)
   */
  phone: (field: string = 'phone') => combineValidators(
    validators.required(field),
    validators.phone(field)
  ),

  /**
   * Standard price validation
   */
  price: (field: string = 'price') => combineValidators(
    validators.required(field),
    validators.custom(field, (value: string) => {
      const numericValue = parseFloat(value);
      return !isNaN(numericValue) && numericValue >= 0;
    }, `${field} must be a valid positive number`)
  ),

  /**
   * Standard year validation
   */
  foundedYear: () => combineValidators(
    validators.required('foundedYear'),
    validators.custom('foundedYear', (value: number) => {
      const currentYear = new Date().getFullYear();
      return value >= 1800 && value <= currentYear + 10;
    }, 'Founded year must be between 1800 and current year + 10')
  ),

  /**
   * Standard Turkish national ID validation
   */
  nationalId: (field: string = 'nationalId') => combineValidators(
    validators.required(field),
    validators.custom(field, (value: string) => /^\d{11}$/.test(value), `${field} must be 11 digits`)
  ),

  /**
   * Standard status validation
   */
  status: (field: string = 'status', validStatuses: string[] = ['active', 'inactive']) =>
    validators.oneOf(field, validStatuses),

  /**
   * Standard category validation
   */
  category: (field: string, validCategories: string[]) =>
    validators.oneOf(field, validCategories),
};
