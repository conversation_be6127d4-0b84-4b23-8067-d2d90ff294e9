import { TenantAwareDB } from '../db';
import { BaseService } from './base';
import { ServiceResult } from '../errors/types';

export class FinancialService extends BaseService {
  
  constructor() {
    super('FinancialService');
  }

  async getFinancialSummary(tenantId?: string): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'getFinancialSummary',
      async () => {
        return await TenantAwareDB.getFinancialSummary(tenantId);
      },
      { tenantId }
    );
  }
}
