import { eq, and, desc, lt } from 'drizzle-orm';
import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { TenantAwareDBBase } from './base';

export class ItemsDB extends TenantAwareDBBase {

  static async getItems(tenantId?: string) {
    return this.getAllWithTenantFilter<typeof schema.items.$inferSelect>(schema.items, tenantId, desc(schema.items.createdAt));
  }

  static async getItemById(id: string, tenantId?: string) {
    return this.getByIdWithTenantFilter<typeof schema.items.$inferSelect>(schema.items, id, tenantId);
  }

  static async getItemByName(name: string, tenantId?: string) {
    return this.getByNameWithTenantFilter<typeof schema.items.$inferSelect>(schema.items, name, tenantId);
  }

  static async createItem(
    data: Omit<typeof schema.items.$inferInsert, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.insertWithAudit(schema.items, data, tenantId, userId);
  }

  static async updateItem(
    id: string, 
    data: Partial<typeof schema.items.$inferInsert>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.updateWithAudit(schema.items, id, data, tenantId, userId);
  }

  static async deleteItem(id: string, tenantId?: string) {
    return this.deleteWithTenantFilter(schema.items, id, tenantId);
  }

  // Item Purchase Operations
  static async createItemPurchase(
    data: Omit<typeof schema.itemPurchases.$inferInsert, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.insertWithAudit(schema.itemPurchases, data, tenantId, userId);
  }

  static async getItemPurchases(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select({
      id: schema.itemPurchases.id,
      itemId: schema.itemPurchases.itemId,
      athleteId: schema.itemPurchases.athleteId,
      quantity: schema.itemPurchases.quantity,
      totalPrice: schema.itemPurchases.totalPrice,
      purchaseDate: schema.itemPurchases.purchaseDate,
      createdAt: schema.itemPurchases.createdAt,
      updatedAt: schema.itemPurchases.updatedAt,
      item: {
        id: schema.items.id,
        name: schema.items.name,
        description: schema.items.description,
        price: schema.items.price,
        category: schema.items.category,
      },
      athlete: {
        id: schema.athletes.id,
        name: schema.athletes.name,
        surname: schema.athletes.surname,
      }
    })
    .from(schema.itemPurchases)
    .leftJoin(schema.items, eq(schema.itemPurchases.itemId, schema.items.id))
    .leftJoin(schema.athletes, eq(schema.itemPurchases.athleteId, schema.athletes.id))
    .where(eq(schema.itemPurchases.tenantId, filter.tenantId))
    .orderBy(desc(schema.itemPurchases.purchaseDate));
  }

  static async getItemPurchaseById(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select().from(schema.itemPurchases)
      .where(and(
        eq(schema.itemPurchases.id, id),
        eq(schema.itemPurchases.tenantId, filter.tenantId)
      ));
    return result[0] || null;
  }

  static async updateItemPurchase(
    id: string, 
    data: Partial<typeof schema.itemPurchases.$inferInsert>, 
    tenantId?: string, 
    userId?: bigint
  ) {
    return this.updateWithAudit(schema.itemPurchases, id, data, tenantId, userId);
  }

  static async updateItemStock(
    itemId: string, 
    quantityChange: number, 
    tenantId?: string, 
    userId?: bigint
  ) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Get current item
    const item = await this.getItemById(itemId, tenantId);
    if (!item) throw new Error('Item not found');
    
    const newStock = parseInt(item.stock.toString()) + quantityChange;
    if (newStock < 0) throw new Error('Insufficient stock');
    
    return this.updateWithAudit(schema.items, itemId, { stock: newStock }, tenantId, userId);
  }

  static async getPendingPurchasesByItemId(itemId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select().from(schema.itemPurchases)
      .innerJoin(
        schema.payments,
        eq(schema.payments.itemPurchaseId, schema.itemPurchases.id)
      )
      .where(and(
        eq(schema.itemPurchases.itemId, itemId),
        eq(schema.itemPurchases.tenantId, filter.tenantId),
        eq(schema.payments.status, 'pending')
      ))
      .orderBy(desc(schema.itemPurchases.purchaseDate));
  }

  static async getItemsByCategory(category: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select().from(schema.items)
      .where(and(
        eq(schema.items.tenantId, filter.tenantId),
        eq(schema.items.category, category as any)
      ))
      .orderBy(desc(schema.items.createdAt));
  }

  static async getLowStockItems(threshold: number, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select().from(schema.items)
      .where(and(
        eq(schema.items.tenantId, filter.tenantId),
        lt(schema.items.stock, threshold)
      ))
      .orderBy(schema.items.stock);
  }
}
