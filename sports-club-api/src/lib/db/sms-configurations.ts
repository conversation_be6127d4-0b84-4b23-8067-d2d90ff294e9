import { eq, and, desc } from 'drizzle-orm';
import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { TenantAwareDBBase } from './base';

export class SmsConfigurationsDB extends TenantAwareDBBase {
  
  static async getSmsConfigurations(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    return db.select().from(schema.smsConfigurations)
      .where(eq(schema.smsConfigurations.tenantId, filter.tenantId))
      .orderBy(desc(schema.smsConfigurations.version));
  }

  static async getActiveSmsConfiguration(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select().from(schema.smsConfigurations)
      .where(and(
        eq(schema.smsConfigurations.tenantId, filter.tenantId),
        eq(schema.smsConfigurations.isActive, true)
      ))
      .orderBy(desc(schema.smsConfigurations.version))
      .limit(1);
    
    return result[0] || null;
  }

  static async getSmsConfigurationById(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select().from(schema.smsConfigurations)
      .where(and(
        eq(schema.smsConfigurations.id, id),
        eq(schema.smsConfigurations.tenantId, filter.tenantId)
      ));
    
    return result[0] || null;
  }

  static async getSmsConfigurationByVersion(version: number, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select().from(schema.smsConfigurations)
      .where(and(
        eq(schema.smsConfigurations.tenantId, filter.tenantId),
        eq(schema.smsConfigurations.version, version)
      ));
    
    return result[0] || null;
  }

  static async getNextVersion(tenantId?: string): Promise<number> {
    const filter = await this.getTenantFilter(tenantId);
    const result = await db.select({
      maxVersion: schema.smsConfigurations.version
    })
    .from(schema.smsConfigurations)
    .where(eq(schema.smsConfigurations.tenantId, filter.tenantId))
    .orderBy(desc(schema.smsConfigurations.version))
    .limit(1);
    
    return (result[0]?.maxVersion || 0) + 1;
  }

  static async createSmsConfiguration(
    data: {
      pendingPaymentTemplate: string;
      overduePaymentTemplate: string;
      pendingReminderDays: string; // JSON string
      overdueReminderDays: string; // JSON string
    },
    tenantId?: string,
    userId?: bigint
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const auditInfo = await this.getAuditInfo(userId);
    const version = await this.getNextVersion(tenantId);

    // Deactivate all existing configurations
    await db.update(schema.smsConfigurations)
      .set({ 
        isActive: false,
        updatedAt: new Date(),
        updatedBy: auditInfo.updatedBy
      })
      .where(eq(schema.smsConfigurations.tenantId, filter.tenantId));

    // Create new configuration
    const insertData = {
      ...data,
      ...filter,
      ...auditInfo,
      version,
      isActive: true,
    };

    const result = await db.insert(schema.smsConfigurations)
      .values(insertData)
      .returning();
    
    return result[0];
  }

  static async updateSmsConfiguration(
    id: string,
    data: {
      pendingPaymentTemplate?: string;
      overduePaymentTemplate?: string;
      pendingReminderDays?: string; // JSON string
      overdueReminderDays?: string; // JSON string;
      isActive?: boolean;
    },
    tenantId?: string,
    userId?: bigint
  ) {
    return this.updateWithAudit(schema.smsConfigurations, id, data, tenantId, userId);
  }

  static async deleteSmsConfiguration(id: string, tenantId?: string) {
    return this.deleteWithTenantFilter(schema.smsConfigurations, id, tenantId);
  }

  static async deactivateAllConfigurations(tenantId?: string, userId?: bigint) {
    const filter = await this.getTenantFilter(tenantId);
    const auditInfo = await this.getAuditInfo(userId);

    return db.update(schema.smsConfigurations)
      .set({ 
        isActive: false,
        updatedAt: new Date(),
        updatedBy: auditInfo.updatedBy
      })
      .where(eq(schema.smsConfigurations.tenantId, filter.tenantId));
  }

  static async activateConfiguration(id: string, tenantId?: string, userId?: bigint) {
    const filter = await this.getTenantFilter(tenantId);
    const auditInfo = await this.getAuditInfo(userId);

    // First deactivate all configurations
    await this.deactivateAllConfigurations(tenantId, userId);

    // Then activate the specified one
    return db.update(schema.smsConfigurations)
      .set({ 
        isActive: true,
        updatedAt: new Date(),
        updatedBy: auditInfo.updatedBy
      })
      .where(and(
        eq(schema.smsConfigurations.id, id),
        eq(schema.smsConfigurations.tenantId, filter.tenantId)
      ));
  }
}
