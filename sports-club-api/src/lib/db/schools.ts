import { eq, and } from 'drizzle-orm';
import { TenantAwareDBBase } from './base';
import * as schema from '@/src/db/schema';

/**
 * Database operations for Schools
 */
export class SchoolsDB extends TenantAwareDBBase {
  static async getSchools(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Get schools with their branches and instructor count
    const schoolsWithRelations = await this.database
      .select({
        id: schema.schools.id,
        name: schema.schools.name,
        foundedYear: schema.schools.foundedYear,
        logo: schema.schools.logo,
        address: schema.schools.address,
        phone: schema.schools.phone,
        email: schema.schools.email,
        createdAt: schema.schools.createdAt,
        updatedAt: schema.schools.updatedAt,
        createdBy: schema.schools.createdBy,
        updatedBy: schema.schools.updatedBy,
        // Get branch info if related
        branchId: schema.branches.id,
        branchName: schema.branches.name,
        // Get instructor count
        instructorId: schema.instructors.id,
      })
      .from(schema.schools)
      .leftJoin(
        schema.schoolBranches,
        eq(schema.schoolBranches.schoolId, schema.schools.id)
      )
      .leftJoin(
        schema.branches,
        eq(schema.branches.id, schema.schoolBranches.branchId)
      )
      .leftJoin(
        schema.instructorSchools,
        eq(schema.instructorSchools.schoolId, schema.schools.id)
      )
      .leftJoin(
        schema.instructors,
        eq(schema.instructors.id, schema.instructorSchools.instructorId)
      )
      .where(eq(schema.schools.tenantId, filter.tenantId));

    // Group results to construct the expected format
    const schoolMap = new Map();
    
    for (const row of schoolsWithRelations) {
      if (!schoolMap.has(row.id)) {
        schoolMap.set(row.id, {
          id: row.id,
          name: row.name,
          foundedYear: row.foundedYear,
          logo: row.logo,
          address: row.address,
          phone: row.phone,
          email: row.email,
          createdAt: row.createdAt,
          updatedAt: row.updatedAt,
          createdBy: row.createdBy,
          updatedBy: row.updatedBy,
          branches: [],
          instructors: [],
        });
      }
      
      const school = schoolMap.get(row.id);
      
      // Add branch if exists and not already added
      if (row.branchId && row.branchName && !school.branches.find((b: any) => b.id === row.branchId)) {
        school.branches.push({
          id: row.branchId,
          name: row.branchName,
        });
      }
      
      // Add instructor ID if exists and not already added
      if (row.instructorId && !school.instructors.includes(row.instructorId)) {
        school.instructors.push(row.instructorId);
      }
    }
    
    return Array.from(schoolMap.values());
  }

  static async getSchoolById(id: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    // Get school with its branches and instructor count
    const schoolWithRelations = await this.database
      .select({
        id: schema.schools.id,
        name: schema.schools.name,
        foundedYear: schema.schools.foundedYear,
        logo: schema.schools.logo,
        address: schema.schools.address,
        phone: schema.schools.phone,
        email: schema.schools.email,
        createdAt: schema.schools.createdAt,
        updatedAt: schema.schools.updatedAt,
        createdBy: schema.schools.createdBy,
        updatedBy: schema.schools.updatedBy,
        // Get branch info if related
        branchId: schema.branches.id,
        branchName: schema.branches.name,
        // Get instructor count
        instructorId: schema.instructors.id,
      })
      .from(schema.schools)
      .leftJoin(
        schema.schoolBranches,
        eq(schema.schoolBranches.schoolId, schema.schools.id)
      )
      .leftJoin(
        schema.branches,
        eq(schema.branches.id, schema.schoolBranches.branchId)
      )
      .leftJoin(
        schema.instructorSchools,
        eq(schema.instructorSchools.schoolId, schema.schools.id)
      )
      .leftJoin(
        schema.instructors,
        eq(schema.instructors.id, schema.instructorSchools.instructorId)
      )
      .where(and(
        eq(schema.schools.id, id),
        eq(schema.schools.tenantId, filter.tenantId)
      ));

    if (schoolWithRelations.length === 0) {
      return null;
    }

    // Build the school object from the joined results
    const firstRow = schoolWithRelations[0];
    const school = {
      id: firstRow.id,
      name: firstRow.name,
      foundedYear: firstRow.foundedYear,
      logo: firstRow.logo,
      address: firstRow.address,
      phone: firstRow.phone,
      email: firstRow.email,
      createdAt: firstRow.createdAt,
      updatedAt: firstRow.updatedAt,
      createdBy: firstRow.createdBy,
      updatedBy: firstRow.updatedBy,
      branches: [] as Array<{ id: string; name: string }>,
      instructors: [] as string[],
    };

    // Collect unique branches and instructors
    for (const row of schoolWithRelations) {
      // Add branch if exists and not already added
      if (row.branchId && row.branchName && !school.branches.find(b => b.id === row.branchId)) {
        school.branches.push({
          id: row.branchId,
          name: row.branchName,
        });
      }
      
      // Add instructor ID if exists and not already added
      if (row.instructorId && !school.instructors.includes(row.instructorId)) {
        school.instructors.push(row.instructorId);
      }
    }
    
    return school;
  }

  static async createSchool(data: Omit<typeof schema.schools.$inferInsert, 'tenantId' | 'createdBy' | 'updatedBy' | 'createdAt' | 'updatedAt'>, tenantId?: string, userId?: bigint) {
    return this.insertWithAudit(schema.schools, data, tenantId, userId);
  }

  static async updateSchool(id: string, data: Partial<typeof schema.schools.$inferInsert>, tenantId?: string, userId?: bigint) {
    return this.updateWithAudit(schema.schools, id, data, tenantId, userId);
  }

  static async getSchoolByName(name: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    const school = await this.database
      .select()
      .from(schema.schools)
      .where(and(
        eq(schema.schools.name, name),
        eq(schema.schools.tenantId, filter.tenantId)
      ))
      .limit(1);

    return school[0] || null;
  }

  static async getTeamsBySchoolId(schoolId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    const teams = await this.database
      .select()
      .from(schema.teams)
      .where(and(
        eq(schema.teams.schoolId, schoolId),
        eq(schema.teams.tenantId, filter.tenantId)
      ));

    return teams;
  }

  static async deleteSchool(id: string, tenantId?: string) {
    return this.deleteWithTenantFilter(schema.schools, id, tenantId);
  }

  // School-Branch relationship management
  static async assignBranchesToSchool(schoolId: string, branchIds: string[], tenantId?: string, userId?: bigint) {
    // First, remove existing school-branch associations
    await this.database
      .delete(schema.schoolBranches)
      .where(eq(schema.schoolBranches.schoolId, schoolId));

    // Then, add new associations
    if (branchIds.length > 0) {
      const associations = branchIds.map(branchId => ({
        schoolId,
        branchId,
        createdAt: new Date(),
      }));

      await this.database
        .insert(schema.schoolBranches)
        .values(associations);
    }
  }

  static async getSchoolBranches(schoolId: string, tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    
    const branches = await this.database
      .select({
        id: schema.branches.id,
        name: schema.branches.name,
        description: schema.branches.description,
      })
      .from(schema.schoolBranches)
      .innerJoin(schema.branches, eq(schema.branches.id, schema.schoolBranches.branchId))
      .where(eq(schema.schoolBranches.schoolId, schoolId));

    return branches;
  }
}
