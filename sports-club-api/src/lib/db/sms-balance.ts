import { eq, and } from 'drizzle-orm';
import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { TenantAwareDBBase } from './base';
import logger from '@/lib/logger';

export class SmsBalanceDB extends TenantAwareDBBase {
  
  static async getSmsBalance(tenantId?: string) {
    const filter = await this.getTenantFilter(tenantId);
    logger.info('🔍 DB - Getting SMS balance for tenant:', filter.tenantId);

    const result = await db.select().from(schema.smsBalance)
      .where(eq(schema.smsBalance.tenantId, filter.tenantId));
    logger.info('📊 DB - SMS balance query result:', result);

    return result[0] || null;
  }

  static async createSmsBalance(
    data: {
      balance: number;
    },
    tenantId?: string,
    userId?: bigint
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const auditInfo = await this.getAuditInfo(userId);
    
    const insertData = {
      ...data,
      ...filter,
      ...auditInfo,
      lastUpdated: new Date(),
    };

    const result = await db.insert(schema.smsBalance)
      .values(insertData)
      .returning();
    
    return result[0];
  }

  static async updateSmsBalance(
    balance: number,
    tenantId?: string,
    userId?: bigint
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const auditInfo = await this.getAuditInfo(userId);
    
    const result = await db.update(schema.smsBalance)
      .set({
        balance,
        lastUpdated: new Date(),
        updatedAt: new Date(),
        updatedBy: auditInfo.updatedBy,
      })
      .where(eq(schema.smsBalance.tenantId, filter.tenantId))
      .returning();
    
    return result[0];
  }

  static async decrementSmsBalance(
    amount: number,
    tenantId?: string,
    userId?: bigint
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const auditInfo = await this.getAuditInfo(userId);
    
    // Get current balance first
    const currentBalance = await this.getSmsBalance(tenantId);
    if (!currentBalance) {
      throw new Error('SMS balance not found for tenant');
    }

    const newBalance = currentBalance.balance - amount;
    if (newBalance < 0) {
      throw new Error('Insufficient SMS balance');
    }

    const result = await db.update(schema.smsBalance)
      .set({
        balance: newBalance,
        lastUpdated: new Date(),
        updatedAt: new Date(),
        updatedBy: auditInfo.updatedBy,
      })
      .where(eq(schema.smsBalance.tenantId, filter.tenantId))
      .returning();
    
    return result[0];
  }

  static async incrementSmsBalance(
    amount: number,
    tenantId?: string,
    userId?: bigint
  ) {
    const filter = await this.getTenantFilter(tenantId);
    const auditInfo = await this.getAuditInfo(userId);
    
    // Get current balance first
    const currentBalance = await this.getSmsBalance(tenantId);
    if (!currentBalance) {
      // Create initial balance if it doesn't exist
      return this.createSmsBalance({ balance: amount }, tenantId, userId);
    }

    const newBalance = currentBalance.balance + amount;

    const result = await db.update(schema.smsBalance)
      .set({
        balance: newBalance,
        lastUpdated: new Date(),
        updatedAt: new Date(),
        updatedBy: auditInfo.updatedBy,
      })
      .where(eq(schema.smsBalance.tenantId, filter.tenantId))
      .returning();
    
    return result[0];
  }

  static async initializeSmsBalance(tenantId?: string, userId?: bigint) {
    const existingBalance = await this.getSmsBalance(tenantId);
    if (existingBalance) {
      return existingBalance;
    }

    return this.createSmsBalance({ balance: 0 }, tenantId, userId);
  }

  static async checkSufficientBalance(requiredAmount: number, tenantId?: string): Promise<boolean> {
    const balance = await this.getSmsBalance(tenantId);
    if (!balance) {
      return false;
    }
    return balance.balance >= requiredAmount;
  }
}
