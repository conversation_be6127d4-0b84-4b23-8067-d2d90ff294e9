import { db } from '../../src/db';
import * as schema from '../../src/db/schema';
import { eq, desc } from 'drizzle-orm';
import { TenantAwareDBBase } from './base';

export class TenantsDB extends TenantAwareDBBase {
  
  static async getTenants() {
    return db.select().from(schema.tenants)
      .orderBy(desc(schema.tenants.createdAt));
  }

  static async getTenantById(id: string) {
    const result = await db.select().from(schema.tenants)
      .where(eq(schema.tenants.id, id));
    
    return result[0] || null;
  }

  static async getTenantByName(name: string) {
    const result = await db.select().from(schema.tenants)
      .where(eq(schema.tenants.name, name));
    
    return result[0] || null;
  }

  static async createTenant(data: {
    id: string; // Zitadel organization ID
    name: string;
  }, userId: bigint) {
    const result = await db.insert(schema.tenants)
      .values({
        ...data,
        createdBy: userId,
        updatedBy: userId,
      })
      .returning();
    
    return result[0];
  }

  static async updateTenant(id: string, data: {
    name?: string;
  }, userId: bigint) {
    const result = await db.update(schema.tenants)
      .set({
        ...data,
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(schema.tenants.id, id))
      .returning();
    
    return result[0] || null;
  }

  static async deleteTenant(id: string) {
    const result = await db.delete(schema.tenants)
      .where(eq(schema.tenants.id, id))
      .returning();
    
    return result[0] || null;
  }

  static async tenantExists(id: string): Promise<boolean> {
    const result = await db.select({ id: schema.tenants.id })
      .from(schema.tenants)
      .where(eq(schema.tenants.id, id));
    
    return result.length > 0;
  }
}