import { eq, and, desc, gte, lte, count, sql } from 'drizzle-orm';
import { db } from '@/src/db';
import * as schema from '@/src/db/schema';

export class PasswordResetSmsLogsDB {
  
  static async createLog(data: {
    username: string;
    userId: string;
    receiver: string;
    message: string;
    verificationCode: string;
    expiresAt: Date;
    status?: 'pending' | 'sent' | 'failed';
  }) {
    const [result] = await db.insert(schema.passwordResetSmsLogs).values({
      username: data.username,
      userId: data.userId,
      receiver: data.receiver,
      message: data.message,
      verificationCode: data.verificationCode,
      expiresAt: data.expiresAt,
      status: data.status || 'pending',
    }).returning();
    
    return result;
  }

  static async updateLogStatus(
    id: string,
    status: 'sent' | 'failed',
    additionalData?: {
      sentAt?: Date;
      creditsUsed?: number;
      providerResponse?: string;
      errorMessage?: string;
    }
  ) {
    const updateData: any = {
      status,
      updatedAt: new Date(),
    };

    if (additionalData) {
      if (additionalData.sentAt) updateData.sentAt = additionalData.sentAt;
      if (additionalData.creditsUsed !== undefined) updateData.creditsUsed = additionalData.creditsUsed;
      if (additionalData.providerResponse) updateData.providerResponse = additionalData.providerResponse;
      if (additionalData.errorMessage) updateData.errorMessage = additionalData.errorMessage;
    }

    const [result] = await db
      .update(schema.passwordResetSmsLogs)
      .set(updateData)
      .where(eq(schema.passwordResetSmsLogs.id, id))
      .returning();
    
    return result;
  }

  static async markAsUsed(verificationCode: string, userId: string) {
    const [result] = await db
      .update(schema.passwordResetSmsLogs)
      .set({
        usedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(schema.passwordResetSmsLogs.verificationCode, verificationCode),
          eq(schema.passwordResetSmsLogs.userId, userId),
          eq(schema.passwordResetSmsLogs.status, 'sent')
        )
      )
      .returning();
    
    return result;
  }

  static async getLogById(id: string) {
    const [result] = await db
      .select()
      .from(schema.passwordResetSmsLogs)
      .where(eq(schema.passwordResetSmsLogs.id, id));
    
    return result || null;
  }

  static async getLogsByUserId(userId: string, limit: number = 10) {
    const results = await db
      .select()
      .from(schema.passwordResetSmsLogs)
      .where(eq(schema.passwordResetSmsLogs.userId, userId))
      .orderBy(desc(schema.passwordResetSmsLogs.createdAt))
      .limit(limit);
    
    return results;
  }

  static async getLogsByUsername(username: string, limit: number = 10) {
    const results = await db
      .select()
      .from(schema.passwordResetSmsLogs)
      .where(eq(schema.passwordResetSmsLogs.username, username))
      .orderBy(desc(schema.passwordResetSmsLogs.createdAt))
      .limit(limit);
    
    return results;
  }

  static async getRecentLogs(hours: number = 24) {
    const since = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    const results = await db
      .select()
      .from(schema.passwordResetSmsLogs)
      .where(gte(schema.passwordResetSmsLogs.createdAt, since))
      .orderBy(desc(schema.passwordResetSmsLogs.createdAt));
    
    return results;
  }

  static async getActiveVerificationCode(userId: string) {
    const now = new Date();
    
    const [result] = await db
      .select()
      .from(schema.passwordResetSmsLogs)
      .where(
        and(
          eq(schema.passwordResetSmsLogs.userId, userId),
          eq(schema.passwordResetSmsLogs.status, 'sent'),
          gte(schema.passwordResetSmsLogs.expiresAt, now),
          sql`${schema.passwordResetSmsLogs.usedAt} IS NULL`
        )
      )
      .orderBy(desc(schema.passwordResetSmsLogs.createdAt))
      .limit(1);
    
    return result || null;
  }

  static async getStats(days: number = 30) {
    const since = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    
    const results = await db
      .select({
        status: schema.passwordResetSmsLogs.status,
        count: count(),
        totalCredits: sql<number>`COALESCE(SUM(${schema.passwordResetSmsLogs.creditsUsed}), 0)`,
      })
      .from(schema.passwordResetSmsLogs)
      .where(gte(schema.passwordResetSmsLogs.createdAt, since))
      .groupBy(schema.passwordResetSmsLogs.status);
    
    const stats = {
      total: 0,
      sent: 0,
      failed: 0,
      pending: 0,
      totalCreditsUsed: 0,
    };
    
    results.forEach((row) => {
      stats.total += row.count;
      if (row.status === 'sent') {
        stats.sent = row.count;
        stats.totalCreditsUsed = row.totalCredits;
      } else if (row.status === 'failed') {
        stats.failed = row.count;
      } else if (row.status === 'pending') {
        stats.pending = row.count;
      }
    });
    
    return stats;
  }

  static async cleanupExpiredLogs(daysToKeep: number = 30) {
    const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);
    
    await db
      .delete(schema.passwordResetSmsLogs)
      .where(
        and(
          lte(schema.passwordResetSmsLogs.createdAt, cutoffDate),
          sql`${schema.passwordResetSmsLogs.usedAt} IS NOT NULL OR ${schema.passwordResetSmsLogs.expiresAt} < NOW()`
        )
      );
  }
}
