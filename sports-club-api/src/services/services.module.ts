import { Module } from '@nestjs/common';

// Import the existing services from the copied service layer
import { AthleteService } from '../lib/services/athlete-service';
import { SchoolService } from '../lib/services/school-service';
import { TeamService } from '../lib/services/team-service';
import { InstructorService } from '../lib/services/instructor-service';
import { PaymentService } from '../lib/services/payment-service';
import { PaymentTransactionService } from '../lib/services/payment-transaction-service';
import { ExpenseService } from '../lib/services/expense-service';
import { FacilityService } from '../lib/services/facility-service';
import { ItemService } from '../lib/services/item-service';
import { SmsService } from '../lib/services/sms-service';
import { BranchService } from '../lib/services/branch-service';
import { PaymentPlanService } from '../lib/services/payment-plan-service';

@Module({
  providers: [
    AthleteService,
    SchoolService,
    TeamService,
    InstructorService,
    PaymentService,
    PaymentTransactionService,
    ExpenseService,
    FacilityService,
    ItemService,
    SmsService,
    BranchService,
    PaymentPlanService,
  ],
  exports: [
    AthleteService,
    SchoolService,
    TeamService,
    InstructorService,
    PaymentService,
    PaymentTransactionService,
    ExpenseService,
    FacilityService,
    ItemService,
    SmsService,
    BranchService,
    PaymentPlanService,
  ],
})
export class ServicesModule {}
