import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, IsArray, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class CreateSchoolDto {
  @ApiProperty({ description: 'School name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Founded year', minimum: 1800, maximum: 2100 })
  @IsNumber()
  @Min(1800)
  @Max(2100)
  foundedYear: number;

  @ApiPropertyOptional({ description: 'School address' })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({ description: 'School phone number' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ description: 'School email address' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: 'School logo URL' })
  @IsOptional()
  @IsString()
  logo?: string;

  @ApiPropertyOptional({ description: 'Branch IDs associated with the school', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  branches?: string[];
}

export class UpdateSchoolDto {
  @ApiPropertyOptional({ description: 'School name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Founded year', minimum: 1800, maximum: 2100 })
  @IsOptional()
  @IsNumber()
  @Min(1800)
  @Max(2100)
  foundedYear?: number;

  @ApiPropertyOptional({ description: 'School address' })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({ description: 'School phone number' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ description: 'School email address' })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({ description: 'School logo URL' })
  @IsOptional()
  @IsString()
  logo?: string;

  @ApiPropertyOptional({ description: 'Branch IDs associated with the school', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  branches?: string[];
}
