import { IsS<PERSON>, <PERSON><PERSON><PERSON>al, IsE<PERSON>, IsDateString, IsArray, IsBoolean, IsNumber, Min, Max, <PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type, Transform } from 'class-transformer';

export class CreateAthleteDto {
  @ApiProperty({ description: 'Athlete first name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Athlete last name' })
  @IsString()
  surname: string;

  @ApiProperty({ description: 'National ID number' })
  @IsString()
  nationalId: string;

  @ApiProperty({ description: 'Birth date in YYYY-MM-DD format' })
  @IsDateString()
  birthDate: string;

  @ApiPropertyOptional({ description: 'Registration date in YYYY-MM-DD format' })
  @IsOptional()
  @IsDateString()
  registrationDate?: string;

  @ApiProperty({ description: 'Parent first name' })
  @IsString()
  parentName: string;

  @ApiProperty({ description: 'Parent last name' })
  @IsString()
  parentSurname: string;

  @ApiProperty({ description: 'Parent phone number' })
  @IsString()
  parentPhone: string;

  @ApiPropertyOptional({ description: 'Parent email address' })
  @IsOptional()
  @IsEmail()
  parentEmail?: string;

  @ApiPropertyOptional({ description: 'Parent address' })
  @IsOptional()
  @IsString()
  parentAddress?: string;

  @ApiPropertyOptional({ description: 'Payment plan ID' })
  @IsOptional()
  @IsString()
  paymentPlanId?: string;
}

export class UpdateAthleteDto {
  @ApiPropertyOptional({ description: 'Athlete first name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Athlete last name' })
  @IsOptional()
  @IsString()
  surname?: string;

  @ApiPropertyOptional({ description: 'National ID number' })
  @IsOptional()
  @IsString()
  nationalId?: string;

  @ApiPropertyOptional({ description: 'Birth date in YYYY-MM-DD format' })
  @IsOptional()
  @IsDateString()
  birthDate?: string;

  @ApiPropertyOptional({ description: 'Registration date in YYYY-MM-DD format' })
  @IsOptional()
  @IsDateString()
  registrationDate?: string;

  @ApiPropertyOptional({ description: 'Parent first name' })
  @IsOptional()
  @IsString()
  parentName?: string;

  @ApiPropertyOptional({ description: 'Parent last name' })
  @IsOptional()
  @IsString()
  parentSurname?: string;

  @ApiPropertyOptional({ description: 'Parent phone number' })
  @IsOptional()
  @IsString()
  parentPhone?: string;

  @ApiPropertyOptional({ description: 'Parent email address' })
  @IsOptional()
  @IsEmail()
  parentEmail?: string;

  @ApiPropertyOptional({ description: 'Parent address' })
  @IsOptional()
  @IsString()
  parentAddress?: string;
}

export class TeamAssignmentDto {
  @ApiProperty({ description: 'Team ID' })
  @IsString()
  teamId: string;

  @ApiPropertyOptional({ description: 'Payment plan ID' })
  @IsOptional()
  @IsString()
  paymentPlanId?: string;

  @ApiPropertyOptional({ description: 'Use prorated payment calculation' })
  @IsOptional()
  @IsBoolean()
  useProrated?: boolean;
}

export class CreateAthleteWithTeamAssignmentsDto extends CreateAthleteDto {
  @ApiPropertyOptional({ description: 'Team assignments', type: [TeamAssignmentDto] })
  @IsOptional()
  @IsArray()
  @Type(() => TeamAssignmentDto)
  teamAssignments?: TeamAssignmentDto[];
}

export class CreateAthleteWithBalanceDto extends CreateAthleteWithTeamAssignmentsDto {
  @ApiPropertyOptional({ description: 'Initial balance amount' })
  @IsOptional()
  @IsString()
  initialBalance?: string;

  @ApiPropertyOptional({ description: 'Use prorated payment calculation' })
  @IsOptional()
  @IsBoolean()
  useProrated?: boolean;
}

export class ActivateAthleteDto {
  @ApiPropertyOptional({ description: 'Team assignments for activation', type: [TeamAssignmentDto] })
  @IsOptional()
  @IsArray()
  @Type(() => TeamAssignmentDto)
  teamAssignments?: TeamAssignmentDto[];
}

export class GetAthletesPaginatedDto {
  @ApiPropertyOptional({ description: 'Page number', minimum: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Items per page', minimum: 1, maximum: 100 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Search term' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: 'Sort field' })
  @IsOptional()
  @IsString()
  sortBy?: string;

  @ApiPropertyOptional({ description: 'Sort order', enum: ['asc', 'desc'] })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc';

  @ApiPropertyOptional({ description: 'Filter by name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Filter by surname' })
  @IsOptional()
  @IsString()
  surname?: string;

  @ApiPropertyOptional({ description: 'Filter by parent email' })
  @IsOptional()
  @IsString()
  parentEmail?: string;

  @ApiPropertyOptional({ description: 'Filter by parent phone' })
  @IsOptional()
  @IsString()
  parentPhone?: string;

  @ApiPropertyOptional({ description: 'Filter by national ID' })
  @IsOptional()
  @IsString()
  nationalId?: string;

  @ApiPropertyOptional({ description: 'Filter by status', enum: ['active', 'inactive', 'suspended'] })
  @IsOptional()
  @IsEnum(['active', 'inactive', 'suspended'])
  status?: 'active' | 'inactive' | 'suspended';
}
