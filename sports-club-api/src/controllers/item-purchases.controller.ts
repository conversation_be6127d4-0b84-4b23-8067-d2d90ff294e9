import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { User } from '../decorators/user.decorator';
import { AuthenticatedUser } from '../auth/jwt.strategy';

@ApiTags('item-purchases')
@ApiBearerAuth()
@Controller('item-purchases')
@UseGuards(JwtAuthGuard)
export class ItemPurchasesController {
  @Get()
  async getItemPurchases(@User() user: AuthenticatedUser) {
    return { success: true, data: [] };
  }

  @Get(':id')
  async getItemPurchaseById(@Param('id') id: string, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Post()
  async createItemPurchase(@Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Put(':id')
  async updateItemPurchase(@Param('id') id: string, @Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Delete(':id')
  async deleteItemPurchase(@Param('id') id: string, @User() user: AuthenticatedUser) {
    return { success: true };
  }
}
