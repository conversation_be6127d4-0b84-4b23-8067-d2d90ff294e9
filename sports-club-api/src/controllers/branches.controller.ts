import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { User } from '../decorators/user.decorator';
import { AuthenticatedUser } from '../auth/jwt.strategy';

@ApiTags('branches')
@ApiBearerAuth()
@Controller('branches')
@UseGuards(JwtAuthGuard)
export class BranchesController {
  @Get()
  async getBranches(@User() user: AuthenticatedUser) {
    return { success: true, data: [] };
  }

  @Get(':id')
  async getBranchById(@Param('id') id: string, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Post()
  async createBranch(@Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Put(':id')
  async updateBranch(@Param('id') id: string, @Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Delete(':id')
  async deleteBranch(@Param('id') id: string, @User() user: AuthenticatedUser) {
    return { success: true };
  }
}
