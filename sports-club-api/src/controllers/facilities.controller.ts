import { Controller, Get, Post, Put, Delete, Body, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { User } from '../decorators/user.decorator';
import { AuthenticatedUser } from '../auth/jwt.strategy';

@ApiTags('facilities')
@ApiBearerAuth()
@Controller('facilities')
@UseGuards(JwtAuthGuard)
export class FacilitiesController {
  @Get()
  async getFacilities(@User() user: AuthenticatedUser) {
    return { success: true, data: [] };
  }

  @Get(':id')
  async getFacilityById(@Param('id') id: string, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Post()
  async createFacility(@Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Put(':id')
  async updateFacility(@Param('id') id: string, @Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Delete(':id')
  async deleteFacility(@Param('id') id: string, @User() user: AuthenticatedUser) {
    return { success: true };
  }
}
