import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { User } from '../decorators/user.decorator';
import { AuthenticatedUser } from '../auth/jwt.strategy';

@ApiTags('expenses')
@ApiBearerAuth()
@Controller('expenses')
@UseGuards(JwtAuthGuard)
export class ExpensesController {
  @Get()
  async getExpenses(@User() user: AuthenticatedUser) {
    return { success: true, data: [] };
  }

  @Get(':id')
  async getExpenseById(@Param('id') id: string, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Post()
  async createExpense(@Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Put(':id')
  async updateExpense(@Param('id') id: string, @Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Delete(':id')
  async deleteExpense(@Param('id') id: string, @User() user: AuthenticatedUser) {
    return { success: true };
  }
}
