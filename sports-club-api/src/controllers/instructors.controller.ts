import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { User } from '../decorators/user.decorator';
import { AuthenticatedUser } from '../auth/jwt.strategy';

@ApiTags('instructors')
@ApiBearerAuth()
@Controller('instructors')
@UseGuards(JwtAuthGuard)
export class InstructorsController {
  @Get()
  async getInstructors(@User() user: AuthenticatedUser) {
    return { success: true, data: [] };
  }

  @Get('paginated')
  async getInstructorsPaginated(@Query() query: any, @User() user: AuthenticatedUser) {
    return { success: true, data: { items: [], total: 0, page: 1, limit: 10 } };
  }

  @Get(':id')
  async getInstructorById(@Param('id') id: string, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Post()
  async createInstructor(@Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Put(':id')
  async updateInstructor(@Param('id') id: string, @Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Delete(':id')
  async deleteInstructor(@Param('id') id: string, @User() user: AuthenticatedUser) {
    return { success: true };
  }
}
