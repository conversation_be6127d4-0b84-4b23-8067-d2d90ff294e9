import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { User } from '../decorators/user.decorator';
import { AuthenticatedUser } from '../auth/jwt.strategy';

@ApiTags('payment-transactions')
@ApiBearerAuth()
@Controller('payment-transactions')
@UseGuards(JwtAuthGuard)
export class PaymentTransactionsController {
  @Get()
  async getPaymentTransactions(@User() user: AuthenticatedUser) {
    return { success: true, data: [] };
  }

  @Get(':id')
  async getPaymentTransactionById(@Param('id') id: string, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Post()
  async createPaymentTransaction(@Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Put(':id')
  async updatePaymentTransaction(@Param('id') id: string, @Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Delete(':id')
  async deletePaymentTransaction(@Param('id') id: string, @User() user: AuthenticatedUser) {
    return { success: true };
  }
}
