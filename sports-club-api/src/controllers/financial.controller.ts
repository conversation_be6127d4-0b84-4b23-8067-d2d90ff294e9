import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { User } from '../decorators/user.decorator';
import { AuthenticatedUser } from '../auth/jwt.strategy';

@ApiTags('financial')
@ApiBearerAuth()
@Controller('financial')
@UseGuards(JwtAuthGuard)
export class FinancialController {
  @Get('dashboard')
  async getFinancialDashboard(@User() user: AuthenticatedUser) {
    return { success: true, data: { income: 0, expenses: 0, balance: 0 } };
  }

  @Get('reports')
  async getFinancialReports(@Query() query: any, @User() user: AuthenticatedUser) {
    return { success: true, data: [] };
  }

  @Get('income')
  async getIncome(@Query() query: any, @User() user: AuthenticatedUser) {
    return { success: true, data: [] };
  }

  @Get('expenses')
  async getExpenses(@Query() query: any, @User() user: AuthenticatedUser) {
    return { success: true, data: [] };
  }
}
