import { Module } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { AuthModule } from '../auth/auth.module';
import { ServicesModule } from '../services/services.module';

// Import all controllers
import { AthletesController } from './athletes.controller';
import { SchoolsController } from './schools.controller';
import { TeamsController } from './teams.controller';
import { InstructorsController } from './instructors.controller';
import { PaymentsController } from './payments.controller';
import { PaymentTransactionsController } from './payment-transactions.controller';
import { ExpensesController } from './expenses.controller';
import { FacilitiesController } from './facilities.controller';
import { ItemsController } from './items.controller';
import { ItemPurchasesController } from './item-purchases.controller';
import { PaymentPlansController } from './payment-plans.controller';
import { BranchesController } from './branches.controller';
import { SmsController } from './sms.controller';
import { TenantsController } from './tenants.controller';
import { FinancialController } from './financial.controller';

@Module({
  imports: [AuthModule, ServicesModule],
  controllers: [
    AthletesController,
    SchoolsController,
    TeamsController,
    InstructorsController,
    PaymentsController,
    PaymentTransactionsController,
    ExpensesController,
    FacilitiesController,
    ItemsController,
    ItemPurchasesController,
    PaymentPlansController,
    BranchesController,
    SmsController,
    TenantsController,
    FinancialController,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
  ],
})
export class ControllersModule {}
