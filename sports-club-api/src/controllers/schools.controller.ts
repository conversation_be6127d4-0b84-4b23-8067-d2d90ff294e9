import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { User } from '../decorators/user.decorator';
import { AuthenticatedUser } from '../auth/jwt.strategy';
import { CreateSchoolDto, UpdateSchoolDto } from '../dto/schools.dto';

@ApiTags('schools')
@ApiBearerAuth()
@Controller('schools')
@UseGuards(JwtAuthGuard)
export class SchoolsController {
  constructor(
    // We'll inject the service layer here
    // private readonly schoolService: SchoolService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all schools' })
  @ApiResponse({ status: 200, description: 'Schools retrieved successfully' })
  async getSchools(@User() user: AuthenticatedUser) {
    try {
      // This will call the existing service layer
      // const result = await this.schoolService.getSchools(undefined, user.tenantId);
      
      return { success: true, data: [] };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to get schools',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get school by ID' })
  @ApiResponse({ status: 200, description: 'School retrieved successfully' })
  @ApiResponse({ status: 404, description: 'School not found' })
  async getSchoolById(
    @Param('id') id: string,
    @User() user: AuthenticatedUser,
  ) {
    try {
      // This will call the existing service layer
      // const result = await this.schoolService.getSchoolById(id, undefined, user.tenantId);
      
      return { success: true, data: null };
    } catch (error) {
      throw new HttpException(
        error.message || `Failed to get school with ID ${id}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post()
  @ApiOperation({ summary: 'Create new school' })
  @ApiResponse({ status: 201, description: 'School created successfully' })
  async createSchool(
    @Body() createSchoolDto: CreateSchoolDto,
    @User() user: AuthenticatedUser,
  ) {
    try {
      // This will call the existing service layer
      // const result = await this.schoolService.createSchoolWithBranches(
      //   createSchoolDto,
      //   undefined,
      //   user.tenantId
      // );
      
      return { success: true, data: null };
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to create school',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update school' })
  @ApiResponse({ status: 200, description: 'School updated successfully' })
  @ApiResponse({ status: 404, description: 'School not found' })
  async updateSchool(
    @Param('id') id: string,
    @Body() updateSchoolDto: UpdateSchoolDto,
    @User() user: AuthenticatedUser,
  ) {
    try {
      // This will call the existing service layer
      // const result = await this.schoolService.updateSchoolWithBranches(
      //   id,
      //   updateSchoolDto,
      //   undefined,
      //   user.tenantId
      // );
      
      return { success: true, data: null };
    } catch (error) {
      throw new HttpException(
        error.message || `Failed to update school with ID ${id}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete school' })
  @ApiResponse({ status: 200, description: 'School deleted successfully' })
  @ApiResponse({ status: 404, description: 'School not found' })
  @ApiResponse({ status: 400, description: 'Cannot delete school with existing teams' })
  async deleteSchool(
    @Param('id') id: string,
    @User() user: AuthenticatedUser,
  ) {
    try {
      // This will call the existing service layer
      // const result = await this.schoolService.deleteSchool(id, undefined, user.tenantId);
      
      return { success: true };
    } catch (error) {
      // Handle business rule errors (like school has teams)
      if (error.message?.includes('has_teams')) {
        throw new HttpException(
          'Cannot delete school with existing teams',
          HttpStatus.BAD_REQUEST,
        );
      }
      
      throw new HttpException(
        error.message || `Failed to delete school with ID ${id}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':id/branches')
  @ApiOperation({ summary: 'Assign branches to school' })
  @ApiResponse({ status: 200, description: 'Branches assigned successfully' })
  async assignBranchesToSchool(
    @Param('id') id: string,
    @Body() body: { branchIds: string[] },
    @User() user: AuthenticatedUser,
  ) {
    try {
      // This will call the existing service layer
      // const result = await this.schoolService.assignBranchesToSchool(
      //   id,
      //   body.branchIds,
      //   undefined,
      //   user.tenantId
      // );
      
      return { success: true };
    } catch (error) {
      throw new HttpException(
        error.message || `Failed to assign branches to school with ID ${id}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
