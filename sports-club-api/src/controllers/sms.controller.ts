import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { User } from '../decorators/user.decorator';
import { AuthenticatedUser } from '../auth/jwt.strategy';

@ApiTags('sms')
@ApiBearerAuth()
@Controller('sms')
@UseGuards(JwtAuthGuard)
export class SmsController {
  @Get('configuration')
  async getSmsConfiguration(@User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Post('configuration')
  async createSmsConfiguration(@Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Put('configuration/:id')
  async updateSmsConfiguration(@Param('id') id: string, @Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Post('configuration/:id/activate')
  async activateSmsConfiguration(@Param('id') id: string, @User() user: AuthenticatedUser) {
    return { success: true };
  }

  @Post('send/payment-reminder')
  async sendPaymentReminderSms(@Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Post('send/template')
  async sendTemplateSms(@Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }

  @Get('logs')
  async getSmsLogs(@Query() query: any, @User() user: AuthenticatedUser) {
    return { success: true, data: [] };
  }

  @Get('balance')
  async getSmsBalance(@User() user: AuthenticatedUser) {
    return { success: true, data: { balance: 0 } };
  }

  @Post('balance/add')
  async addSmsBalance(@Body() body: any, @User() user: AuthenticatedUser) {
    return { success: true, data: null };
  }
}
