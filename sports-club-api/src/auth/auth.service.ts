import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

@Injectable()
export class AuthService {
  constructor(private configService: ConfigService) {}

  async validateZitadelToken(token: string): Promise<any> {
    try {
      const issuer = this.configService.get<string>('ZITADEL_ISSUER');
      
      // Verify token with Zitadel introspection endpoint
      const response = await axios.post(
        `${issuer}/oauth/v2/introspect`,
        new URLSearchParams({
          token,
          client_id: this.configService.get<string>('ZITADEL_CLIENT_ID'),
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Bearer ${this.configService.get<string>('ZITADEL_PAT_TOKEN')}`,
          },
        }
      );

      if (!response.data.active) {
        throw new UnauthorizedException('Token is not active');
      }

      return response.data;
    } catch (error) {
      throw new UnauthorizedException('Token validation failed');
    }
  }

  async getUserInfo(token: string): Promise<any> {
    try {
      const issuer = this.configService.get<string>('ZITADEL_ISSUER');
      
      const response = await axios.get(`${issuer}/oidc/v1/userinfo`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      return response.data;
    } catch (error) {
      throw new UnauthorizedException('Failed to get user info');
    }
  }
}
