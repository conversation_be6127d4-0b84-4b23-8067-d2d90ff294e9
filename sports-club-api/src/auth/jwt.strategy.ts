import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

export interface JwtPayload {
  sub: string;
  email: string;
  name: string;
  'urn:zitadel:iam:org:project:roles'?: Record<string, any>;
  iat: number;
  exp: number;
  aud: string[];
  iss: string;
}

export interface AuthenticatedUser {
  userId: string;
  email: string;
  name: string;
  tenantId: string;
  roles?: Record<string, any>;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKeyProvider: async (request, rawJwtToken, done) => {
        try {
          // Get JWKS from Zitadel to verify token signature
          const issuer = this.configService.get<string>('ZITADEL_ISSUER');
          const jwksResponse = await axios.get(`${issuer}/.well-known/openid-configuration`);
          const jwksUri = jwksResponse.data.jwks_uri;
          
          // For simplicity, we'll use the JWT_SECRET for now
          // In production, you should implement proper JWKS verification
          const secret = this.configService.get<string>('JWT_SECRET');
          done(null, secret);
        } catch (error) {
          done(error, null);
        }
      },
      algorithms: ['RS256', 'HS256'],
    });
  }

  async validate(payload: JwtPayload): Promise<AuthenticatedUser> {
    try {
      // Verify the token is from the correct issuer
      const expectedIssuer = this.configService.get<string>('ZITADEL_ISSUER');
      if (payload.iss !== expectedIssuer) {
        throw new UnauthorizedException('Invalid token issuer');
      }

      // Extract tenant ID from the user ID or roles
      // This logic should match your existing tenant extraction logic
      const tenantId = this.extractTenantId(payload);
      
      if (!tenantId) {
        throw new UnauthorizedException('No tenant ID found in token');
      }

      return {
        userId: payload.sub,
        email: payload.email,
        name: payload.name,
        tenantId,
        roles: payload['urn:zitadel:iam:org:project:roles'],
      };
    } catch (error) {
      throw new UnauthorizedException('Token validation failed');
    }
  }

  private extractTenantId(payload: JwtPayload): string | null {
    // Extract tenant ID from user ID (assuming format like "tenant_id:user_id")
    // This should match the logic from your existing middleware-tenant-utils.ts
    const userId = payload.sub;
    
    if (userId && userId.includes(':')) {
      return userId.split(':')[0];
    }
    
    // Fallback: try to extract from roles or other claims
    const roles = payload['urn:zitadel:iam:org:project:roles'];
    if (roles) {
      // Look for tenant information in roles
      for (const [key, value] of Object.entries(roles)) {
        if (key.includes('tenant') || key.includes('org')) {
          // Extract tenant ID from role key or value
          const match = key.match(/tenant[_-]?(\w+)/i);
          if (match) {
            return match[1];
          }
        }
      }
    }
    
    return null;
  }
}
