{"name": "sports-club-api", "version": "1.0.0", "description": "Sports Club Management API built with NestJS", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.15.0", "dependencies": {"@nestjs/common": "^11.1.6", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.6", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.6", "@nestjs/swagger": "^11.2.0", "@netgsm/sms": "^1.1.10", "@types/bcryptjs": "^3.0.0", "@types/express": "^5.0.3", "@types/multer": "^2.0.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^16.5.0", "drizzle-kit": "^0.31.1", "drizzle-orm": "^0.43.1", "exceljs": "^4.4.0", "express": "^5.1.0", "multer": "^2.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@nestjs/cli": "^11.0.10", "@nestjs/schematics": "^11.0.7", "@nestjs/testing": "^11.1.6", "@types/node": "20.6.2", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/pg": "^8.15.2", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "5.2.2"}}