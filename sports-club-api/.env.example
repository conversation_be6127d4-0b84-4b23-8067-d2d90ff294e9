# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/sports_club_db

# Zitadel Configuration
ZITADEL_ISSUER=https://your-zitadel-instance.com
ZITADEL_CLIENT_ID=your-client-id
ZITADEL_PAT_TOKEN=your-pat-token

# JWT Configuration
JWT_SECRET=your-jwt-secret-key

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Logging
LOG_LEVEL=info
