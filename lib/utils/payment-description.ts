/**
 * Formats payment descriptions by replacing common keywords with their translated versions
 * This function handles both translation keys and hardcoded English descriptions
 */
export function formatPaymentDescription(description: string | null | undefined, t: any): string {
  if (!description) return "-";
  
  // Check if the description is a translation key
  if (description.startsWith('descriptions.')) {
    const translationKey = `payments.${description}`;
    
    // Handle different types of description keys
    if (description === 'descriptions.initialBalance') {
      return t('payments.descriptions.initialBalance');
    }
    
    if (description === 'descriptions.initialBalanceFromImport') {
      return t('payments.descriptions.initialBalanceFromImport');
    }
    
    if (description === 'descriptions.proratedBalanceGeneric') {
      return t('payments.descriptions.proratedBalanceGeneric');
    }
    
    if (description.startsWith('descriptions.proratedBalance')) {
      // For prorated balance, if we only have the key without plan name,
      // use a simpler description that doesn't require plan name interpolation
      return t('payments.descriptions.proratedBalanceGeneric');
    }
    
    // Fallback: try to translate the key directly
    const translated = t(translationKey);
    if (translated !== translationKey) {
      return translated;
    }
  }
  
  // Check for other common patterns and handle them
  if (description.includes('remaining days of the month')) {
    // This might be an already-interpolated prorated balance description
    // Try to detect the plan name pattern
    const match = description.match(/Prorated balance for (.+) \(remaining days of the month\)/);
    if (match) {
      const planName = match[1];
      return t('payments.descriptions.proratedBalance', { planName });
    }
  }
  
  // Handle hardcoded English descriptions
  if (description === 'Initial balance') {
    return t('payments.descriptions.initialBalance');
  }
  
  if (description === 'Initial balance from import') {
    return t('payments.descriptions.initialBalanceFromImport');
  }
  
  // Return the original description if no translation pattern matches
  return description;
}
