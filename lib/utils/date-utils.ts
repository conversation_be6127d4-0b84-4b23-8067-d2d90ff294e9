/**
 * Add days to a date
 * @param date - The base date
 * @param days - Number of days to add
 * @returns Date string in YYYY-MM-DD format
 */
export function addDaysToDate(date: Date, days: number): string {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result.toISOString().split('T')[0];
}

/**
 * Get due date for a payment (10 days from billing date)
 * @param billingDate - The billing date (defaults to today)
 * @returns Due date string in YYYY-MM-DD format
 */
export function getPaymentDueDate(billingDate?: Date): string {
  const baseDate = billingDate || new Date();
  return addDaysToDate(baseDate, 10);
}
