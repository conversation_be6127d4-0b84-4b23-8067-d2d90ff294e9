import fs from 'fs';
import path from 'path';

interface TranslationCache {
  [locale: string]: {
    [namespace: string]: any;
  };
}

const translationCache: TranslationCache = {};

/**
 * Load translation file for server-side use
 */
function loadTranslationFile(locale: string, namespace: string): any {
  const cacheKey = `${locale}-${namespace}`;

  if (translationCache[locale]?.[namespace]) {
    return translationCache[locale][namespace];
  }

  try {
    const filePath = path.join(process.cwd(), 'public', 'locales', locale, `${namespace}.json`);
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const translations = JSON.parse(fileContent);

    // Initialize cache structure if needed
    if (!translationCache[locale]) {
      translationCache[locale] = {};
    }

    translationCache[locale][namespace] = translations;
    return translations;
  } catch (error) {
    console.warn(`Failed to load translation file: ${locale}/${namespace}.json`, error);
    return {};
  }
}

function getNestedProperty(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * Get translated text on server-side
 * @param key - Translation key (supports nested keys with dot notation)
 * @param locale - Locale (defaults to 'en')
 * @param namespace - Translation namespace (defaults to 'shared')
 * @param interpolation - Variables for interpolation
 */
export function getServerTranslation(
  key: string,
  locale: string = 'en',
  namespace: string = 'shared',
  interpolation?: Record<string, string | number>
): string {
  const translations = loadTranslationFile(locale, namespace);

  // If the loaded translations have a root key matching the namespace, use that as the base
  let translationRoot = translations;
  if (namespace !== 'shared' && translations[namespace]) {
    translationRoot = translations[namespace];
  }

  // Handle nested keys (e.g., 'descriptions.initialBalance')
  const keys = key.split('.');
  let value = translationRoot;

  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // Fallback to English if key not found
      if (locale !== 'en') {
        return getServerTranslation(key, 'en', namespace, interpolation);
      }
      return key; // Return key if not found
    }
  }

  let result = typeof value === 'string' ? value : key;

  // Handle interpolation
  if (interpolation && typeof result === 'string') {
    Object.entries(interpolation).forEach(([placeholder, replacement]) => {
      result = result.replace(new RegExp(`{{${placeholder}}}`, 'g'), String(replacement));
    });
  }

  return result;
}

/**
 * Convenience function for payment-related translations
 */
export function getPaymentTranslation(
  key: string,
  locale: string = 'en',
  interpolation?: Record<string, string | number>
): string {
  return getServerTranslation(key, locale, 'payments', interpolation);
}

/**
 * Legacy function for backwards compatibility - maps to getServerTranslation with auto-namespace detection
 */
export function serverTranslate(locale: string, key: string, options: Record<string, any> = {}): string {
  let namespace = 'shared';
  if (key.startsWith('athletes.')) namespace = 'athletes';
  else if (key.startsWith('payments.')) namespace = 'payments';

  return getServerTranslation(key, locale, namespace, options);
}
