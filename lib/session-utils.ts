// ========================================
// CLIENT-SAFE SESSION UTILITIES
// (Can be used in both client and server components)
// ========================================

/**
 * Extract tenant ID from NextAuth session (client-safe version)
 * @param session - The NextAuth session
 * @returns The tenant ID or null if not found
 */
export function extractTenantIdFromSession(session: any): string | null {
  if (!session) return null;
  
  // Check if tenant ID is already stored in session
  if (session.tenantId) {
    return session.tenantId;
  }
  
  // Check if it's in the user object
  if (session.user?.tenantId) {
    return session.user.tenantId;
  }
  
  // Check if it's in the token object  
  if (session.token?.tenantId) {
    return session.token.tenantId;
  }
  
  return null;
}

/**
 * Extract user ID from NextAuth session (client-safe version)
 * @param session - The NextAuth session
 * @returns The user ID as bigint or null if not found
 */
export function extractUserIdFromSession(session: any): bigint | null {
  if (!session) return null;
  
  // Check if user ID is already stored in session
  if (session.userId) {
    try {
      return BigInt(session.userId);
    } catch (error) {
      console.error('Error converting session user ID to bigint:', { error });
    }
  }
  
  return null;
}