/**
 * Utility functions for calculating prorated payment amounts
 */

/**
 * Calculate prorated amount for remaining days in the current month
 * @param monthlyAmount - The full monthly payment amount
 * @param startDate - The date to start proration from (defaults to today)
 * @returns The prorated amount for remaining days
 */
export function calculateProratedAmount(
  monthlyAmount: number,
  startDate: Date = new Date()
): number {
  const currentDate = new Date(startDate);
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  
  // Get total days in the current month
  const totalDaysInMonth = new Date(year, month + 1, 0).getDate();
  
  // Get remaining days (including today)
  const currentDay = currentDate.getDate();
  const remainingDays = totalDaysInMonth - currentDay + 1;
  
  // Calculate daily rate and multiply by remaining days
  const dailyRate = monthlyAmount / totalDaysInMonth;
  const proratedAmount = dailyRate * remainingDays;
  
  return Math.round(proratedAmount * 100) / 100; // Round to 2 decimal places
}

/**
 * Calculate prorated amount based on payment plan billing cycle
 * Formula: (total_amount / (next_billing_date - last_billing_date)) * (next_billing_date - current_date)
 * @param monthlyAmount - The full monthly payment amount
 * @param assignDay - Day of month for billing cycle (1-31)
 * @param currentDate - The current date (defaults to today)
 * @returns The prorated amount based on billing cycle
 */
export function calculateProratedAmountForBillingCycle(
  monthlyAmount: number,
  assignDay: number,
  currentDate: Date = new Date()
): number {
  const today = new Date(currentDate);
  const year = today.getFullYear();
  const month = today.getMonth();
  
  // Calculate last billing date (assign day of current/previous month)
  let lastBillingDate = new Date(year, month, assignDay);
  if (lastBillingDate > today) {
    // If assign day hasn't occurred this month, use previous month
    lastBillingDate = new Date(year, month - 1, assignDay);
  }
  
  // Calculate next billing date (assign day of current/next month)
  let nextBillingDate = new Date(year, month, assignDay);
  if (nextBillingDate <= today) {
    // If assign day has passed or is today, use next month
    nextBillingDate = new Date(year, month + 1, assignDay);
  }
  
  // Calculate total billing cycle days
  const totalBillingDays = Math.ceil((nextBillingDate.getTime() - lastBillingDate.getTime()) / (1000 * 60 * 60 * 24));
  
  // Calculate remaining days from current date to next billing date
  const remainingDays = Math.ceil((nextBillingDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
  
  // Apply the formula: (total_amount / (next_billing_date - last_billing_date)) * (next_billing_date - current_date)
  const dailyRate = monthlyAmount / totalBillingDays;
  const proratedAmount = dailyRate * remainingDays;
  
  return Math.round(proratedAmount * 100) / 100; // Round to 2 decimal places
}

/**
 * Get the number of remaining days in the current month (including today)
 * @param startDate - The date to calculate from (defaults to today)
 * @returns Number of remaining days
 */
export function getRemainingDaysInMonth(startDate: Date = new Date()): number {
  const currentDate = new Date(startDate);
  const year = currentDate.getFullYear();
  const month = currentDate.getMonth();
  
  // Get total days in the current month
  const totalDaysInMonth = new Date(year, month + 1, 0).getDate();
  
  // Get remaining days (including today)
  const currentDay = currentDate.getDate();
  const remainingDays = totalDaysInMonth - currentDay + 1;
  
  return remainingDays;
}

/**
 * Get total days in the current month
 * @param date - The date to get the month from (defaults to today)
 * @returns Total days in the month
 */
export function getTotalDaysInMonth(date: Date = new Date()): number {
  const year = date.getFullYear();
  const month = date.getMonth();
  return new Date(year, month + 1, 0).getDate();
}
