// Base types
export interface Branch {
  id: string | null;
  name: string | null;
  description?: string | null;
}

export interface School {
  id: string;
  name: string;
  branches: Branch[];
  foundedYear: number;
  logo?: string | null;
  address?: string | null;
  phone?: string | null;
  email?: string | null;
  instructors: string[];
  createdAt: Date;
  updatedAt: Date;
  createdBy?: bigint;
  updatedBy?: bigint;
}

export interface Instructor {
  id: string;
  name: string;
  surname: string;
  email: string;
  phone: string;
  nationalId?: string | null;
  birthDate?: string | null;
  address?: string | null;
  salary?: string | null;
  branches: Branch[];
  schools: string[];
  createdAt: Date;
  updatedAt: Date;
  createdBy?: bigint;
  updatedBy?: bigint;
}

// Simplified interfaces for nested objects in relations
export interface TeamBranch {
  id: string;
  name: string;
  description?: string | null;
}

export interface TeamSchool {
  id: string;
  name: string;
}

export interface TeamInstructor {
  id: string;
  name: string;
  surname: string;
}

export interface Team {
  id: string;
  name: string;
  description?: string | null;
  schoolId: string;
  branchId: string;
  instructorId: string;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
  createdBy: bigint;
  updatedBy: bigint;
  // Relations
  branch?: TeamBranch;
  school?: TeamSchool;
  instructor?: TeamInstructor;
  trainingSchedule?: TrainingSchedule[];
  athletes?: string[];
}

export interface TrainingSchedule {
  id: string;
  dayOfWeek: number;
  startTime: string;
  endTime: string;
  facilityId: string;
  facility?: {
    id: string;
    name: string;
  } | null;
}

export interface AthleteTeamDetail {
  teamId: string;
  teamName: string;
  branchName: string;
  branchId: string;
  schoolName: string;
  schoolId: string;
  instructorName: string;
  instructorSurname: string;
  joinedAt: string;
  leftAt?: string | null;
}

export interface Athlete {
  id: string;
  name: string;
  surname: string;
  nationalId: string;
  birthDate: string; // date string from database
  registrationDate: string; // date string from database
  status: 'active' | 'inactive' | 'suspended';
  balance: string; // decimal from database
  tenantId: string;
  // Parent information
  parentName: string;
  parentSurname: string;
  parentPhone: string;
  parentEmail?: string | null;
  parentAddress?: string | null;
  // Audit trail
  createdAt: Date;
  updatedAt: Date;
  createdBy: bigint;
  updatedBy: bigint;
  // Relations
  teams?: string[];
  teamDetails?: AthleteTeamDetail[];
}

export interface PaymentPlan {
  id: string;
  name: string;
  monthlyValue: string; // decimal from database comes as string
  assignDay: number; // Day of month when payment is assigned (1-31)
  dueDay: number; // Day of month when payment is due (1-31)
  status: 'active' | 'inactive';
  description?: string | null;
  createdAt: Date;
  updatedAt: Date;
  createdBy: bigint;
  updatedBy: bigint;
  // Legacy fields for backward compatibility during migration period
  totalAmount?: string;
  installmentAmount?: string;
  repeatCount?: number;
  frequency?: 'monthly' | 'quarterly';
  startDate?: string;
  endDate?: string;
  amount?: string; // Will be mapped to monthlyValue in forms
  branches?: Branch[];
}

export interface Payment {
  id: string;
  athleteId: string;
  athletePaymentPlanId?: string | null;
  itemPurchaseId?: string | null;
  amount: string; // decimal from database comes as string - total amount due
  amountPaid: string; // decimal from database comes as string - amount paid so far
  date: string; // date from database comes as string - payment assigned/created date
  dueDate: string; // date from database comes as string - when payment is due
  status: 'pending' | 'completed' | 'overdue' | 'cancelled' | 'partially_paid';
  type: 'fee' | 'equipment' | 'other';
  description?: string | null;
  createdAt: Date;
  updatedAt: Date;
  createdBy: bigint;
  updatedBy: bigint;
  athlete?: {
    id: string;
    name: string;
    surname: string;
    parentName?: string | null;
    parentSurname?: string | null;
    parentEmail?: string | null;
    parentPhone?: string | null;
    nationalId?: string | null;
  } | null;
  team?: {
    id: string;
    name: string;
  } | null;
  school?: {
    id: string;
    name: string;
  } | null;
  // Legacy field for backward compatibility
  planId?: string | null;
  paymentPlan?: {
    id: string;
    name: string;
    amount: string;
    totalAmount?: string;
    frequency: "monthly" | "quarterly";
    description?: string | null;
  } | null;
}

export interface ExpenseInstructor {
  id: string;
  name: string;
  surname: string;
}

export interface ExpenseFacility {
  id: string;
  name: string;
  type: string;
}

export interface Expense {
  id: string;
  tenantId: string;
  amount: string; // Decimal stored as string
  date: string; // Date stored as string
  category: 'salary' | 'insurance' | 'rent' | 'equipment' | 'other';
  description: string;
  instructor?: ExpenseInstructor | null;
  facility?: ExpenseFacility | null;
  createdAt: Date;
  updatedAt: Date;
  createdBy: bigint;
  updatedBy: bigint;
}

export interface Facility {
  id: string;
  tenantId: string;
  name: string;
  type: 'field' | 'court' | 'pool' | 'studio' | 'other';
  address: string;
  totalCapacity?: number | null;
  currentlyOccupied?: number | null;
  length?: string | null; // Decimal stored as string
  width?: string | null; // Decimal stored as string
  dimensionUnit?: 'meters' | 'feet' | null;
  createdAt: Date;
  updatedAt: Date;
  createdBy: bigint;
  updatedBy: bigint;
  // Computed property for backward compatibility
  capacity?: {
    total: number;
    currentlyOccupied: number;
    dimensions?: {
      length: number;
      width: number;
      unit: 'meters' | 'feet';
    };
  };
}

export interface PaymentTransaction {
  id: string;
  tenantId: string;
  athleteId: string;
  paymentId?: string | null; // Nullable for balance top-ups
  amount: string; // decimal from database comes as string
  transactionMethod: 'cash' | 'bank_transfer' | 'credit_card' | 'existing_balance';
  transactionDate: Date;
  notes?: string | null;
  createdAt: Date;
  updatedAt: Date;
  createdBy: bigint;
  updatedBy: bigint;
  athlete?: {
    id: string;
    name: string;
    surname: string;
    parentName?: string | null;
    parentEmail?: string | null;
    parentPhone?: string | null;
  };
  payment?: {
    id: string;
    amount: string;
    description?: string | null;
    type: 'fee' | 'equipment' | 'other';
  };
}

export interface Item {
  id: string;
  name: string;
  description?: string | null;
  price: string; // decimal from database comes as string
  category: "equipment" | "clothing" | "accessories" | "other";
  stock: number;
  image?: string | null;
  createdAt: Date;
  updatedAt: Date;
  createdBy: bigint;
  updatedBy: bigint;
}

export interface ItemPurchase {
  id: string;
  itemId: string;
  athleteId: string;
  quantity: number;
  totalPrice: number;
  status: "pending" | "completed" | "cancelled";
  purchaseDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Payment Plan Assignment interfaces
export interface AthletePaymentPlan {
  id: string;
  athleteId: string;
  planId: string;
  teamId?: string | null;
  assignedDate: string; // date from database comes as string
  isActive: boolean;
  lastPaymentDate?: string | null; // date from database comes as string
  createdAt: Date;
  updatedAt: Date;
  createdBy: bigint;
  updatedBy: bigint;
  // Relations
  athlete?: {
    id: string;
    name: string;
    surname: string;
    nationalId?: string | null;
    parentName?: string | null;
    parentEmail?: string | null;
    parentPhone?: string | null;
  } | null;
  plan?: {
    id: string;
    name: string;
    monthlyValue: string;
    assignDay: number;
    dueDay: number;
    status: 'active' | 'inactive';
    description?: string | null;
  } | null;
  team?: {
    id: string;
    name: string;
    branchId: string;
    branchName?: string;
    schoolId: string;
    schoolName?: string;
  } | null;
  // Legacy fields for backward compatibility
  startDate?: string;
  remainingPayments?: number;
}

export interface PaymentPlanAssignment {
  planId: string;
  athleteId: string;
  teamId?: string | null;
  isActive: boolean;
}