import { db } from '@/src/db';
import * as schema from '@/src/db/schema';
import { eq, and, desc, ne } from 'drizzle-orm';

export class SmsTemplatesDB {
  
  static async getSmsTemplates(language?: string) {
    if (language) {
      return db.select().from(schema.smsTemplates)
        .where(eq(schema.smsTemplates.language, language))
        .orderBy(schema.smsTemplates.type, schema.smsTemplates.language);
    }

    return db.select().from(schema.smsTemplates)
      .orderBy(schema.smsTemplates.type, schema.smsTemplates.language);
  }

  static async getActiveSmsTemplates(language?: string) {
    if (language) {
      return db.select().from(schema.smsTemplates)
        .where(and(
          eq(schema.smsTemplates.isActive, true),
          eq(schema.smsTemplates.language, language)
        ))
        .orderBy(schema.smsTemplates.type);
    }

    return db.select().from(schema.smsTemplates)
      .where(eq(schema.smsTemplates.isActive, true))
      .orderBy(schema.smsTemplates.type);
  }

  static async getSmsTemplateByType(
    type: 'general' | 'event' | 'meeting' | 'holiday' | 'custom',
    language: string = 'en'
  ) {

    const result = await db.select().from(schema.smsTemplates)
      .where(and(
        eq(schema.smsTemplates.type, type),
        eq(schema.smsTemplates.language, language),
        eq(schema.smsTemplates.isActive, true)
      ))
      .limit(1);

    return result[0] || null;
  }

  static async createSmsTemplate(data: {
    type: 'general' | 'event' | 'meeting' | 'holiday' | 'custom';
    language: string;
    title: string;
    template: string;
    isActive?: boolean;
    createdBy?: bigint;
    updatedBy?: bigint;
  }) {
    const result = await db.insert(schema.smsTemplates).values({
      type: data.type,
      language: data.language,
      title: data.title,
      template: data.template,
      isActive: data.isActive ?? true,
      createdBy: data.createdBy || BigInt(1),
      updatedBy: data.updatedBy || BigInt(1),
    }).returning();

    return result[0];
  }

  static async updateSmsTemplate(
    id: string,
    data: {
      title?: string;
      template?: string;
      isActive?: boolean;
      updatedBy?: bigint;
    }
  ) {
    const result = await db.update(schema.smsTemplates)
      .set({
        ...data,
        updatedBy: data.updatedBy || BigInt(1),
        updatedAt: new Date(),
      })
      .where(eq(schema.smsTemplates.id, id))
      .returning();

    return result[0];
  }

  static async deleteSmsTemplate(id: string) {
    const result = await db.delete(schema.smsTemplates)
      .where(eq(schema.smsTemplates.id, id))
      .returning();

    return result[0];
  }

  static async deactivateOtherTemplates(
    type: 'general' | 'event' | 'meeting' | 'holiday' | 'custom',
    language: string,
    excludeId?: string
  ) {
    let whereCondition = and(
      eq(schema.smsTemplates.type, type),
      eq(schema.smsTemplates.language, language),
      eq(schema.smsTemplates.isActive, true)
    );

    if (excludeId) {
      whereCondition = and(
        whereCondition,
        ne(schema.smsTemplates.id, excludeId)
      );
    }

    return db.update(schema.smsTemplates)
      .set({
        isActive: false,
        updatedBy: BigInt(1),
        updatedAt: new Date(),
      })
      .where(whereCondition);
  }
}
