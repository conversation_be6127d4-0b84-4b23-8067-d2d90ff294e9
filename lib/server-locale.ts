"use server";

import { headers, cookies } from 'next/headers';
import acceptLanguage from 'accept-language';

acceptLanguage.languages(['en', 'tr']);

/**
 * Get the current locale from server-side context
 * Uses cookies first, then Accept-Language header, with 'en' as fallback
 */
export async function getServerLocale(): Promise<string> {
  try {
    // Try to get locale from cookies first (set by middleware)
    const cookieStore = await cookies();
    const localeCookie = cookieStore.get('NEXT_LOCALE');
    
    if (localeCookie?.value) {
      const lng = acceptLanguage.get(localeCookie.value);
      if (lng) return lng;
    }
    
    // Fallback to Accept-Language header
    const headersList = await headers();
    const acceptLanguageHeader = headersList.get('accept-language');
    
    if (acceptLanguageHeader) {
      const lng = acceptLanguage.get(acceptLanguageHeader);
      if (lng) return lng;
    }
    
    // Default fallback
    return 'en';
  } catch (error) {
    // If anything goes wrong, return default
    console.warn('Error getting server locale:', error);
    return 'en';
  }
}