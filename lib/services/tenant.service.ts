import { BaseService } from './base';
import { ServiceResult, ValidationError } from '../errors/types';
import { NotFoundError, BusinessRuleError } from '../errors/errors';
import { TenantAwareDB } from '../db';
import { getServerUserId } from '../tenant-utils-server';

export class TenantService extends BaseService {
  
  /**
   * Get all tenants
   */
  async getTenants(
    userId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeOperation(
      'getTenants',
      async () => {
        return TenantAwareDB.getTenants();
      },
      { userId }
    );
  }

  /**
   * Get tenant by ID
   */
  async getTenantById(
    id: string,
    userId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'getTenantById',
      async () => {
        return TenantAwareDB.getTenantById(id);
      },
      { userId, tenantId: id }
    );
  }

  /**
   * Get tenant by name
   */
  async getTenantByName(
    name: string,
    userId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'getTenantByName',
      async () => {
        return TenantAwareDB.getTenantByName(name);
      },
      { userId }
    );
  }

  /**
   * Create a new tenant
   */
  async createTenant(
    data: {
      id: string; // Zitadel organization ID
      name: string;
    },
    userId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'createTenant',
      async () => {
        const effectiveUserId = userId ? BigInt(userId) : await getServerUserId();
        
        if (!effectiveUserId) {
          throw new BusinessRuleError('user_id_required', 'User ID is required for tenant creation');
        }
        
        // Validate required fields
        if (!data.id || !data.name) {
          throw new BusinessRuleError('tenant_id_required','Tenant ID and name are required');
        }

        // Check if tenant already exists
        const existingTenant = await TenantAwareDB.getTenantById(data.id);
        if (existingTenant) {
          throw new BusinessRuleError('duplicate_tenant_id', 'A tenant with this ID already exists');
        }

        // Check for duplicate name
        const existingByName = await TenantAwareDB.getTenantByName(data.name);
        if (existingByName) {
          throw new BusinessRuleError('duplicate_tenant_name', 'A tenant with this name already exists');
        }

        return TenantAwareDB.createTenant(data, effectiveUserId);
      },
      { userId, tenantId: data.id }
    );
  }

  /**
   * Update tenant
   */
  async updateTenant(
    id: string,
    data: {
      name?: string;
    },
    userId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeOperation(
      'updateTenant',
      async () => {
        const effectiveUserId = userId ? BigInt(userId) : await getServerUserId();
        
        if (!effectiveUserId) {
          throw new BusinessRuleError('user_id_required', 'User ID is required for tenant update');
        }
        
        // Check if tenant exists
        const existingTenant = await TenantAwareDB.getTenantById(id);
        if (!existingTenant) {
          throw new NotFoundError('Tenant not found');
        }

        // Check for duplicate name (excluding current tenant)
        if (data.name) {
          const duplicateName = await TenantAwareDB.getTenantByName(data.name);
          if (duplicateName && duplicateName.id !== id) {
            throw new BusinessRuleError('duplicate_tenant_name', 'A tenant with this name already exists');
          }
        }

        return TenantAwareDB.updateTenant(id, data, effectiveUserId);
      },
      { userId, tenantId: id }
    );
  }

  /**
   * Delete tenant
   */
  async deleteTenant(
    id: string,
    userId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'deleteTenant',
      async () => {
        // Check if tenant exists
        const existingTenant = await TenantAwareDB.getTenantById(id);
        if (!existingTenant) {
          throw new NotFoundError('Tenant not found');
        }

        await TenantAwareDB.deleteTenant(id);
        return true;
      },
      { userId, tenantId: id }
    );
  }

  /**
   * Check if tenant exists
   */
  async tenantExists(
    id: string,
    userId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeOperation(
      'tenantExists',
      async () => {
        return TenantAwareDB.tenantExists(id);
      },
      { userId, tenantId: id }
    );
  }
}