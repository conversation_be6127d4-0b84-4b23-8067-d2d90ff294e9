import { BaseService } from './base';
import { TenantAwareDB } from '../db';
import { ServiceResult } from '../errors/types';
import { BusinessRuleError } from '../errors/errors';
import { updateAthleteBalance } from '../balance-calculator';
import { balanceProcessorService } from './balance-processor.service';
import { PaymentBusinessRulesValidator } from '../utils/payment-business-rules';
import { Payment } from '../types';

export class PaymentService extends BaseService {
  constructor() {
    super('PaymentService');
  }

  async getPayments(
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeDatabaseOperation(
      'getPayments',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getPayments(resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getPaymentsPaginated(
    options: {
      page: number;
      limit: number;
      search?: string;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
      filters?: Record<string, string>;
    },
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'getPaymentsPaginated',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getPaymentsPaginated(
          options.page,
          options.limit,
          options.search,
          options.sortBy || 'date',
          options.sortOrder || 'desc',
          options.filters || {}
        );
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getPaymentById(
    id: string,
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.checkResourceExists(
      'getPaymentById',
      'Payment',
      id,
      () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return TenantAwareDB.getPaymentById(id, resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async getAthleteOutstandingPayments(
    athleteId: string,
    tenantId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any[]>> {
    return this.executeDatabaseOperation(
      'getAthleteOutstandingPayments',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        return await TenantAwareDB.getAthleteOutstandingPayments(athleteId, resolvedTenantId);
      },
      { tenantId: effectiveTenantId || tenantId }
    );
  }

  async createPayment(
    data: {
      athleteId: string;
      athletePaymentPlanId?: string | null;
      amount: string;
      date: string;
      dueDate: string;
      status: "pending" | "completed" | "overdue" | "cancelled" | "partially_paid";
      type: "fee" | "equipment" | "other";
      description?: string | null;
    },
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'createPayment',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;

        // Create the payment first
        const payment = await TenantAwareDB.createPayment(data, resolvedTenantId, userIdBigInt);

        // If payment is created with completed status, automatically generate transaction
        if (data.status === 'completed') {
          await TenantAwareDB.createPaymentTransaction({
            athleteId: data.athleteId,
            paymentId: payment.id,
            amount: data.amount,
            transactionMethod: 'cash', // Default method for completed payments
            notes: `completed_payment:${data.description || data.type}`,
          }, resolvedTenantId, userIdBigInt);

          // Update athlete balance
          await updateAthleteBalance(data.athleteId, resolvedTenantId!, userIdBigInt);
        }
        // If payment is pending or overdue, try to allocate existing balance
        else if (data.status === 'pending' || data.status === 'overdue') {
          const balanceResult = await balanceProcessorService().processNewPaymentBalanceAllocation(
            payment.id,
            resolvedTenantId,
            userId,
            resolvedTenantId
          );

          if (balanceResult.success && balanceResult.data?.allocated) {
            // Return updated payment data
            const updatedPayment = await TenantAwareDB.getPaymentById(payment.id, resolvedTenantId);
            return { ...updatedPayment, balanceAllocation: balanceResult.data };
          }
        }

        return payment;
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  async updatePayment(
    id: string,
    data: {
      athleteId?: string;
      athletePaymentPlanId?: string | null;
      amount?: string;
      date?: string;
      dueDate?: string;
      status?: "pending" | "completed" | "overdue" | "cancelled";
      type?: "fee" | "equipment" | "other";
      description?: string | null;
    },
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'updatePayment',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;
        
        // Get current payment for validation
        const currentPayment = await TenantAwareDB.getPaymentById(id, resolvedTenantId);
        if (!currentPayment) {
          throw new BusinessRuleError(
            'paymentNotFound',
            `Payment with ID ${id} not found`,
            undefined,
            'Payment not found'
          );
        }

        // Server-side business rules validation
        const validation = PaymentBusinessRulesValidator.validateEdit(currentPayment as Payment, data);
        if (!validation.isValid) {
          const errorRule = validation.errorKey?.split('.').pop() || 'editNotAllowed';
          throw new BusinessRuleError(
            errorRule,
            `Payment edit not allowed: ${validation.errorKey}`,
            undefined,
            'Payment cannot be edited in its current state'
          );
        }

        // Check if payment should be marked as completed
        let updatedData = { ...data };
        if (data.amount !== undefined) {
          const newAmount = parseFloat(data.amount);
          const amountPaid = parseFloat(currentPayment.amountPaid || '0');
          
          // If new amount equals amount paid, mark as completed
          if (newAmount === amountPaid && newAmount > 0) {
            (updatedData as any).status = 'completed';
          }
          // If new amount is greater than amount paid and current status is completed, revert to partially_paid
          else if (newAmount > amountPaid && currentPayment.status === 'completed') {
            (updatedData as any).status = amountPaid > 0 ? 'partially_paid' : 'pending';
          }
        }

        return await TenantAwareDB.updatePayment(id, updatedData, resolvedTenantId, userIdBigInt);
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  async deletePayment(
    id: string,
    tenantId?: string,
    effectiveTenantId?: string,
    userId?: string
  ): Promise<ServiceResult<boolean>> {
    return this.executeDatabaseOperation(
      'deletePayment',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;

        // Get current payment for validation
        const currentPayment = await TenantAwareDB.getPaymentById(id, resolvedTenantId);
        if (!currentPayment) {
          throw new BusinessRuleError(
            'paymentNotFound',
            `Payment with ID ${id} not found`,
            undefined,
            'Payment not found'
          );
        }

        // Server-side business rules validation
        const validation = PaymentBusinessRulesValidator.validateDelete(currentPayment as Payment);
        if (!validation.isValid) {
          const errorRule = validation.errorKey?.split('.').pop() || 'deleteNotAllowed';
          throw new BusinessRuleError(
            errorRule,
            `Payment deletion not allowed: ${validation.errorKey}`,
            undefined,
            'Payment cannot be deleted in its current state'
          );
        }

        // Delete payment and get details for balance update
        const deleteResult = await TenantAwareDB.deletePayment(id, resolvedTenantId);
        const { paymentDetails } = deleteResult;

        // Update athlete balance if the deleted payment affects the balance
        // Only pending and overdue payments affect the athlete's outstanding balance
        // Cancelled payments don't affect balance, completed payments don't affect balance
        if (paymentDetails.status === 'pending' || paymentDetails.status === 'overdue') {
          await updateAthleteBalance(
            paymentDetails.athleteId,
            resolvedTenantId!,
            userIdBigInt
          );
        }

        return true;
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  async processPayment(
    id: string,
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'processPayment',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;
        return await TenantAwareDB.updatePayment(id, { 
          status: "completed",
          date: new Date().toISOString().split('T')[0]
        }, resolvedTenantId, userIdBigInt);
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }

  async createRefund(
    paymentId: string,
    data: {
      amount: string;
      reason: string;
    },
    tenantId?: string,
    userId?: string,
    effectiveTenantId?: string
  ): Promise<ServiceResult<any>> {
    return this.executeDatabaseOperation(
      'createRefund',
      async () => {
        const resolvedTenantId = effectiveTenantId || tenantId;
        const userIdBigInt = userId ? BigInt(userId) : undefined;

        // Get payment for validation
        const payment = await TenantAwareDB.getPaymentById(paymentId, resolvedTenantId);
        if (!payment) {
          throw new BusinessRuleError(
            'paymentNotFound',
            `Payment with ID ${paymentId} not found`,
            undefined,
            'Payment not found'
          );
        }

        // Server-side validation for refund creation
        const validation = PaymentBusinessRulesValidator.validateRefundCreation(payment as Payment);
        if (!validation.isValid) {
          const errorRule = validation.errorKey?.split('.').pop() || 'refundNotAllowed';
          throw new BusinessRuleError(
            errorRule,
            `Refund not allowed: ${validation.errorKey}`,
            undefined,
            'Refund cannot be created for this payment'
          );
        }

        // Validate refund amount
        const refundAmount = parseFloat(data.amount);
        const paidAmount = parseFloat(payment.amountPaid || '0');
        
        if (refundAmount > paidAmount) {
          throw new BusinessRuleError(
            'refundExceedsPaid',
            `Refund amount ${refundAmount} exceeds paid amount ${paidAmount}`,
            undefined,
            'Refund amount cannot exceed paid amount'
          );
        }

        // Create refund transaction (negative amount to credit athlete's balance)
        const refundTransaction = await TenantAwareDB.createPaymentTransaction({
          athleteId: payment.athleteId,
          paymentId: paymentId,
          amount: `-${data.amount}`, // Negative amount for refund
          transactionMethod: 'cash', // Use cash as the method for refunds
          notes: `refund:${data.reason}`,
        }, resolvedTenantId, userIdBigInt);

        // Update athlete balance
        await updateAthleteBalance(payment.athleteId, resolvedTenantId!, userIdBigInt);

        return refundTransaction;
      },
      { tenantId: effectiveTenantId || tenantId, userId }
    );
  }
}

// Export singleton instance
let paymentServiceInstance: PaymentService | null = null;

export function paymentService(): PaymentService {
  if (!paymentServiceInstance) {
    paymentServiceInstance = new PaymentService();
  }
  return paymentServiceInstance;
}
