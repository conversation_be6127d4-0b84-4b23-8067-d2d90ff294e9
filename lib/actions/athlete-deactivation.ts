"use server";

import { getServerTenantId, getServerUserId } from '../tenant-utils-server';
import { getServerLocale } from '../server-locale';
import { AthleteDeactivationService } from '../services/athlete-deactivation.service';
import { athleteService } from '../services';
import { removeAthleteFromTeam } from './athlete-teams';
import { deleteAssignment } from './payment-plan-assignments';
import { updatePayment, createPayment, getPaymentsPaginated } from './payments';
import { balanceProcessorService } from '../services/balance-processor.service';
import { paymentTransactionService } from '../services/payment-transaction.service';
import { updateAthleteBalance } from '../balance-calculator';
import logger from '@/lib/logger';
import type { PaymentAdjustment, DeactivationSummary } from '../services/athlete-deactivation.service';

export interface CalculateDeactivationResult {
  success: boolean;
  summary?: DeactivationSummary;
  error?: string;
  errorType?: 'BusinessRuleError' | 'general';
}

export interface ExecuteDeactivationResult {
  success: boolean;
  processedAdjustments?: number;
  totalCreditAdded?: number;
  error?: string;
  errorType?: 'BusinessRuleError' | 'general';
}

/**
 * Calculate deactivation adjustments without executing them
 * This shows the user what will happen before they confirm
 */
export async function calculateAthleteDeactivation(
  athleteId: string
): Promise<CalculateDeactivationResult> {
  try {
    const tenantId = await getServerTenantId();
    const locale = await getServerLocale();
    
    if (!tenantId) {
      return { success: false, error: 'Missing tenant context' };
    }

    const service = new AthleteDeactivationService();
    const result = await service.calculateDeactivationAdjustments(athleteId, locale);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.userMessage || 'Failed to calculate deactivation adjustments',
        errorType: 'general'
      };
    }

    return {
      success: true,
      summary: result.data
    };
  } catch (error) {
    logger.error('calculateAthleteDeactivation error:', { error, athleteId });
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to calculate deactivation'
    };
  }
}

/**
 * Execute the deactivation process atomically
 * This includes:
 * 1. Setting athlete status to inactive
 * 2. Processing payment adjustments (prorated amounts, credits)
 * 3. Removing athlete from teams
 * 4. Deactivating payment plan assignments
 * 5. Cancelling future payments and same-day pending payments
 * 6. Updating athlete balance to reflect cancelled payments
 * 7. Processing any balance allocations from adjustments
 */
export async function executeAthleteDeactivation(
  summary: DeactivationSummary
): Promise<ExecuteDeactivationResult> {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();
    const locale = await getServerLocale();

    if (!tenantId || !userId) {
      return { success: false, error: 'Missing tenant or user context' };
    }

    logger.info('Starting athlete deactivation execution', {
      athleteId: summary.athleteId,
      adjustmentsCount: summary.paymentAdjustments.length,
      teamsCount: summary.teamsToRemove.length,
      plansCount: summary.paymentPlansToDeactivate.length
    });

    // Execute each step with proper error handling
    let processedAdjustments = 0;
    let totalCreditAdded = 0;

    // 1. Update athlete status to inactive
    logger.info('Updating athlete status to inactive', { athleteId: summary.athleteId });
    const updateResult = await athleteService().updateAthlete(
      summary.athleteId,
      { status: 'inactive' },
      undefined,
      tenantId
    );

    if (!updateResult.success) {
      logger.error('Failed to update athlete status:', { error: updateResult.error });
      return {
        success: false,
        error: updateResult.error?.userMessage || 'Failed to deactivate athlete',
        errorType: updateResult.validationErrors?.length ? 'BusinessRuleError' : 'general'
      };
    }

    // 2. Process payment adjustments
    logger.info('Processing payment adjustments', { count: summary.paymentAdjustments.length });
    for (const adjustment of summary.paymentAdjustments) {
      try {
        await executePaymentAdjustment(adjustment, summary.athleteId, locale);
        processedAdjustments++;
        
        // For cancelled payments, the debt reduction is equivalent to credit
        if (adjustment.action === 'cancel_payment') {
          totalCreditAdded += adjustment.originalAmount; // Full payment amount is effectively credited
        } else {
          totalCreditAdded += adjustment.creditToBalance;
        }
        
        logger.info('Payment adjustment processed', { 
          paymentId: adjustment.paymentId, 
          action: adjustment.action,
          originalAmount: adjustment.originalAmount,
          creditEffect: adjustment.action === 'cancel_payment' ? adjustment.originalAmount : adjustment.creditToBalance
        });
      } catch (adjustmentError) {
        logger.error('Error processing payment adjustment:', { 
          error: adjustmentError, 
          paymentId: adjustment.paymentId 
        });
        // Continue with other adjustments
      }
    }

    // 3. Remove from teams
    logger.info('Removing athlete from teams', { count: summary.teamsToRemove.length });
    for (const team of summary.teamsToRemove) {
      try {
        await removeAthleteFromTeam(summary.athleteId, team.teamId);
        logger.info('Removed athlete from team', { athleteId: summary.athleteId, teamId: team.teamId });
      } catch (teamError) {
        logger.error('Error removing athlete from team:', { 
          error: teamError, 
          athleteId: summary.athleteId, 
          teamId: team.teamId 
        });
        // Continue with other teams
      }
    }

    // 4. Deactivate payment plan assignments
    logger.info('Deactivating payment plan assignments', { count: summary.paymentPlansToDeactivate.length });
    for (const plan of summary.paymentPlansToDeactivate) {
      try {
        await deleteAssignment(plan.planId);
        logger.info('Deactivated payment plan assignment', { planId: plan.planId });
      } catch (planError) {
        logger.error('Error deactivating payment plan assignment:', { 
          error: planError, 
          planId: plan.planId 
        });
        // Continue with other plans
      }
    }

    // 5. Cancel future payments and same-day payments
    logger.info('Cancelling future and same-day payments', { athleteId: summary.athleteId });
    await cancelFuturePayments(summary.athleteId);

    // 6. Force balance update after payment cancellations to ensure consistency
    logger.info('Updating athlete balance after payment cancellations', { athleteId: summary.athleteId });
    try {
      // First try using the balance processor service
      await balanceProcessorService().processAthleteBalanceAllocation(
        summary.athleteId,
        tenantId,
        userId?.toString(),
        tenantId
      );
      logger.info('Balance updated after payment cancellations', { athleteId: summary.athleteId });
    } catch (balanceError) {
      // Fallback to direct balance calculation
      try {
        await updateAthleteBalance(summary.athleteId, tenantId, userId || undefined);
        logger.info('Balance updated via fallback method after payment cancellations', { athleteId: summary.athleteId });
      } catch (fallbackError) {
        logger.error('Error updating balance after payment cancellations (both methods failed):', { 
          balanceError, 
          fallbackError,
          athleteId: summary.athleteId 
        });
      }
    }

    // 7. Process balance allocation if there are credits from adjustments
    if (totalCreditAdded > 0) {
      logger.info('Processing balance allocation for adjustments', { athleteId: summary.athleteId, creditAmount: totalCreditAdded });
      try {
        await balanceProcessorService().processAthleteBalanceAllocation(
          summary.athleteId,
          tenantId,
          userId?.toString(),
          tenantId
        );
        logger.info('Balance allocation completed for adjustments', { athleteId: summary.athleteId });
      } catch (balanceError) {
        logger.error('Error processing balance allocation for adjustments (non-critical):', { 
          error: balanceError, 
          athleteId: summary.athleteId 
        });
        // Don't fail the deactivation for balance processing errors
      }
    }

    logger.info('Athlete deactivation completed successfully', {
      athleteId: summary.athleteId,
      processedAdjustments,
      totalCreditAdded
    });

    return {
      success: true,
      processedAdjustments,
      totalCreditAdded
    };
  } catch (error) {
    logger.error('executeAthleteDeactivation error:', { error, athleteId: summary.athleteId });
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to execute deactivation'
    };
  }
}

/**
 * Execute a single payment adjustment
 */
async function executePaymentAdjustment(
  adjustment: PaymentAdjustment,
  athleteId: string,
  locale: string
): Promise<void> {
  switch (adjustment.action) {
    case 'reduce_payment':
      // Update payment amount and description
      await updatePayment(adjustment.paymentId, {
        amount: adjustment.finalAmount.toFixed(2),
        description: adjustment.description
      });
      break;

    case 'cancel_payment':
      // Cancel the payment entirely
      await updatePayment(adjustment.paymentId, {
        status: 'cancelled',
        description: adjustment.description
      });
      break;

    case 'create_balance_transaction':
      // Create balance top-up transaction (positive balance)
      await paymentTransactionService().addAthleteBalance(
        athleteId,
        adjustment.creditToBalance.toFixed(2),
        'cash', // Use cash as method for internal balance adjustments
        adjustment.description
      );
      break;

    case 'adjust_partial_overpaid':
      // Update payment amount and status
      await updatePayment(adjustment.paymentId, {
        amount: adjustment.finalAmount.toFixed(2),
        status: 'completed',
        description: adjustment.description
      });
      
      // Add overpayment as credit if there is any
      if (adjustment.creditToBalance > 0) {
        await paymentTransactionService().addAthleteBalance(
          athleteId,
          adjustment.creditToBalance.toFixed(2),
          'cash',
          `${adjustment.description} - Overpayment credit`
        );
      }
      break;

    case 'adjust_partial_underpaid':
      // Just update payment amount
      await updatePayment(adjustment.paymentId, {
        amount: adjustment.finalAmount.toFixed(2),
        description: adjustment.description
      });
      break;
  }
}

/**
 * Cancel future payments and same-day payments that haven't been processed
 */
async function cancelFuturePayments(athleteId: string): Promise<void> {
  try {
    // Get all payments for the athlete
    const allPayments = await getPaymentsPaginated(1, 1000, athleteId, 'date', 'desc', {});
    
    // Filter to payments that should be cancelled:
    // 1. Future payments (beyond today)
    // 2. Same-day payments that are still pending/overdue (not completed/partially_paid)
    const today = new Date();
    today.setHours(23, 59, 59, 999); // Set to end of day for comparison
    
    const paymentsToCancel = allPayments.data.filter((payment: any) => {
      const paymentDate = new Date(payment.date);
      const isEligibleStatus = payment.status === 'pending' || payment.status === 'overdue';
      
      // Cancel if it's a future payment OR same-day payment with eligible status
      return (paymentDate > today || 
              (paymentDate.toDateString() === new Date().toDateString() && isEligibleStatus)) 
             && isEligibleStatus;
    });

    // Cancel each eligible payment
    for (const payment of paymentsToCancel) {
      try {
        await updatePayment(payment.id, {
          status: 'cancelled',
          description: 'Cancelled due to athlete deactivation'
        });
        
        logger.info('Payment cancelled during deactivation', { 
          athleteId, 
          paymentId: payment.id,
          paymentDate: payment.date,
          originalStatus: payment.status,
          amount: payment.amount
        });
      } catch (paymentError) {
        logger.error('Error cancelling payment:', { 
          error: paymentError, 
          paymentId: payment.id 
        });
        // Continue with other payments
      }
    }

    logger.info('Payments cancelled during deactivation', { 
      athleteId, 
      cancelledCount: paymentsToCancel.length,
      futurePayments: paymentsToCancel.filter((p: any) => new Date(p.date) > today).length,
      sameDayPayments: paymentsToCancel.filter((p: any) => new Date(p.date).toDateString() === new Date().toDateString()).length
    });
  } catch (error) {
    logger.error('Error cancelling payments:', { error, athleteId });
    // Don't throw - this is not critical for deactivation
  }
}
