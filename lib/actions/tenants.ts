"use server";

import { TenantService } from '../services/tenant.service';
import { getServerUserId } from '../tenant-utils-server';
import logger from '@/lib/logger';

const tenantService = new TenantService('TenantService');

// Tenants
export async function getTenants() {
  try {
    const userId = await getServerUserId();
    const result = await tenantService.getTenants(userId?.toString());
    
    if (!result.success) {
      logger.error("getTenants error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to get tenants");
    }
    
    return result.data || [];
  } catch (error) {
    logger.error("getTenants error:", { error });
    throw error;
  }
}

export async function getTenantById(id: string) {
  try {
    const userId = await getServerUserId();
    const result = await tenantService.getTenantById(id, userId?.toString());
    
    if (!result.success) {
      logger.error("getTenantById error:", { error: result.error });
      throw new Error(result.error?.userMessage || `Failed to get tenant with ID ${id}`);
    }
    
    return result.data;
  } catch (error) {
    logger.error("getTenantById error:", { error });
    throw error;
  }
}

export async function getTenantByName(name: string) {
  try {
    const userId = await getServerUserId();
    const result = await tenantService.getTenantByName(name, userId?.toString());
    
    if (!result.success) {
      logger.error("getTenantByName error:", { error: result.error });
      throw new Error(result.error?.userMessage || `Failed to get tenant with name ${name}`);
    }
    
    return result.data;
  } catch (error) {
    logger.error("getTenantByName error:", { error });
    throw error;
  }
}

export async function createTenant(data: { id: string; name: string }) {
  try {
    logger.info("createTenant called with:", { data });
    const userId = await getServerUserId();
    
    const result = await tenantService.createTenant(data, userId?.toString());
    
    if (!result.success) {
      logger.error("createTenant error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to create tenant");
    }
    
    logger.info("createTenant success:", { tenantId: result.data?.id });
    return result.data;
  } catch (error) {
    logger.error("createTenant error:", { error });
    throw error;
  }
}

export async function updateTenant(id: string, data: { name?: string }) {
  try {
    logger.info("updateTenant called with:", { id, data });
    const userId = await getServerUserId();
    
    const result = await tenantService.updateTenant(id, data, userId?.toString());
    
    if (!result.success) {
      logger.error("updateTenant error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to update tenant");
    }
    
    logger.info("updateTenant success:", { tenantId: id });
    return result.data;
  } catch (error) {
    logger.error("updateTenant error:", { error });
    throw error;
  }
}

export async function deleteTenant(id: string) {
  try {
    logger.info("deleteTenant called with:", { id });
    const userId = await getServerUserId();
    
    const result = await tenantService.deleteTenant(id, userId?.toString());
    
    if (!result.success) {
      logger.error("deleteTenant error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to delete tenant");
    }
    
    logger.info("deleteTenant success:", { tenantId: id });
    return true;
  } catch (error) {
    logger.error("deleteTenant error:", { error });
    throw error;
  }
}

export async function tenantExists(id: string) {
  try {
    const userId = await getServerUserId();
    const result = await tenantService.tenantExists(id, userId?.toString());
    
    if (!result.success) {
      logger.error("tenantExists error:", { error: result.error });
      throw new Error(result.error?.userMessage || "Failed to check tenant existence");
    }
    
    return result.data || false;
  } catch (error) {
    logger.error("tenantExists error:", { error });
    throw error;
  }
}