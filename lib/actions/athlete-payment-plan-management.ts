"use server";

import { revalidatePath } from "next/cache";
import { assignPaymentPlanToAthlete } from "../payment-plan-utils";
import { getServerTenantId, getServerUserId } from "../tenant-utils-server";
import logger from "@/lib/logger";

export interface AssignPaymentPlanWithProrationParams {
  athleteId: string;
  planId: string;
  teamId: string;
  useProrated?: boolean;
  customProratedAmount?: string;
  locale?: string;
}

/**
 * Assign a payment plan to an athlete with optional prorated payment creation
 */
export async function assignPaymentPlanWithProration({
  athleteId,
  planId,
  teamId,
  useProrated = false,
  customProratedAmount,
  locale = 'en'
}: AssignPaymentPlanWithProrationParams) {
  try {
    const tenantId = await getServerTenantId();
    const userId = await getServerUserId();

    if (!tenantId || !userId) {
      return { success: false, error: 'Missing tenant or user context' };
    }

    // Assign the payment plan
    const assignmentResult = await assignPaymentPlanToAthlete({
      athleteId,
      planId,
      teamId,
      tenantId,
      userId: BigInt(userId),
      isActive: true
    });

    // Create prorated payment if requested
    if (useProrated && assignmentResult) {
      const { createPayment } = await import('../actions/payments');
      const { getPaymentPlans } = await import('../actions/payment-plans');
      const { calculateProratedAmountForBillingCycle } = await import('../proration-utils');
      const { getPaymentTranslation } = await import('../utils/server-translation');
      const { getPaymentDueDate } = await import('../utils/date-utils');

      // Get payment plan details
      const paymentPlans = await getPaymentPlans();
      const paymentPlan = paymentPlans.find(plan => plan.id === planId);

      if (paymentPlan) {
        let proratedAmount: number;
        
        if (customProratedAmount && customProratedAmount.trim() !== '') {
          proratedAmount = parseFloat(customProratedAmount);
        } else {
          const monthlyAmount = parseFloat(paymentPlan.monthlyValue);
          // Use the new billing cycle-based proration calculation
          proratedAmount = calculateProratedAmountForBillingCycle(
            monthlyAmount,
            paymentPlan.assignDay
          );
        }

        if (proratedAmount > 0) {
          const billingDate = new Date();
          await createPayment({
            athleteId,
            athletePaymentPlanId: assignmentResult.id,
            amount: proratedAmount.toFixed(2),
            date: billingDate.toISOString().split('T')[0],
            dueDate: getPaymentDueDate(billingDate),
            status: 'pending',
            type: 'fee',
            description: getPaymentTranslation(
              'descriptions.proratedBalance',
              locale,
              { planName: paymentPlan.name }
            ),
          });
        }
      }
    }

    revalidatePath(`/athletes/${athleteId}`);
    return { success: true, data: assignmentResult };
  } catch (error) {
    logger.error("assignPaymentPlanWithProration error:", { error });
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to assign payment plan' 
    };
  }
}
