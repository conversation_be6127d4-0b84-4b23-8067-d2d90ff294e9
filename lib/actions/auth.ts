
"use server";

import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import logger from '@/lib/logger';
import axios from 'axios';
import { smsService, passwordResetSmsService } from '@/lib/services';
import { getServerTranslation } from '@/lib/utils/server-translation';

// Environment constants - defined once at module level
const ZITADEL_ISSUER = process.env.ZITADEL_ISSUER;
const ZITADEL_PAT_TOKEN = process.env.ZITADEL_PAT_TOKEN;

export async function serverLogout() {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      logger.warn('Server logout called without an active session.');
      return { success: false, error: 'Not authenticated' };
    }

    logger.info(`User logout initiated for session: ${session.user?.email || 'unknown'}`);

    if (!ZITADEL_ISSUER) {
      logger.error('ZITADEL_ISSUER environment variable is not set');
      return { success: false, error: 'Server configuration error' };
    }

    const zitadelEndSessionUrl = new URL(`${ZITADEL_ISSUER}/oidc/v1/end_session`);

    if (session.idToken) {
      zitadelEndSessionUrl.searchParams.append('id_token_hint', session.idToken);
    }

    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const postLogoutRedirectUri = `${baseUrl}/auth/signin`;
    zitadelEndSessionUrl.searchParams.append('post_logout_redirect_uri', postLogoutRedirectUri);

    // Revoke token on Zitadel side
    if (session.accessToken) {
      try {
        await axios.post(`${ZITADEL_ISSUER}/oauth/v2/revoke`, new URLSearchParams({
          client_id: process.env.ZITADEL_CLIENT_ID!,
          token: session.accessToken as string,
        }), {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
          },
        });
        logger.info('Access token successfully revoked on Zitadel.');
      } catch (revokeError) {
        logger.error('Error revoking token on Zitadel:', { revokeError });
        // Continue with logout even if token revocation fails
      }
    }

    logger.info(`User logout completed successfully for: ${session.user?.email || 'unknown'}`);
    return { success: true, logoutUrl: zitadelEndSessionUrl.toString() };

  } catch (error) {
    logger.error('Error during server-side logout:', { error });
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error during logout' };
  }
}

export async function checkRecentPasswordResetAttempt(userId: string) {
  try {
    const result = await passwordResetSmsService().getActiveVerificationCode(userId);
    
    if (result.success && result.data) {
      const log = result.data;
      const now = new Date();
      const sentAt = new Date(log.createdAt);
      const elapsedMs = now.getTime() - sentAt.getTime();
      const twoMinutesMs = 2 * 60 * 1000; // 2 minutes in milliseconds
      
      if (elapsedMs < twoMinutesMs) {
        // Still within 2-minute cooldown period
        const remainingMs = twoMinutesMs - elapsedMs;
        const remainingSeconds = Math.ceil(remainingMs / 1000);
        
        return {
          success: true,
          data: {
            canSendCode: false,
            remainingSeconds,
            lastSentAt: log.createdAt
          }
        };
      }
    }
    
    // No recent attempt or cooldown period has passed
    return {
      success: true,
      data: {
        canSendCode: true,
        remainingSeconds: 0,
        lastSentAt: null
      }
    };
  } catch (error) {
    logger.error('Error checking recent password reset attempt:', { error });
    return {
      success: false,
      error: 'Failed to check recent attempts'
    };
  }
}

export async function getUserByUsername(username: string) {
  try {
    if (!ZITADEL_ISSUER || !ZITADEL_PAT_TOKEN) {
      logger.error('Required environment variables are not set');
      return { success: false, error: 'Server configuration error' };
    }

    const response = await axios.post(
      `${ZITADEL_ISSUER}/v2/users`,
      {
        query: {
          offset: "0",
          limit: 100,
          asc: true
        },
        queries: [
          {
            userNameQuery: {
              userName: username,
              method: "TEXT_QUERY_METHOD_EQUALS"
            }
          }
        ]
      },
      {
        headers: {
          'Authorization': `Bearer ${ZITADEL_PAT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.data?.result && response.data.result.length > 0) {
      const user = response.data.result[0];
      
      // Check if user has phone number
      const phone = user.human?.phone?.phone;
      const isPhoneVerified = user.human?.phone?.isVerified;
      const preferredLanguage = user.human?.profile?.preferredLanguage || 'en';
      
      if (!phone) {
        logger.warn(`User ${username} does not have a phone number`);
        return { 
          success: false, 
          error: 'Phone number not found. Please contact support.' 
        };
      }

      if (!isPhoneVerified) {
        logger.warn(`User ${username} phone number is not verified`);
        return { 
          success: false, 
          error: 'Phone number not verified. Please contact support.' 
        };
      }

      return {
        success: true,
        data: {
          userId: user.userId,
          phone,
          preferredLanguage
        }
      };
    }

    return { success: false, error: 'User not found' };
  } catch (error) {
    logger.error('Error getting user by username:', { error });
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to get user information' 
    };
  }
}

export async function sendPasswordResetCode(
  userId: string,
  phoneNumber: string,
  preferredLanguage: string = 'en',
  username: string = ''
) {
  try {
    if (!ZITADEL_ISSUER || !ZITADEL_PAT_TOKEN) {
      logger.error('Missing required environment variables');
      return { success: false, error: 'Server configuration error' };
    }

    // Check if user has a recent password reset attempt
    const recentAttemptCheck = await checkRecentPasswordResetAttempt(userId);
    if (recentAttemptCheck.success && !recentAttemptCheck.data?.canSendCode) {
      const remainingSeconds = recentAttemptCheck.data?.remainingSeconds || 120;
      logger.warn(`Rate limit: User ${userId} attempted password reset too soon. ${remainingSeconds}s remaining`);
      return {
        success: false,
        error: `Please wait ${remainingSeconds} seconds before requesting a new code`,
        rateLimited: true,
        remainingSeconds
      };
    }

    // Call Zitadel API to get password reset code
    const response = await axios.post(
      `${ZITADEL_ISSUER}/v2/users/${userId}/password_reset`,
      {
        returnCode: {}
      },
      {
        headers: {
          'Authorization': `Bearer ${ZITADEL_PAT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.data?.verificationCode) {
      const verificationCode = response.data.verificationCode;
      
      // Format phone number (remove +90 for Turkish numbers)
      let formattedPhone = phoneNumber;
      if (phoneNumber.startsWith('+90')) {
        formattedPhone = phoneNumber.substring(3);
      } else if (phoneNumber.startsWith('90')) {
        formattedPhone = phoneNumber.substring(2);
      }
      
      // Get the SMS message template from i18n based on language
      const smsMessageTemplate = getServerTranslation(
        'templates.passwordReset.message',
        preferredLanguage === 'tr' ? 'tr' : 'en',
        'sms',
        { code: verificationCode }
      );
      
      // Calculate SMS credits (based on message length)
      const messageLength = smsMessageTemplate.length;
      const creditsNeeded = messageLength <= 160 ? 1 : Math.ceil(messageLength / 153);
      
      // Create password reset SMS log entry
      const logResult = await passwordResetSmsService().createLog(
        username || phoneNumber, // Use username if provided, otherwise use phone number
        userId,
        formattedPhone,
        smsMessageTemplate,
        verificationCode,
        10 // Expires in 10 minutes
      );
      
      if (!logResult.success) {
        logger.error('Failed to create password reset SMS log', {
          error: logResult.error,
          userId
        });
        return {
          success: false,
          error: 'Failed to initialize password reset process'
        };
      }
      
      const logEntry = logResult.data;
      
      // Send SMS using existing SMS service
      const smsResult = await smsService().sendSms({
        messages: [{
          receiver: formattedPhone,
          message: smsMessageTemplate
        }],
        metadata: {
          type: 'password_reset',
          language: preferredLanguage,
          userId: userId,
          passwordResetLogId: logEntry?.id
        }
      });

      if (!smsResult.success || !smsResult.data || smsResult.data.sentCount === 0) {
        // Update log to failed status
        if (logEntry?.id) {
          await passwordResetSmsService().markAsFailed(
            logEntry.id,
            smsResult.error?.message || 'Failed to send SMS',
            smsResult.error
          );
        }
        
        logger.error('Failed to send password reset SMS', { 
          phone: formattedPhone.substring(0, 3) + '****',
          error: smsResult.error,
          userId: userId
        });
        return { 
          success: false, 
          error: 'Failed to send verification code via SMS' 
        };
      }
      
      // Update log to success status
      if (logEntry?.id) {
        await passwordResetSmsService().markAsSent(
          logEntry.id,
          creditsNeeded,
          smsResult.data
        );
      }

      logger.info(`Password reset code sent successfully for user ${userId}`);
      return { 
        success: true, 
        data: { message: 'Verification code sent successfully' } 
      };
    }

    return { 
      success: false, 
      error: 'Failed to generate password reset code' 
    };
  } catch (error) {
    logger.error('Error sending password reset code:', { error });
    if (axios.isAxiosError(error) && error.response?.status === 409) {
      return { 
        success: false, 
        error: 'A password reset code was recently sent. Please wait before requesting a new one.' 
      };
    }
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to send password reset code' 
    };
  }
}

export async function resetUserPassword(
  userId: string,
  newPassword: string,
  verificationCode: string
) {
  try {
    if (!ZITADEL_ISSUER || !ZITADEL_PAT_TOKEN) {
      logger.error('Missing required environment variables');
      return { success: false, error: 'Server configuration error', errorCode: 'SERVER_ERROR' };
    }

    // Validate password strength
    if (newPassword.length < 8) {
      return { 
        success: false, 
        error: 'Password must be at least 8 characters long',
        errorCode: 'PASSWORD_TOO_SHORT'
      };
    }

    // Call Zitadel API to reset password
    const response = await axios.post(
      `${ZITADEL_ISSUER}/v2/users/${userId}/password`,
      {
        newPassword: {
          password: newPassword
        },
        verificationCode: verificationCode
      },
      {
        headers: {
          'Authorization': `Bearer ${ZITADEL_PAT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.status === 200 || response.status === 204) {
      // Mark the verification code as used in our logs
      await passwordResetSmsService().markCodeAsUsed(verificationCode, userId);
      
      logger.info(`Password reset successfully for user ${userId}`);
      return { 
        success: true, 
        data: { message: 'Password reset successfully' } 
      };
    }

    return { 
      success: false, 
      error: 'Failed to reset password',
      errorCode: 'UNKNOWN_ERROR'
    };
  } catch (error) {
    logger.error('Error resetting user password:', { error });
    
    if (axios.isAxiosError(error)) {
      const errorMessage = error.response?.data?.message || '';
      
      // Check for specific error codes in the response message
      if (errorMessage.includes('CODE-woT0xc')) {
        return { 
          success: false, 
          error: 'Invalid verification code',
          errorCode: 'INVALID_VERIFICATION_CODE'
        };
      }
      
      if (errorMessage.includes('DOMAIN-co3Xw')) {
        return { 
          success: false, 
          error: 'Password does not meet security requirements',
          errorCode: 'INSECURE_PASSWORD'
        };
      }
      
      // Handle other HTTP status codes
      if (error.response?.status === 400) {
        return { 
          success: false, 
          error: 'Invalid verification code or code has expired',
          errorCode: 'INVALID_REQUEST'
        };
      }
      
      if (error.response?.status === 404) {
        return { 
          success: false, 
          error: 'User not found',
          errorCode: 'USER_NOT_FOUND'
        };
      }
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to reset password',
      errorCode: 'GENERAL_ERROR'
    };
  }
}
