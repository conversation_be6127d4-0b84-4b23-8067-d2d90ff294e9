import { apiClient, ApiResponse } from './client';

export interface Item {
  id: string;
  name: string;
  description?: string;
  price: number;
  category?: string;
  stock?: number;
  isActive: boolean;
}

export interface CreateItemData {
  name: string;
  description?: string;
  price: number;
  category?: string;
  stock?: number;
}

export interface UpdateItemData {
  name?: string;
  description?: string;
  price?: number;
  category?: string;
  stock?: number;
  isActive?: boolean;
}

export class ItemsApi {
  static async getItems(): Promise<ApiResponse<Item[]>> {
    return apiClient.get<Item[]>('/items');
  }

  static async getItemById(id: string): Promise<ApiResponse<Item>> {
    return apiClient.get<Item>(`/items/${id}`);
  }

  static async createItem(data: CreateItemData): Promise<ApiResponse<Item>> {
    return apiClient.post<Item>('/items', data);
  }

  static async updateItem(id: string, data: UpdateItemData): Promise<ApiResponse<Item>> {
    return apiClient.put<Item>(`/items/${id}`, data);
  }

  static async deleteItem(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/items/${id}`);
  }
}

export const getItems = ItemsApi.getItems;
export const getItemById = ItemsApi.getItemById;
export const createItem = ItemsApi.createItem;
export const updateItem = ItemsApi.updateItem;
export const deleteItem = ItemsApi.deleteItem;
