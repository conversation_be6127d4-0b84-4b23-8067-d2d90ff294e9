import { apiClient, ApiResponse, PaginatedResponse } from './client';

export interface Payment {
  id: string;
  athleteId: string;
  amount: number;
  dueDate: string;
  status: 'pending' | 'paid' | 'overdue' | 'cancelled';
  paymentMethod?: 'cash' | 'bank_transfer' | 'credit_card';
  description?: string;
  athleteName?: string;
  athleteSurname?: string;
  teamName?: string;
  planName?: string;
}

export interface CreatePaymentData {
  athleteId: string;
  amount: number;
  dueDate: string;
  description?: string;
  paymentMethod?: 'cash' | 'bank_transfer' | 'credit_card';
}

export interface UpdatePaymentData {
  amount?: number;
  dueDate?: string;
  status?: 'pending' | 'paid' | 'overdue' | 'cancelled';
  paymentMethod?: 'cash' | 'bank_transfer' | 'credit_card';
  description?: string;
}

export interface GetPaymentsPaginatedParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  status?: 'pending' | 'paid' | 'overdue' | 'cancelled';
  athleteId?: string;
  teamId?: string;
  paymentMethod?: 'cash' | 'bank_transfer' | 'credit_card';
  dueDateFrom?: string;
  dueDateTo?: string;
}

export class PaymentsApi {
  // Get all payments
  static async getPayments(): Promise<ApiResponse<Payment[]>> {
    return apiClient.get<Payment[]>('/payments');
  }

  // Get payments with pagination
  static async getPaymentsPaginated(params?: GetPaymentsPaginatedParams): Promise<ApiResponse<PaginatedResponse<Payment>>> {
    return apiClient.getPaginated<Payment>('/payments/paginated', params);
  }

  // Get payment by ID
  static async getPaymentById(id: string): Promise<ApiResponse<Payment>> {
    return apiClient.get<Payment>(`/payments/${id}`);
  }

  // Create payment
  static async createPayment(data: CreatePaymentData): Promise<ApiResponse<Payment>> {
    return apiClient.post<Payment>('/payments', data);
  }

  // Update payment
  static async updatePayment(id: string, data: UpdatePaymentData): Promise<ApiResponse<Payment>> {
    return apiClient.put<Payment>(`/payments/${id}`, data);
  }

  // Delete payment
  static async deletePayment(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/payments/${id}`);
  }

  // Get payments for SMS (overdue/pending)
  static async getPaymentsForSms(): Promise<ApiResponse<Payment[]>> {
    return apiClient.get<Payment[]>('/payments/for-sms');
  }

  // Process payment status updates
  static async processPaymentStatusUpdates(): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/payments/process-status-updates');
  }
}

// Export individual functions for backward compatibility
export const getPayments = PaymentsApi.getPayments;
export const getPaymentsPaginated = PaymentsApi.getPaymentsPaginated;
export const getPaymentById = PaymentsApi.getPaymentById;
export const createPayment = PaymentsApi.createPayment;
export const updatePayment = PaymentsApi.updatePayment;
export const deletePayment = PaymentsApi.deletePayment;
export const getPaymentsForSms = PaymentsApi.getPaymentsForSms;
