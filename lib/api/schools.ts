import { apiClient, ApiResponse } from './client';

export interface School {
  id: string;
  name: string;
  foundedYear: number;
  address?: string;
  phone?: string;
  email?: string;
  logo?: string;
  branches?: string[];
}

export interface CreateSchoolData {
  name: string;
  foundedYear: number;
  address?: string;
  phone?: string;
  email?: string;
  logo?: string;
  branches?: string[];
}

export interface UpdateSchoolData {
  name?: string;
  foundedYear?: number;
  address?: string;
  phone?: string;
  email?: string;
  logo?: string;
  branches?: string[];
}

export class SchoolsApi {
  // Get all schools
  static async getSchools(): Promise<ApiResponse<School[]>> {
    return apiClient.get<School[]>('/schools');
  }

  // Get school by ID
  static async getSchoolById(id: string): Promise<ApiResponse<School>> {
    return apiClient.get<School>(`/schools/${id}`);
  }

  // Create school
  static async createSchool(data: CreateSchoolData): Promise<ApiResponse<School>> {
    return apiClient.post<School>('/schools', data);
  }

  // Update school
  static async updateSchool(id: string, data: UpdateSchoolData): Promise<ApiResponse<School>> {
    return apiClient.put<School>(`/schools/${id}`, data);
  }

  // Delete school
  static async deleteSchool(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/schools/${id}`);
  }

  // Assign branches to school
  static async assignBranchesToSchool(id: string, branchIds: string[]): Promise<ApiResponse<void>> {
    return apiClient.put<void>(`/schools/${id}/branches`, { branchIds });
  }
}

// Export individual functions for backward compatibility
export const getSchools = SchoolsApi.getSchools;
export const getSchoolById = SchoolsApi.getSchoolById;
export const createSchool = SchoolsApi.createSchool;
export const updateSchool = SchoolsApi.updateSchool;
export const deleteSchool = SchoolsApi.deleteSchool;
