import { apiClient, ApiResponse } from './client';

export interface SmsConfiguration {
  id: string;
  pendingPaymentTemplate: string;
  overduePaymentTemplate: string;
  pendingReminderDays: number[];
  overdueReminderDays: number[];
  isActive: boolean;
}

export interface CreateSmsConfigurationData {
  pendingPaymentTemplate: string;
  overduePaymentTemplate: string;
  pendingReminderDays: number[];
  overdueReminderDays: number[];
}

export interface UpdateSmsConfigurationData {
  pendingPaymentTemplate?: string;
  overduePaymentTemplate?: string;
  pendingReminderDays?: number[];
  overdueReminderDays?: number[];
}

export interface SendPaymentReminderSmsData {
  paymentIds: string[];
  templateType: 'pending' | 'overdue';
  customTemplate?: string;
  combinePayments?: boolean;
  language?: string;
}

export interface SendTemplateSmsData {
  templateType: 'general' | 'event' | 'meeting' | 'holiday' | 'custom';
  customTemplate?: string;
  athleteIds: string[];
  language?: string;
}

export interface SmsLog {
  id: string;
  recipient: string;
  message: string;
  status: 'sent' | 'failed' | 'pending';
  sentAt: string;
  creditsUsed: number;
  messageId?: string;
}

export interface SmsBalance {
  balance: number;
  lastUpdated: string;
}

export class SmsApi {
  // Get SMS configuration
  static async getSmsConfiguration(): Promise<ApiResponse<SmsConfiguration>> {
    return apiClient.get<SmsConfiguration>('/sms/configuration');
  }

  // Create SMS configuration
  static async createSmsConfiguration(data: CreateSmsConfigurationData): Promise<ApiResponse<SmsConfiguration>> {
    return apiClient.post<SmsConfiguration>('/sms/configuration', data);
  }

  // Update SMS configuration
  static async updateSmsConfiguration(id: string, data: UpdateSmsConfigurationData): Promise<ApiResponse<SmsConfiguration>> {
    return apiClient.put<SmsConfiguration>(`/sms/configuration/${id}`, data);
  }

  // Activate SMS configuration
  static async activateSmsConfiguration(id: string): Promise<ApiResponse<void>> {
    return apiClient.post<void>(`/sms/configuration/${id}/activate`);
  }

  // Send payment reminder SMS
  static async sendPaymentReminderSms(data: SendPaymentReminderSmsData): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/sms/send/payment-reminder', data);
  }

  // Send template SMS
  static async sendTemplateSms(data: SendTemplateSmsData): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/sms/send/template', data);
  }

  // Get SMS logs
  static async getSmsLogs(params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: 'sent' | 'failed' | 'pending';
    dateFrom?: string;
    dateTo?: string;
  }): Promise<ApiResponse<any>> {
    return apiClient.getPaginated<SmsLog>('/sms/logs', params);
  }

  // Get SMS balance
  static async getSmsBalance(): Promise<ApiResponse<SmsBalance>> {
    return apiClient.get<SmsBalance>('/sms/balance');
  }

  // Add SMS balance
  static async addSmsBalance(data: { amount: number; paymentMethod: string }): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/sms/balance/add', data);
  }

  // Get SMS pricing
  static async getSmsPricing(): Promise<ApiResponse<any>> {
    return apiClient.get<any>('/sms/pricing');
  }

  // Get athletes for SMS
  static async getAllActiveAthletesForSms(): Promise<ApiResponse<any[]>> {
    return apiClient.get<any[]>('/sms/athletes');
  }

  // Preview SMS message
  static async previewSmsMessage(data: {
    templateType: 'pending' | 'overdue' | 'general' | 'event' | 'meeting' | 'holiday' | 'custom';
    customTemplate?: string;
    athleteId?: string;
    paymentIds?: string[];
    language?: string;
  }): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/sms/preview', data);
  }
}

// Export individual functions for backward compatibility
export const getSmsConfiguration = SmsApi.getSmsConfiguration;
export const createSmsConfiguration = SmsApi.createSmsConfiguration;
export const updateSmsConfiguration = SmsApi.updateSmsConfiguration;
export const activateSmsConfiguration = SmsApi.activateSmsConfiguration;
export const sendPaymentReminderSms = SmsApi.sendPaymentReminderSms;
export const sendTemplateSms = SmsApi.sendTemplateSms;
export const getSmsLogs = SmsApi.getSmsLogs;
export const getSmsBalance = SmsApi.getSmsBalance;
export const addSmsBalance = SmsApi.addSmsBalance;
export const getAllActiveAthletesForSms = SmsApi.getAllActiveAthletesForSms;
