import { apiClient, ApiResponse, PaginatedResponse } from './client';

export interface Instructor {
  id: string;
  name: string;
  surname: string;
  email: string;
  phone: string;
  nationalId?: string;
  birthDate?: string;
  address?: string;
  salary?: number;
  branchIds?: string[];
  schoolIds?: string[];
}

export interface CreateInstructorData {
  name: string;
  surname: string;
  email: string;
  phone: string;
  nationalId?: string;
  birthDate?: string;
  address?: string;
  salary?: number;
  branchIds?: string[];
  schoolIds?: string[];
}

export interface UpdateInstructorData {
  name?: string;
  surname?: string;
  email?: string;
  phone?: string;
  nationalId?: string;
  birthDate?: string;
  address?: string;
  salary?: number;
  branchIds?: string[];
  schoolIds?: string[];
}

export interface GetInstructorsPaginatedParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  name?: string;
  surname?: string;
  email?: string;
  phone?: string;
}

export class InstructorsApi {
  // Get all instructors
  static async getInstructors(): Promise<ApiResponse<Instructor[]>> {
    return apiClient.get<Instructor[]>('/instructors');
  }

  // Get instructors with pagination
  static async getInstructorsPaginated(params?: GetInstructorsPaginatedParams): Promise<ApiResponse<PaginatedResponse<Instructor>>> {
    return apiClient.getPaginated<Instructor>('/instructors/paginated', params);
  }

  // Get instructor by ID
  static async getInstructorById(id: string): Promise<ApiResponse<Instructor>> {
    return apiClient.get<Instructor>(`/instructors/${id}`);
  }

  // Create instructor
  static async createInstructor(data: CreateInstructorData): Promise<ApiResponse<Instructor>> {
    return apiClient.post<Instructor>('/instructors', data);
  }

  // Update instructor
  static async updateInstructor(id: string, data: UpdateInstructorData): Promise<ApiResponse<Instructor>> {
    return apiClient.put<Instructor>(`/instructors/${id}`, data);
  }

  // Delete instructor
  static async deleteInstructor(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/instructors/${id}`);
  }

  // Update instructor schools
  static async updateInstructorSchools(instructorId: string, schoolIds: string[]): Promise<ApiResponse<void>> {
    return apiClient.put<void>(`/instructors/${instructorId}/schools`, { schoolIds });
  }

  // Update instructor branches
  static async updateInstructorBranches(instructorId: string, branchIds: string[]): Promise<ApiResponse<void>> {
    return apiClient.put<void>(`/instructors/${instructorId}/branches`, { branchIds });
  }
}

// Export individual functions for backward compatibility
export const getInstructors = InstructorsApi.getInstructors;
export const getInstructorsPaginated = InstructorsApi.getInstructorsPaginated;
export const getInstructorById = InstructorsApi.getInstructorById;
export const createInstructor = InstructorsApi.createInstructor;
export const updateInstructor = InstructorsApi.updateInstructor;
export const deleteInstructor = InstructorsApi.deleteInstructor;
export const updateInstructorSchools = InstructorsApi.updateInstructorSchools;
export const updateInstructorBranches = InstructorsApi.updateInstructorBranches;
