import { apiClient, ApiResponse } from './client';

export interface PaymentPlan {
  id: string;
  name: string;
  amount: number;
  frequency: 'monthly' | 'weekly' | 'yearly';
  description?: string;
  isActive: boolean;
}

export interface CreatePaymentPlanData {
  name: string;
  amount: number;
  frequency: 'monthly' | 'weekly' | 'yearly';
  description?: string;
}

export interface UpdatePaymentPlanData {
  name?: string;
  amount?: number;
  frequency?: 'monthly' | 'weekly' | 'yearly';
  description?: string;
  isActive?: boolean;
}

export class PaymentPlansApi {
  static async getPaymentPlans(): Promise<ApiResponse<PaymentPlan[]>> {
    return apiClient.get<PaymentPlan[]>('/payment-plans');
  }

  static async getPaymentPlanById(id: string): Promise<ApiResponse<PaymentPlan>> {
    return apiClient.get<PaymentPlan>(`/payment-plans/${id}`);
  }

  static async createPaymentPlan(data: CreatePaymentPlanData): Promise<ApiResponse<PaymentPlan>> {
    return apiClient.post<PaymentPlan>('/payment-plans', data);
  }

  static async updatePaymentPlan(id: string, data: UpdatePaymentPlanData): Promise<ApiResponse<PaymentPlan>> {
    return apiClient.put<PaymentPlan>(`/payment-plans/${id}`, data);
  }

  static async deletePaymentPlan(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/payment-plans/${id}`);
  }

  static async getAvailablePaymentPlansForTeam(teamId: string): Promise<ApiResponse<PaymentPlan[]>> {
    return apiClient.get<PaymentPlan[]>(`/payment-plans/available-for-team/${teamId}`);
  }
}

export const getPaymentPlans = PaymentPlansApi.getPaymentPlans;
export const getPaymentPlanById = PaymentPlansApi.getPaymentPlanById;
export const createPaymentPlan = PaymentPlansApi.createPaymentPlan;
export const updatePaymentPlan = PaymentPlansApi.updatePaymentPlan;
export const deletePaymentPlan = PaymentPlansApi.deletePaymentPlan;
export const getAvailablePaymentPlansForTeam = PaymentPlansApi.getAvailablePaymentPlansForTeam;
