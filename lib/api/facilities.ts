import { apiClient, ApiResponse } from './client';

export interface Facility {
  id: string;
  name: string;
  description?: string;
  capacity?: number;
  location?: string;
  equipment?: string[];
  isActive: boolean;
}

export interface CreateFacilityData {
  name: string;
  description?: string;
  capacity?: number;
  location?: string;
  equipment?: string[];
}

export interface UpdateFacilityData {
  name?: string;
  description?: string;
  capacity?: number;
  location?: string;
  equipment?: string[];
  isActive?: boolean;
}

export class FacilitiesApi {
  static async getFacilities(): Promise<ApiResponse<Facility[]>> {
    return apiClient.get<Facility[]>('/facilities');
  }

  static async getFacilityById(id: string): Promise<ApiResponse<Facility>> {
    return apiClient.get<Facility>(`/facilities/${id}`);
  }

  static async createFacility(data: CreateFacilityData): Promise<ApiResponse<Facility>> {
    return apiClient.post<Facility>('/facilities', data);
  }

  static async updateFacility(id: string, data: UpdateFacilityData): Promise<ApiResponse<Facility>> {
    return apiClient.put<Facility>(`/facilities/${id}`, data);
  }

  static async deleteFacility(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/facilities/${id}`);
  }
}

export const getFacilities = FacilitiesApi.getFacilities;
export const getFacilityById = FacilitiesApi.getFacilityById;
export const createFacility = FacilitiesApi.createFacility;
export const updateFacility = FacilitiesApi.updateFacility;
export const deleteFacility = FacilitiesApi.deleteFacility;
