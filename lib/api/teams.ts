import { apiClient, ApiResponse, PaginatedResponse } from './client';

export interface Team {
  id: string;
  name: string;
  description?: string;
  schoolId: string;
  branchId: string;
  instructorId?: string;
  maxAthletes?: number;
  minAge?: number;
  maxAge?: number;
  schoolName?: string;
  branchName?: string;
  instructorName?: string;
}

export interface CreateTeamData {
  name: string;
  description?: string;
  schoolId: string;
  branchId: string;
  instructorId?: string;
  maxAthletes?: number;
  minAge?: number;
  maxAge?: number;
}

export interface UpdateTeamData {
  name?: string;
  description?: string;
  schoolId?: string;
  branchId?: string;
  instructorId?: string;
  maxAthletes?: number;
  minAge?: number;
  maxAge?: number;
}

export interface GetTeamsPaginatedParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  name?: string;
  description?: string;
  schoolName?: string;
  branchName?: string;
  instructorName?: string;
}

export class TeamsApi {
  // Get all teams
  static async getTeams(): Promise<ApiResponse<Team[]>> {
    return apiClient.get<Team[]>('/teams');
  }

  // Get teams with pagination
  static async getTeamsPaginated(params?: GetTeamsPaginatedParams): Promise<ApiResponse<PaginatedResponse<Team>>> {
    return apiClient.getPaginated<Team>('/teams/paginated', params);
  }

  // Get team by ID
  static async getTeamById(id: string): Promise<ApiResponse<Team>> {
    return apiClient.get<Team>(`/teams/${id}`);
  }

  // Create team
  static async createTeam(data: CreateTeamData): Promise<ApiResponse<Team>> {
    return apiClient.post<Team>('/teams', data);
  }

  // Create team with schedules
  static async createTeamWithSchedules(teamData: CreateTeamData, schedules: any[]): Promise<ApiResponse<Team>> {
    return apiClient.post<Team>('/teams/with-schedules', { teamData, schedules });
  }

  // Update team
  static async updateTeam(id: string, data: UpdateTeamData): Promise<ApiResponse<Team>> {
    return apiClient.put<Team>(`/teams/${id}`, data);
  }

  // Update team with schedules
  static async updateTeamWithSchedules(id: string, teamData: UpdateTeamData, schedules: any[]): Promise<ApiResponse<Team>> {
    return apiClient.put<Team>(`/teams/${id}/with-schedules`, { teamData, schedules });
  }

  // Delete team
  static async deleteTeam(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/teams/${id}`);
  }

  // Bulk assign payment plans to team athletes
  static async bulkAssignPaymentPlans(teamId: string, paymentPlanId: string, athleteIds: string[]): Promise<ApiResponse<any>> {
    return apiClient.post<any>(`/teams/${teamId}/payment-plans/bulk-assign`, { paymentPlanId, athleteIds });
  }
}

// Export individual functions for backward compatibility
export const getTeams = TeamsApi.getTeams;
export const getTeamsPaginated = TeamsApi.getTeamsPaginated;
export const getTeamById = TeamsApi.getTeamById;
export const createTeam = TeamsApi.createTeam;
export const createTeamWithSchedules = TeamsApi.createTeamWithSchedules;
export const updateTeam = TeamsApi.updateTeam;
export const updateTeamWithSchedules = TeamsApi.updateTeamWithSchedules;
export const deleteTeam = TeamsApi.deleteTeam;
