import { apiClient, ApiResponse } from './client';

export interface AthleteTeamAssignment {
  athleteId: string;
  teamId: string;
  paymentPlanId?: string;
  isActive: boolean;
}

export interface AddAthleteToTeamData {
  athleteId: string;
  teamId: string;
  paymentPlanId?: string;
}

export interface RemoveAthleteFromTeamData {
  athleteId: string;
  teamId: string;
  cleanupPayments?: boolean;
}

export class AthleteTeamManagementApi {
  // Add athlete to team with payment plan
  static async addAthleteToTeamWithPaymentPlan(data: AddAthleteToTeamData): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/athlete-team-management/add-with-payment-plan', data);
  }

  // Remove athlete from team with cleanup
  static async removeAthleteFromTeamWithCleanup(data: RemoveAthleteFromTeamData): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/athlete-team-management/remove-with-cleanup', data);
  }

  // Check if athlete is in team
  static async checkAthleteInTeam(athleteId: string, teamId: string): Promise<ApiResponse<boolean>> {
    return apiClient.get<boolean>(`/athlete-team-management/check/${athleteId}/${teamId}`);
  }

  // Get athlete team payment plans
  static async getAthleteTeamPaymentPlans(athleteId: string, teamId: string): Promise<ApiResponse<any[]>> {
    return apiClient.get<any[]>(`/athlete-team-management/payment-plans/${athleteId}/${teamId}`);
  }

  // Check payment plan conflicts
  static async checkPaymentPlanConflicts(teamId: string, paymentPlanId: string, athleteIds: string[]): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/athlete-team-management/check-conflicts', {
      teamId,
      paymentPlanId,
      athleteIds,
    });
  }

  // Bulk assign payment plan
  static async bulkAssignPaymentPlan(teamId: string, paymentPlanId: string, athleteIds: string[]): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/athlete-team-management/bulk-assign', {
      teamId,
      paymentPlanId,
      athleteIds,
    });
  }

  // Get all facility schedules
  static async getAllFacilitySchedules(): Promise<ApiResponse<any[]>> {
    return apiClient.get<any[]>('/athlete-team-management/facility-schedules');
  }

  // Payment plan assignments
  static async assignPaymentPlan(data: any): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/athlete-team-management/assign-payment-plan', data);
  }

  static async deactivateAssignment(assignmentId: string): Promise<ApiResponse<any>> {
    return apiClient.put<any>(`/athlete-team-management/deactivate-assignment/${assignmentId}`);
  }

  // Add athlete to team
  static async addAthleteToTeam(athleteId: string, teamId: string): Promise<ApiResponse<any>> {
    return apiClient.post<any>('/athlete-team-management/add-to-team', { athleteId, teamId });
  }

  // Get payment transactions paginated
  static async getPaymentTransactionsPaginated(params?: any): Promise<ApiResponse<any>> {
    return apiClient.getPaginated<any>('/payment-transactions/paginated', params);
  }

  // Process payment
  static async processPayment(paymentId: string, data: any): Promise<ApiResponse<any>> {
    return apiClient.post<any>(`/payments/${paymentId}/process`, data);
  }

  // Get transactions by payment ID
  static async getTransactionsByPaymentId(paymentId: string): Promise<ApiResponse<any[]>> {
    return apiClient.get<any[]>(`/payment-transactions/by-payment/${paymentId}`);
  }
}

// Export individual functions for backward compatibility
export const addAthleteToTeamWithPaymentPlan = AthleteTeamManagementApi.addAthleteToTeamWithPaymentPlan;
export const removeAthleteFromTeamWithCleanup = AthleteTeamManagementApi.removeAthleteFromTeamWithCleanup;
export const checkAthleteInTeam = AthleteTeamManagementApi.checkAthleteInTeam;
export const getAthleteTeamPaymentPlans = AthleteTeamManagementApi.getAthleteTeamPaymentPlans;
export const checkPaymentPlanConflicts = AthleteTeamManagementApi.checkPaymentPlanConflicts;
export const bulkAssignPaymentPlan = AthleteTeamManagementApi.bulkAssignPaymentPlan;
export const getAllFacilitySchedules = AthleteTeamManagementApi.getAllFacilitySchedules;
export const assignPaymentPlan = AthleteTeamManagementApi.assignPaymentPlan;
export const deactivateAssignment = AthleteTeamManagementApi.deactivateAssignment;
export const addAthleteToTeam = AthleteTeamManagementApi.addAthleteToTeam;
export const getPaymentTransactionsPaginated = AthleteTeamManagementApi.getPaymentTransactionsPaginated;
export const processPayment = AthleteTeamManagementApi.processPayment;
export const getTransactionsByPaymentId = AthleteTeamManagementApi.getTransactionsByPaymentId;
