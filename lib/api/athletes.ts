import { apiClient, ApiResponse, PaginatedResponse } from './client';

export interface Athlete {
  id: string;
  name: string;
  surname: string;
  nationalId: string;
  birthDate: string;
  registrationDate: string;
  parentName: string;
  parentSurname: string;
  parentPhone: string;
  parentEmail?: string;
  parentAddress?: string;
  status: 'active' | 'inactive' | 'suspended';
  balance?: number;
  teamDetails?: any[];
}

export interface CreateAthleteData {
  name: string;
  surname: string;
  nationalId: string;
  birthDate: string;
  registrationDate?: string;
  parentName: string;
  parentSurname: string;
  parentPhone: string;
  parentEmail?: string;
  parentAddress?: string;
  paymentPlanId?: string;
}

export interface UpdateAthleteData {
  name?: string;
  surname?: string;
  nationalId?: string;
  birthDate?: string;
  registrationDate?: string;
  parentName?: string;
  parentSurname?: string;
  parentPhone?: string;
  parentEmail?: string;
  parentAddress?: string;
}

export interface TeamAssignment {
  teamId: string;
  paymentPlanId?: string;
  useProrated?: boolean;
}

export interface CreateAthleteWithTeamAssignmentsData extends CreateAthleteData {
  teamAssignments?: TeamAssignment[];
}

export interface CreateAthleteWithBalanceData extends CreateAthleteWithTeamAssignmentsData {
  initialBalance?: string;
  useProrated?: boolean;
}

export interface GetAthletesPaginatedParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  name?: string;
  surname?: string;
  parentEmail?: string;
  parentPhone?: string;
  nationalId?: string;
  status?: 'active' | 'inactive' | 'suspended';
}

export class AthletesApi {
  // Get all athletes
  static async getAthletes(): Promise<ApiResponse<Athlete[]>> {
    return apiClient.get<Athlete[]>('/athletes');
  }

  // Get athletes with pagination
  static async getAthletesPaginated(params?: GetAthletesPaginatedParams): Promise<ApiResponse<PaginatedResponse<Athlete>>> {
    return apiClient.getPaginated<Athlete>('/athletes/paginated', params);
  }

  // Get athlete by ID
  static async getAthleteById(id: string): Promise<ApiResponse<Athlete>> {
    return apiClient.get<Athlete>(`/athletes/${id}`);
  }

  // Get overdue athletes
  static async getOverdueAthletes(): Promise<ApiResponse<Athlete[]>> {
    return apiClient.get<Athlete[]>('/athletes/overdue');
  }

  // Get athlete activation data
  static async getAthleteActivationData(id: string): Promise<ApiResponse<any>> {
    return apiClient.get<any>(`/athletes/${id}/activation-data`);
  }

  // Create athlete
  static async createAthlete(data: CreateAthleteData): Promise<ApiResponse<Athlete>> {
    return apiClient.post<Athlete>('/athletes', data);
  }

  // Create athlete with team assignments
  static async createAthleteWithTeamAssignments(data: CreateAthleteWithTeamAssignmentsData): Promise<ApiResponse<Athlete>> {
    return apiClient.post<Athlete>('/athletes/with-teams', data);
  }

  // Create athlete with team assignments and balance
  static async createAthleteWithBalance(data: CreateAthleteWithBalanceData, locale: string = 'en'): Promise<ApiResponse<Athlete>> {
    return apiClient.post<Athlete>(`/athletes/with-balance?locale=${locale}`, data);
  }

  // Update athlete
  static async updateAthlete(id: string, data: UpdateAthleteData): Promise<ApiResponse<Athlete>> {
    return apiClient.put<Athlete>(`/athletes/${id}`, data);
  }

  // Activate athlete
  static async activateAthlete(id: string, teamAssignments?: TeamAssignment[]): Promise<ApiResponse<any>> {
    return apiClient.put<any>(`/athletes/${id}/activate`, { teamAssignments });
  }

  // Deactivate athlete
  static async deactivateAthlete(id: string): Promise<ApiResponse<any>> {
    return apiClient.put<any>(`/athletes/${id}/deactivate`);
  }

  // Delete athlete
  static async deleteAthlete(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/athletes/${id}`);
  }

  // Download athlete template
  static async downloadAthleteTemplate(locale: string = 'en'): Promise<void> {
    return apiClient.downloadFile(`/athletes/template?locale=${locale}`, 'athlete-template.xlsx');
  }

  // Bulk import athletes
  static async bulkImportAthletes(file: File, locale: string = 'en'): Promise<ApiResponse<any>> {
    return apiClient.uploadFile<any>(`/athletes/bulk-import?locale=${locale}`, file, 'file');
  }
}

// Export individual functions for backward compatibility
export const getAthletes = AthletesApi.getAthletes;
export const getAthletesPaginated = AthletesApi.getAthletesPaginated;
export const getAthleteById = AthletesApi.getAthleteById;
export const getOverdueAthletes = AthletesApi.getOverdueAthletes;
export const getAthleteActivationData = AthletesApi.getAthleteActivationData;
export const createAthlete = AthletesApi.createAthlete;
export const createAthleteWithTeamAssignments = AthletesApi.createAthleteWithTeamAssignments;
export const createAthleteWithTeamAssignmentsAndBalance = AthletesApi.createAthleteWithBalance;
export const updateAthlete = AthletesApi.updateAthlete;
export const activateAthlete = AthletesApi.activateAthlete;
export const deactivateAthlete = AthletesApi.deactivateAthlete;
export const deleteAthlete = AthletesApi.deleteAthlete;
export const downloadAthleteTemplate = AthletesApi.downloadAthleteTemplate;
export const bulkImportAthletes = AthletesApi.bulkImportAthletes;
