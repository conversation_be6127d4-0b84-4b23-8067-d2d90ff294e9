import { apiClient, ApiResponse } from './client';

export interface ItemSale {
  id: string;
  itemId: string;
  athleteId: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  saleDate: string;
  paymentMethod: 'cash' | 'credit_card' | 'debit_card';
}

export interface CreateItemSaleData {
  itemId: string;
  athleteId: string;
  quantity: number;
  unitPrice: number;
  paymentMethod: 'cash' | 'credit_card' | 'debit_card';
}

export class ItemSalesApi {
  static async createItemSale(data: CreateItemSaleData): Promise<ApiResponse<ItemSale>> {
    return apiClient.post<ItemSale>('/item-sales', data);
  }

  static async getItemSales(): Promise<ApiResponse<ItemSale[]>> {
    return apiClient.get<ItemSale[]>('/item-sales');
  }

  static async getItemSaleById(id: string): Promise<ApiResponse<ItemSale>> {
    return apiClient.get<ItemSale>(`/item-sales/${id}`);
  }

  static async updateItemSale(id: string, data: Partial<CreateItemSaleData>): Promise<ApiResponse<ItemSale>> {
    return apiClient.put<ItemSale>(`/item-sales/${id}`, data);
  }

  static async deleteItemSale(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/item-sales/${id}`);
  }
}

export const createItemSale = ItemSalesApi.createItemSale;
export const getItemSales = ItemSalesApi.getItemSales;
export const getItemSaleById = ItemSalesApi.getItemSaleById;
export const updateItemSale = ItemSalesApi.updateItemSale;
export const deleteItemSale = ItemSalesApi.deleteItemSale;
