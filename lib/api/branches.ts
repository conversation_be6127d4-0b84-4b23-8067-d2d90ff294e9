import { apiClient, ApiResponse } from './client';

export interface Branch {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  isActive: boolean;
}

export interface CreateBranchData {
  name: string;
  address?: string;
  phone?: string;
  email?: string;
}

export interface UpdateBranchData {
  name?: string;
  address?: string;
  phone?: string;
  email?: string;
  isActive?: boolean;
}

export class BranchesApi {
  static async getBranches(): Promise<ApiResponse<Branch[]>> {
    return apiClient.get<Branch[]>('/branches');
  }

  static async getBranchById(id: string): Promise<ApiResponse<Branch>> {
    return apiClient.get<Branch>(`/branches/${id}`);
  }

  static async createBranch(data: CreateBranchData): Promise<ApiResponse<Branch>> {
    return apiClient.post<Branch>('/branches', data);
  }

  static async updateBranch(id: string, data: UpdateBranchData): Promise<ApiResponse<Branch>> {
    return apiClient.put<Branch>(`/branches/${id}`, data);
  }

  static async deleteBranch(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/branches/${id}`);
  }
}

export const getBranches = BranchesApi.getBranches;
export const getBranchById = BranchesApi.getBranchById;
export const createBranch = BranchesApi.createBranch;
export const updateBranch = BranchesApi.updateBranch;
export const deleteBranch = BranchesApi.deleteBranch;
