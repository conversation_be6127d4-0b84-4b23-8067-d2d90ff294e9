import { apiClient, ApiResponse } from './client';

export interface PaymentTransaction {
  id: string;
  paymentId: string;
  athleteId: string;
  amount: number;
  transactionDate: string;
  method: 'cash' | 'credit_card' | 'debit_card' | 'iban_transfer';
  description?: string;
  reference?: string;
}

export interface CreatePaymentTransactionData {
  paymentId: string;
  athleteId: string;
  amount: number;
  transactionDate: string;
  method: 'cash' | 'credit_card' | 'debit_card' | 'iban_transfer';
  description?: string;
  reference?: string;
}

export interface UpdatePaymentTransactionData {
  amount?: number;
  transactionDate?: string;
  method?: 'cash' | 'credit_card' | 'debit_card' | 'iban_transfer';
  description?: string;
  reference?: string;
}

export class PaymentTransactionsApi {
  static async getPaymentTransactions(): Promise<ApiResponse<PaymentTransaction[]>> {
    return apiClient.get<PaymentTransaction[]>('/payment-transactions');
  }

  static async getPaymentTransactionById(id: string): Promise<ApiResponse<PaymentTransaction>> {
    return apiClient.get<PaymentTransaction>(`/payment-transactions/${id}`);
  }

  static async createPaymentTransaction(data: CreatePaymentTransactionData): Promise<ApiResponse<PaymentTransaction>> {
    return apiClient.post<PaymentTransaction>('/payment-transactions', data);
  }

  static async updatePaymentTransaction(id: string, data: UpdatePaymentTransactionData): Promise<ApiResponse<PaymentTransaction>> {
    return apiClient.put<PaymentTransaction>(`/payment-transactions/${id}`, data);
  }

  static async deletePaymentTransaction(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/payment-transactions/${id}`);
  }
}

export const getPaymentTransactions = PaymentTransactionsApi.getPaymentTransactions;
export const getPaymentTransactionById = PaymentTransactionsApi.getPaymentTransactionById;
export const createPaymentTransaction = PaymentTransactionsApi.createPaymentTransaction;
export const updatePaymentTransaction = PaymentTransactionsApi.updatePaymentTransaction;
export const deletePaymentTransaction = PaymentTransactionsApi.deletePaymentTransaction;
