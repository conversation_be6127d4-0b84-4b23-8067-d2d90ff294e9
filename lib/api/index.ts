// Export the main API client
export { apiClient, ApiClient } from './client';
export type { ApiResponse, PaginatedResponse } from './client';

// Export all API services
export * from './athletes';
export * from './schools';
export * from './teams';
export * from './instructors';
export * from './payments';
export * from './payment-transactions';
export * from './expenses';
export * from './facilities';
export * from './items';
export * from './sms';
export * from './branches';
export * from './payment-plans';
export * from './athlete-team-management';
export * from './item-sales';

// Export API classes for advanced usage
export { AthletesApi } from './athletes';
export { SchoolsApi } from './schools';
export { TeamsApi } from './teams';
export { InstructorsApi } from './instructors';
export { PaymentsApi } from './payments';
export { PaymentTransactionsApi } from './payment-transactions';
export { ExpensesApi } from './expenses';
export { FacilitiesApi } from './facilities';
export { ItemsApi } from './items';
export { SmsApi } from './sms';

// Export types for all domains
export type {
  Athlete,
  CreateAthleteData,
  UpdateAthleteData,
  TeamAssignment,
  CreateAthleteWithTeamAssignmentsData,
  CreateAthleteWithBalanceData,
  GetAthletesPaginatedParams,
} from './athletes';

export type {
  School,
  CreateSchoolData,
  UpdateSchoolData,
} from './schools';

export type {
  Team,
  CreateTeamData,
  UpdateTeamData,
  GetTeamsPaginatedParams,
} from './teams';

export type {
  Instructor,
  CreateInstructorData,
  UpdateInstructorData,
  GetInstructorsPaginatedParams,
} from './instructors';

export type {
  Payment,
  CreatePaymentData,
  UpdatePaymentData,
  GetPaymentsPaginatedParams,
} from './payments';

export type {
  PaymentTransaction,
  CreatePaymentTransactionData,
  UpdatePaymentTransactionData,
} from './payment-transactions';

export type {
  Expense,
  CreateExpenseData,
  UpdateExpenseData,
} from './expenses';

export type {
  Facility,
  CreateFacilityData,
  UpdateFacilityData,
} from './facilities';

export type {
  Item,
  CreateItemData,
  UpdateItemData,
} from './items';

export type {
  SmsConfiguration,
  CreateSmsConfigurationData,
  UpdateSmsConfigurationData,
  SendPaymentReminderSmsData,
  SendTemplateSmsData,
  SmsLog,
  SmsBalance,
} from './sms';
