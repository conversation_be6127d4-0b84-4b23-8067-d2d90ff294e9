import { apiClient, ApiResponse } from './client';

export interface Expense {
  id: string;
  description: string;
  amount: number;
  date: string;
  category?: string;
  paymentMethod?: 'cash' | 'bank_transfer' | 'credit_card';
  receipt?: string;
}

export interface CreateExpenseData {
  description: string;
  amount: number;
  date: string;
  category?: string;
  paymentMethod?: 'cash' | 'bank_transfer' | 'credit_card';
  receipt?: string;
}

export interface UpdateExpenseData {
  description?: string;
  amount?: number;
  date?: string;
  category?: string;
  paymentMethod?: 'cash' | 'bank_transfer' | 'credit_card';
  receipt?: string;
}

export class ExpensesApi {
  // Get all expenses
  static async getExpenses(): Promise<ApiResponse<Expense[]>> {
    return apiClient.get<Expense[]>('/expenses');
  }

  // Get expense by ID
  static async getExpenseById(id: string): Promise<ApiResponse<Expense>> {
    return apiClient.get<Expense>(`/expenses/${id}`);
  }

  // Create expense
  static async createExpense(data: CreateExpenseData): Promise<ApiResponse<Expense>> {
    return apiClient.post<Expense>('/expenses', data);
  }

  // Update expense
  static async updateExpense(id: string, data: UpdateExpenseData): Promise<ApiResponse<Expense>> {
    return apiClient.put<Expense>(`/expenses/${id}`, data);
  }

  // Delete expense
  static async deleteExpense(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/expenses/${id}`);
  }
}

// Export individual functions for backward compatibility
export const getExpenses = ExpensesApi.getExpenses;
export const getExpenseById = ExpensesApi.getExpenseById;
export const createExpense = ExpensesApi.createExpense;
export const updateExpense = ExpensesApi.updateExpense;
export const deleteExpense = ExpensesApi.deleteExpense;
